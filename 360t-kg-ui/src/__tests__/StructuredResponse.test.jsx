import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import StructuredResponse from '../components/chat/StructuredResponse';

// Mock FormatCRenderer since StructuredResponse now only uses it
jest.mock('../components/chat/FormatCRenderer', () => {
  return function MockFormatCRenderer({ response, onNodeSelect, onSendMessage, showDebugInfo }) {
    return (
      <div data-testid="format-c-renderer">
        <div data-testid="response-version">{response?.version || 'undefined'}</div>
        <div data-testid="response-format">{response?.format || 'undefined'}</div>
        <div data-testid="show-debug-info">{String(showDebugInfo)}</div>
        {response?.core?.summary && (
          <div data-testid="format-c-summary">{response.core.summary}</div>
        )}
        {response?.answer && (
          <div data-testid="legacy-answer">{response.answer}</div>
        )}
        <div data-testid="props-passed">
          onNodeSelect: {typeof onNodeSelect}, onSendMessage: {typeof onSendMessage}
        </div>
      </div>
    );
  };
});

describe('StructuredResponse', () => {
  const mockOnNodeSelect = jest.fn();
  const mockOnSendMessage = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders Format C response using FormatCRenderer', () => {
    const formatCResponse = {
      version: '3.0',
      format: 'C',
      core: {
        summary: 'This is a Format C response',
        results: [],
        follow_up_questions: []
      }
    };

    render(
      <StructuredResponse
        response={formatCResponse}
        onNodeSelect={mockOnNodeSelect}
        onSendMessage={mockOnSendMessage}
        showDebugInfo={true}
      />
    );

    // Should always render FormatCRenderer
    expect(screen.getByTestId('format-c-renderer')).toBeInTheDocument();
    expect(screen.getByTestId('response-version')).toHaveTextContent('3.0');
    expect(screen.getByTestId('response-format')).toHaveTextContent('C');
    expect(screen.getByTestId('show-debug-info')).toHaveTextContent('true');
    expect(screen.getByTestId('format-c-summary')).toHaveTextContent('This is a Format C response');
  });

  test('renders legacy v2.0 response using FormatCRenderer', () => {
    const legacyResponse = {
      version: '2.0',
      answer: 'This is a legacy response',
      sections: [],
      sources: [],
      entities: []
    };

    render(
      <StructuredResponse
        response={legacyResponse}
        onNodeSelect={mockOnNodeSelect}
        onSendMessage={mockOnSendMessage}
        showDebugInfo={false}
      />
    );

    // Should still render FormatCRenderer (which will handle legacy format internally)
    expect(screen.getByTestId('format-c-renderer')).toBeInTheDocument();
    expect(screen.getByTestId('response-version')).toHaveTextContent('2.0');
    expect(screen.getByTestId('response-format')).toHaveTextContent('undefined');
    expect(screen.getByTestId('show-debug-info')).toHaveTextContent('false');
    expect(screen.getByTestId('legacy-answer')).toHaveTextContent('This is a legacy response');
  });

  test('passes all props correctly to FormatCRenderer', () => {
    const testResponse = {
      version: '3.0',
      format: 'C',
      core: { summary: 'Test' }
    };

    render(
      <StructuredResponse
        response={testResponse}
        onNodeSelect={mockOnNodeSelect}
        onSendMessage={mockOnSendMessage}
        showDebugInfo={true}
      />
    );

    expect(screen.getByTestId('format-c-renderer')).toBeInTheDocument();
    expect(screen.getByTestId('props-passed')).toHaveTextContent(
      'onNodeSelect: function, onSendMessage: function'
    );
  });

  test('handles null response gracefully', () => {
    render(
      <StructuredResponse
        response={null}
        onNodeSelect={mockOnNodeSelect}
        onSendMessage={mockOnSendMessage}
        showDebugInfo={true}
      />
    );

    expect(screen.getByTestId('format-c-renderer')).toBeInTheDocument();
    expect(screen.getByTestId('response-version')).toHaveTextContent('undefined');
  });

  test('handles empty response gracefully', () => {
    render(
      <StructuredResponse
        response={{}}
        onNodeSelect={mockOnNodeSelect}
        onSendMessage={mockOnSendMessage}
        showDebugInfo={false}
      />
    );

    expect(screen.getByTestId('format-c-renderer')).toBeInTheDocument();
    expect(screen.getByTestId('show-debug-info')).toHaveTextContent('false');
  });

  test('uses default showDebugInfo when not provided', () => {
    const testResponse = {
      version: '3.0',
      format: 'C',
      core: { summary: 'Test' }
    };

    render(
      <StructuredResponse
        response={testResponse}
        onNodeSelect={mockOnNodeSelect}
        onSendMessage={mockOnSendMessage}
        // showDebugInfo not provided - should default to true
      />
    );

    expect(screen.getByTestId('format-c-renderer')).toBeInTheDocument();
    expect(screen.getByTestId('show-debug-info')).toHaveTextContent('true');
  });

  test('handles invalid response format', () => {
    const invalidResponse = {
      version: '1.0',
      data: 'Invalid format'
    };

    render(
      <StructuredResponse
        response={invalidResponse}
        onNodeSelect={mockOnNodeSelect}
        onSendMessage={mockOnSendMessage}
        showDebugInfo={true}
      />
    );

    // Should still render FormatCRenderer - it will handle the invalid format internally
    expect(screen.getByTestId('format-c-renderer')).toBeInTheDocument();
    expect(screen.getByTestId('response-version')).toHaveTextContent('1.0');
  });
});