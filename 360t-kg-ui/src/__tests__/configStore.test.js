import configStore, { useConfigStore } from '../stores/configStore';
import { DEFAULT_GRAPHITI_CONFIG, DEFAULT_ATLAS_RAG_CONFIG } from '../../../config/search-config/dist/index.js';

describe('configStore', () => {
  beforeEach(() => {
    configStore.setConfig(DEFAULT_GRAPHITI_CONFIG);
    if (typeof window !== 'undefined' && window.localStorage) {
      window.localStorage.clear();
    }
  });

  it('initializes with graphiti defaults', () => {
    const config = configStore.getConfig();
    expect(config.mode).toBe('graphiti');
    expect(config.graphiti.edgeCount).toBe(DEFAULT_GRAPHITI_CONFIG.graphiti.edgeCount);
  });

  it('switches to atlasrag mode', () => {
    configStore.setMode('atlasrag');
    const config = configStore.getConfig();
    expect(config.mode).toBe('atlasrag');
    expect(config.atlasrag.topN).toBe(DEFAULT_ATLAS_RAG_CONFIG.atlasrag.topN);
  });

  it('updates atlasrag settings', () => {
    configStore.setMode('atlasrag');
    configStore.updateAtlasRag({ topN: 5 });
    const config = configStore.getConfig();
    expect(config.atlasrag.topN).toBe(5);
  });
});
