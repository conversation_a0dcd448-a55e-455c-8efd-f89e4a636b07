import React from 'react';
import { render, screen, act } from '@testing-library/react';
import configStore, { useConfigStore } from '../stores/configStore';

const ModeViewer = () => {
  const mode = useConfigStore((state) => state.config.mode);
  return <span data-testid="mode-label">{mode}</span>;
};

describe('configStore React integration', () => {
  beforeEach(() => {
    configStore.setMode('graphiti');
  });

  it('reflects store updates in React components', () => {
    render(<ModeViewer />);
    expect(screen.getByTestId('mode-label').textContent).toBe('graphiti');

    act(() => {
      configStore.setMode('atlasrag');
    });

    expect(screen.getByTestId('mode-label').textContent).toBe('atlasrag');
  });
});
