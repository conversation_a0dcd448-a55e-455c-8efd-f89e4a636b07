/**
 * Atlas RAG Parameter Flow Tests
 * Tests to ensure frontend properly structures Atlas RAG parameters for backend
 */

import { 
  createAtlasRAGParameterStructure, 
  validateAtlasRAGParameterStructure, 
  testAtlasRAGSettingsFlow 
} from '../utils/atlasRAGTestUtils';

describe('Atlas RAG Parameter Flow', () => {
  describe('createAtlasRAGParameterStructure', () => {
    test('creates correct structure with default settings', () => {
      const structure = createAtlasRAGParameterStructure();
      
      expect(structure).toHaveProperty('searchType', 'Atlas RAG');
      expect(structure).toHaveProperty('useAtlasRAG', true);
      expect(structure).toHaveProperty('llmProvider', 'ollama');
      expect(structure).toHaveProperty('ollamaUrl', 'http://localhost:11434');
      expect(structure).toHaveProperty('atlasRAGSettings');
      
      // Check Atlas RAG settings structure
      const atlasSettings = structure.atlasRAGSettings;
      expect(atlasSettings).toHaveProperty('topN', 10);
      expect(atlasSettings).toHaveProperty('damping_factor', 0.85);
      expect(atlasSettings).toHaveProperty('ollama_model', 'llama3.2');
      expect(atlasSettings).toHaveProperty('temperature', 0.7);
      expect(atlasSettings).toHaveProperty('max_tokens', 2048);
    });

    test('merges user settings with defaults correctly', () => {
      const userSettings = {
        topN: 15,
        temperature: 0.5,
        custom_field: 'test'
      };
      
      const structure = createAtlasRAGParameterStructure(userSettings);
      
      expect(structure.atlasRAGSettings.topN).toBe(15);
      expect(structure.atlasRAGSettings.temperature).toBe(0.5);
      expect(structure.atlasRAGSettings.custom_field).toBe('test');
      // Should preserve defaults for non-overridden fields
      expect(structure.atlasRAGSettings.damping_factor).toBe(0.85);
      expect(structure.atlasRAGSettings.ollama_model).toBe('llama3.2');
    });
  });

  describe('validateAtlasRAGParameterStructure', () => {
    test('validates correct structure successfully', () => {
      const validStructure = createAtlasRAGParameterStructure();
      const validation = validateAtlasRAGParameterStructure(validStructure);
      
      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);
      expect(validation.structure.hasRequiredTopLevel).toBe(true);
      expect(validation.structure.hasRequiredAtlasSettings).toBe(true);
    });

    test('detects missing required fields', () => {
      const invalidStructure = {
        searchType: 'Atlas RAG',
        useAtlasRAG: true,
        // Missing llmProvider and ollamaUrl
        atlasRAGSettings: {
          // Missing topN, damping_factor, ollama_model
        }
      };
      
      const validation = validateAtlasRAGParameterStructure(invalidStructure);
      
      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('llmProvider is required for backend compatibility');
      expect(validation.errors).toContain('ollamaUrl is required for backend compatibility');
      expect(validation.errors).toContain('Atlas RAG required field missing: topN');
      expect(validation.errors).toContain('Atlas RAG required field missing: damping_factor');
      expect(validation.errors).toContain('Atlas RAG required field missing: ollama_model');
    });

    test('validates field types and ranges', () => {
      const invalidStructure = createAtlasRAGParameterStructure({
        topN: -5, // Invalid range
        damping_factor: 1.5, // Invalid range  
        temperature: 3.0, // Warning range
        ollama_model: 123 // Invalid type
      });
      
      const validation = validateAtlasRAGParameterStructure(invalidStructure);
      
      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('topN must be between 1 and 100');
      expect(validation.errors).toContain('damping_factor must be between 0 and 1');
      expect(validation.errors).toContain('ollama_model must be a string');
      expect(validation.warnings).toContain('temperature outside typical range 0-1');
    });
  });

  describe('testAtlasRAGSettingsFlow', () => {
    test('complete flow test with default settings', () => {
      // Suppress console logs during test
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      const result = testAtlasRAGSettingsFlow();
      
      expect(result.success).toBe(true);
      expect(result.summary.wouldPassBackend).toBe(true);
      expect(result.summary.hasAllRequiredFields).toBe(true);
      expect(result.parametersToSend.searchType).toBe('Atlas RAG');
      expect(result.parametersToSend.useAtlasRAG).toBe(true);
      
      consoleSpy.mockRestore();
    });

    test('complete flow test with custom settings', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      const customSettings = {
        topN: 20,
        damping_factor: 0.9,
        temperature: 0.3,
        ollama_model: 'custom-model:latest'
      };
      
      const result = testAtlasRAGSettingsFlow(customSettings);
      
      expect(result.success).toBe(true);
      expect(result.parametersToSend.atlasRAGSettings.topN).toBe(20);
      expect(result.parametersToSend.atlasRAGSettings.damping_factor).toBe(0.9);
      expect(result.parametersToSend.atlasRAGSettings.temperature).toBe(0.3);
      expect(result.parametersToSend.atlasRAGSettings.ollama_model).toBe('custom-model:latest');
      
      consoleSpy.mockRestore();
    });

    test('flow test with invalid settings fails appropriately', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      const invalidSettings = {
        topN: 'invalid', // Wrong type
        damping_factor: -1, // Invalid range
        ollama_model: null // Invalid value
      };
      
      const result = testAtlasRAGSettingsFlow(invalidSettings);
      
      expect(result.success).toBe(false);
      expect(result.summary.wouldPassBackend).toBe(false);
      expect(result.validation.errors.length).toBeGreaterThan(0);
      
      consoleSpy.mockRestore();
    });
  });

  describe('Backend compatibility validation', () => {
    test('ensures all required backend fields are present', () => {
      const structure = createAtlasRAGParameterStructure();
      
      // These fields are required for backend compatibility
      expect(structure).toHaveProperty('searchType');
      expect(structure).toHaveProperty('useAtlasRAG');
      expect(structure).toHaveProperty('llmProvider');
      expect(structure).toHaveProperty('ollamaUrl');
      expect(structure).toHaveProperty('atlasRAGSettings');
      
      // Check that Atlas RAG settings has all required fields
      const requiredAtlasFields = ['topN', 'damping_factor', 'ollama_model'];
      requiredAtlasFields.forEach(field => {
        expect(structure.atlasRAGSettings).toHaveProperty(field);
      });
    });

    test('parameter names match backend expectations', () => {
      const structure = createAtlasRAGParameterStructure();
      const atlasSettings = structure.atlasRAGSettings;
      
      // Ensure parameter names match what the backend validator expects
      expect(atlasSettings).toHaveProperty('topN'); // Not top_n
      expect(atlasSettings).toHaveProperty('damping_factor'); // With underscore
      expect(atlasSettings).toHaveProperty('ollama_model'); // With underscore
      expect(atlasSettings).toHaveProperty('max_tokens'); // With underscore
      expect(atlasSettings).toHaveProperty('system_prompt_template'); // With underscores
    });
  });
});