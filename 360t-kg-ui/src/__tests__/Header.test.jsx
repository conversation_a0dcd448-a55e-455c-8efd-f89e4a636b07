import React from 'react';
import { fireEvent, screen } from '@testing-library/react';
import { renderWithProviders } from './utils/renderWithProviders';
import Header from '../components/Header';

/**
 * Unit tests for the Header component to verify the Dashboard button has been
 * removed and navigation events still function as expected.
 */
describe('Header navigation', () => {
  const setup = (view = 'explorer') => {
    const onSwitchView = jest.fn();
    renderWithProviders(
      <Header currentView={view} onSwitchView={onSwitchView} />
    );
    return { onSwitchView };
  };

  it('does not render a Dashboard nav button', () => {
    setup();
    const dashboardButton = screen.queryByRole('button', { name: /dashboard/i });
    expect(dashboardButton).toBeNull();
  });

  it('renders expected navigation buttons', () => {
    setup();
    // Analysis button has been removed as part of Phase 5 analysis functionality cleanup
    ['Explorer', 'Chat'].forEach(label => {
      expect(
        screen.getByRole('button', { name: new RegExp(label, 'i') })
      ).toBeInTheDocument();
    });
    
    // Verify Analysis button is NOT present
    const analysisButton = screen.queryByRole('button', { name: /analysis/i });
    expect(analysisButton).toBeNull();
    
    // Note: Documentation is accessible via help button, not as main nav
    const helpButton = screen.getByLabelText(/help & documentation/i);
    expect(helpButton).toBeInTheDocument();
  });

  it('calls onSwitchView when Explorer button is clicked', () => {
    const { onSwitchView } = setup();
    const explorerBtn = screen.getByRole('button', { name: /explorer/i });
    fireEvent.click(explorerBtn);
    expect(onSwitchView).toHaveBeenCalledWith('explorer');
  });
}); 