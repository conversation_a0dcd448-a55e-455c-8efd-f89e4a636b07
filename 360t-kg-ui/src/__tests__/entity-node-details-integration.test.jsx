/**
 * Integration test for Entity Click to NodeDetails workflow
 * Tests the complete flow from StructuredResponse entity click to NodeDetails display
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import StructuredResponse from '../components/chat/StructuredResponse';

// Mock all child components except the ones we're testing
jest.mock('../components/MarkdownRenderer', () => {
  return function MockMarkdownRenderer({ content }) {
    return <div data-testid="markdown-renderer">{content}</div>;
  };
});

jest.mock('../components/chat/SourceTooltip', () => {
  return function MockSourceTooltip({ source }) {
    return <div data-testid="source-tooltip">{source.title}</div>;
  };
});

jest.mock('../components/chat/EntityBadge', () => {
  return function MockEntityBadge({ label, count }) {
    return <div data-testid="entity-badge">{label} ({count})</div>;
  };
});

jest.mock('../components/chat/FollowUpCards', () => {
  return function MockFollowUpCards({ questions, onQuestionClick }) {
    return (
      <div data-testid="follow-up-cards">
        {questions.map((question, index) => (
          <button
            key={index}
            onClick={() => onQuestionClick(question.question || question)}
            data-testid={`follow-up-${index}`}
          >
            {question.question || question}
          </button>
        ))}
      </div>
    );
  };
});

jest.mock('../components/chat/ThinkingSection', () => {
  return function MockThinkingSection({ thinkingContent }) {
    return <div data-testid="thinking-section">{thinkingContent}</div>;
  };
});

jest.mock('../components/chat/AnswerWithReferences', () => {
  return function MockAnswerWithReferences({ content }) {
    return <div data-testid="answer-with-references">{content}</div>;
  };
});

// Use the real EntityList component to test the click handler
jest.unmock('../components/chat/EntityList');

describe('Entity Click to NodeDetails Integration', () => {
  const mockResponse = {
    version: '2.0',
    answer: 'This is a test answer about trading systems.',
    sections: [],
    sources: [],
    entities: [
      {
        id: 'trading-system-001',
        name: 'Trading Risk Engine',
        description: 'Core trading risk management system',
        category: 'TRADING',
        relevance_score: 0.95,
        properties: { 
          type: 'system',
          version: '2.1'
        }
      },
      {
        id: 'ems-gateway-002', 
        name: 'EMS Gateway',
        description: 'Energy management system gateway',
        category: 'EMS',
        relevance_score: 0.87,
        properties: {
          type: 'gateway',
          protocol: 'REST'
        }
      }
    ],
    badges: [],
    follow_up: [],
    metadata: {
      confidence_score: 0.92,
      processing_time_ms: 1200
    }
  };

  test('should call onNodeSelect with correct entity data when entity is clicked', () => {
    const mockOnNodeSelect = jest.fn();
    const mockOnSendMessage = jest.fn();

    render(
      <StructuredResponse
        response={mockResponse}
        onNodeSelect={mockOnNodeSelect}
        onSendMessage={mockOnSendMessage}
        showDebugInfo={false}
      />
    );

    // Find the first entity in the list
    const firstEntity = screen.getByRole('button', { name: /view details for trading risk engine/i });
    expect(firstEntity).toBeInTheDocument();

    // Click the entity
    fireEvent.click(firstEntity);

    // Verify onNodeSelect was called with the correctly converted entity data
    expect(mockOnNodeSelect).toHaveBeenCalledTimes(1);
    expect(mockOnNodeSelect).toHaveBeenCalledWith({
      id: 'trading-system-001',
      name: 'Trading Risk Engine', 
      description: 'Core trading risk management system',
      category: 'TRADING',
      properties: {
        type: 'system',
        version: '2.1'
      },
      relevance_score: 0.95
    });
  });

  test('should handle multiple entity clicks correctly', () => {
    const mockOnNodeSelect = jest.fn();
    const mockOnSendMessage = jest.fn();

    render(
      <StructuredResponse
        response={mockResponse}
        onNodeSelect={mockOnNodeSelect}
        onSendMessage={mockOnSendMessage}
        showDebugInfo={false}
      />
    );

    // Click first entity
    const firstEntity = screen.getByRole('button', { name: /view details for trading risk engine/i });
    fireEvent.click(firstEntity);

    // Click second entity  
    const secondEntity = screen.getByRole('button', { name: /view details for ems gateway/i });
    fireEvent.click(secondEntity);

    // Verify both calls were made with correct data
    expect(mockOnNodeSelect).toHaveBeenCalledTimes(2);
    
    expect(mockOnNodeSelect).toHaveBeenNthCalledWith(1, {
      id: 'trading-system-001',
      name: 'Trading Risk Engine',
      description: 'Core trading risk management system', 
      category: 'TRADING',
      properties: {
        type: 'system',
        version: '2.1'
      },
      relevance_score: 0.95
    });

    expect(mockOnNodeSelect).toHaveBeenNthCalledWith(2, {
      id: 'ems-gateway-002',
      name: 'EMS Gateway',
      description: 'Energy management system gateway',
      category: 'EMS', 
      properties: {
        type: 'gateway',
        protocol: 'REST'
      },
      relevance_score: 0.87
    });
  });

  test('should handle entity without description or properties', () => {
    const mockOnNodeSelect = jest.fn();
    const mockOnSendMessage = jest.fn();

    const minimalResponse = {
      ...mockResponse,
      entities: [
        {
          id: 'minimal-entity',
          name: 'Minimal Entity',
          relevance_score: 0.5
        }
      ]
    };

    render(
      <StructuredResponse
        response={minimalResponse}
        onNodeSelect={mockOnNodeSelect}
        onSendMessage={mockOnSendMessage}
        showDebugInfo={false}
      />
    );

    // Find and click the minimal entity
    const minimalEntity = screen.getByRole('button', { name: /view details for minimal entity/i });
    fireEvent.click(minimalEntity);

    // Verify the call was made with correct minimal data
    expect(mockOnNodeSelect).toHaveBeenCalledWith({
      id: 'minimal-entity',
      name: 'Minimal Entity',
      properties: {},
      relevance_score: 0.5
    });
  });

  test('should work with keyboard navigation', () => {
    const mockOnNodeSelect = jest.fn();
    const mockOnSendMessage = jest.fn();

    render(
      <StructuredResponse
        response={mockResponse}
        onNodeSelect={mockOnNodeSelect}
        onSendMessage={mockOnSendMessage}
        showDebugInfo={false}
      />
    );

    // Find first entity and simulate Enter key press
    const firstEntity = screen.getByRole('button', { name: /view details for trading risk engine/i });
    fireEvent.keyDown(firstEntity, { key: 'Enter' });

    // Verify onNodeSelect was called
    expect(mockOnNodeSelect).toHaveBeenCalledTimes(1);

    // Test Space key as well
    mockOnNodeSelect.mockClear();
    fireEvent.keyDown(firstEntity, { key: ' ' });
    expect(mockOnNodeSelect).toHaveBeenCalledTimes(1);
  });
});