import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import {
  DEFAULT_GRAPHITI_CONFIG,
  DEFAULT_ATLAS_RAG_CONFIG
} from '../../../config/search-config/dist/index.js';

const STORAGE_KEY = 'kg-visualizer-unified-config';
const LEGACY_KEYS = ['kg-visualizer-search-config'];

const clone = (value) => JSON.parse(JSON.stringify(value));

const normalizeGraphiti = (config = {}) => {
  const base = clone(DEFAULT_GRAPHITI_CONFIG);
  const graphitiOverrides = config.graphiti ?? {};

  return {
    ...base,
    ...config,
    mode: 'graphiti',
    graphiti: {
      ...base.graphiti,
      ...graphitiOverrides
    }
  };
};

const normalizeAtlasRag = (config = {}) => {
  const base = clone(DEFAULT_ATLAS_RAG_CONFIG);
  const atlasOverrides = config.atlasrag ?? {};

  return {
    ...base,
    ...config,
    mode: 'atlasrag',
    atlasrag: {
      ...base.atlasrag,
      ...atlasOverrides
    }
  };
};

const normalizeConfig = (config = {}) => {
  if (config.mode === 'atlasrag') {
    return normalizeAtlasRag(config);
  }
  return normalizeGraphiti(config);
};

const readLegacyConfig = () => {
  if (typeof window === 'undefined') return null;

  for (const key of LEGACY_KEYS) {
    const raw = window.localStorage.getItem(key);
    if (!raw) continue;

    try {
      const parsed = JSON.parse(raw);
      return normalizeConfig(parsed);
    } catch (error) {
      console.warn('configStore: failed to parse legacy config key', key, error);
    }
  }

  return null;
};

const bootstrapConfig = () => {
  if (typeof window === 'undefined') return clone(DEFAULT_GRAPHITI_CONFIG);

  const legacy = readLegacyConfig();
  if (legacy) {
    try {
      window.localStorage.setItem(
        STORAGE_KEY,
        JSON.stringify({ state: { config: legacy }, version: 1 })
      );
      LEGACY_KEYS.forEach((key) => window.localStorage.removeItem(key));
    } catch (error) {
      console.warn('configStore: failed to migrate legacy config', error);
    }
    return legacy;
  }

  return clone(DEFAULT_GRAPHITI_CONFIG);
};

export const useConfigStore = create(
  persist(
    (set, get) => ({
      config: bootstrapConfig(),
      setConfig: (config) => set({ config: normalizeConfig(config) }),
      setMode: (mode) => {
        const current = get().config;
        const next = mode === 'atlasrag'
          ? normalizeAtlasRag({ ...current, mode: 'atlasrag' })
          : normalizeGraphiti({ ...current, mode: 'graphiti' });
        set({ config: next });
      },
      updateGraphiti: (partial) => {
        const current = get().config;
        const next = normalizeGraphiti({
          ...(current.mode === 'graphiti' ? current : {}),
          graphiti: {
            ...(current.mode === 'graphiti' ? current.graphiti : {}),
            ...partial
          }
        });
        set({ config: next });
      },
      updateAtlasRag: (partial) => {
        const current = get().config;
        const next = normalizeAtlasRag({
          ...(current.mode === 'atlasrag' ? current : {}),
          atlasrag: {
            ...(current.mode === 'atlasrag' ? current.atlasrag : {}),
            ...partial
          }
        });
        set({ config: next });
      }
    }),
    {
      name: STORAGE_KEY,
      version: 1,
      storage: typeof window !== 'undefined'
        ? createJSONStorage(() => window.localStorage)
        : undefined,
      partialize: (state) => ({ config: state.config })
    }
  )
);

const validateAtlasRagConfig = (config) => {
  const normalized = normalizeConfig(config);
  if (normalized.mode !== 'atlasrag') {
    return { valid: false, issues: ['Configuration mode must be atlasrag'] };
  }

  const issues = [];
  const atlas = normalized.atlasrag || {};

  if (atlas.topN < 1 || atlas.topN > 100) issues.push('topN must be between 1 and 100');
  if (atlas.damping_factor < 0 || atlas.damping_factor > 1) issues.push('damping_factor must be between 0 and 1');
  if (atlas.max_hops < 1 || atlas.max_hops > 10) issues.push('max_hops must be between 1 and 10');
  if (atlas.temperature < 0 || atlas.temperature > 1) issues.push('temperature must be between 0 and 1');

  if (!normalized.llmProvider) issues.push('llmProvider is required');
  if (!normalized.ollamaUrl && normalized.llmProvider === 'ollama') {
    issues.push('ollamaUrl is required for Ollama provider');
  }

  return { valid: issues.length === 0, issues };
};

const api = {
  getConfig: () => normalizeConfig(useConfigStore.getState().config),
  setConfig: (config) => useConfigStore.getState().setConfig(config),
  setMode: (mode) => useConfigStore.getState().setMode(mode),
  updateGraphiti: (partial) => useConfigStore.getState().updateGraphiti(partial),
  updateAtlasRag: (partial) => useConfigStore.getState().updateAtlasRag(partial),
  validateAtlasRAGConfig: (config) => validateAtlasRagConfig(config),
  subscribe: useConfigStore.subscribe
};

export default api;
