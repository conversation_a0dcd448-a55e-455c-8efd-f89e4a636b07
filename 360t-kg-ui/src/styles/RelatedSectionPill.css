/* RelatedSectionPill.css - Styling for collapsible related section pill */

.related-pill-container {
  position: absolute;
  bottom: -18px;
  left: 28px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
  z-index: 30; /* Keep pill above answer card content */
}

.related-pill {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 999px;
  border: 1px solid rgba(148, 163, 184, 0.35);
  background: rgba(255, 255, 255, 0.92);
  box-shadow: 0 6px 18px rgba(15, 23, 42, 0.12);
  color: #1f2937;
  font-size: 11px;
  font-weight: 600;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  transition: transform 0.18s ease, box-shadow 0.18s ease, border-color 0.18s ease;
  cursor: pointer;
  outline: none;
  pointer-events: auto;
}

.related-pill:hover:not(.disabled) {
  transform: translateY(-1px);
  box-shadow: 0 10px 26px rgba(15, 23, 42, 0.16);
  border-color: rgba(37, 99, 235, 0.45);
}

.related-pill:focus-visible:not(.disabled) {
  border-color: rgba(37, 99, 235, 0.6);
  box-shadow: 0 10px 26px rgba(15, 23, 42, 0.16), 0 0 0 2px rgba(37, 99, 235, 0.2);
}

.related-pill.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.related-pill__icon {
  font-size: 12px;
  line-height: 1;
}

.related-pill__text {
  color: #1f2937;
  font-weight: 500;
  text-transform: none;
  letter-spacing: 0;
  white-space: nowrap;
}

.related-pill__chevron {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 999px;
  background: rgba(37, 99, 235, 0.12);
  color: #2563eb;
  transition: transform 0.2s ease;
  flex-shrink: 0;
}

.related-pill__chevron.open {
  transform: rotate(180deg);
}

/* Popover Styling */
.related-popover {
  position: absolute;
  z-index: 60; /* Ensure popover stacks above other UI overlays */
  left: 0;
  min-width: 520px;
  max-width: 640px;
  background: rgba(255, 255, 255, 0.97);
  backdrop-filter: blur(14px);
  border-radius: 18px;
  border: 1px solid rgba(148, 163, 184, 0.25);
  box-shadow: 0 16px 40px rgba(15, 23, 42, 0.24);
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  pointer-events: auto;
  animation: fadeIn 0.2s ease-out;
}

.related-popover.pop-above {
  bottom: calc(100% + 10px);
  top: auto;
  transform-origin: bottom left;
}

.related-popover.pop-below {
  top: calc(100% + 10px);
  bottom: auto;
  transform-origin: top left;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Popover Header */
.related-popover__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(148, 163, 184, 0.2);
}

.related-popover__title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.related-popover__toggle {
  background: rgba(37, 99, 235, 0.12);
  border: none;
  border-radius: 999px;
  font-size: 10px;
  font-weight: 600;
  letter-spacing: 0.04em;
  text-transform: uppercase;
  color: #2563eb;
  padding: 4px 10px;
  cursor: pointer;
  transition: background 0.2s ease, color 0.2s ease;
  outline: none;
}

.related-popover__toggle:hover {
  background: rgba(37, 99, 235, 0.2);
}

.related-popover__toggle:focus-visible {
  background: rgba(37, 99, 235, 0.2);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.3);
}

/* Popover Content */
.related-popover__content {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 4px;
}

/* Custom scrollbar for the content area */
.related-popover__content::-webkit-scrollbar {
  width: 6px;
}

.related-popover__content::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
  border-radius: 3px;
}

.related-popover__content::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 3px;
}

.related-popover__content::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
  .related-pill-container {
    bottom: -12px;
    left: 16px;
  }

  .related-pill {
    font-size: 10px;
    padding: 5px 10px;
    gap: 6px;
  }

  .related-pill__icon {
    font-size: 11px;
  }

  .related-pill__chevron {
    width: 20px;
    height: 20px;
  }

  .related-popover {
    min-width: min(520px, calc(100vw - 40px));
    max-width: calc(100vw - 40px);
    max-height: calc(100vh - 120px);
  }

  .related-popover__content {
    max-height: 300px;
  }

  .related-popover__title {
    font-size: 13px;
  }

  .related-popover__toggle {
    font-size: 9px;
    padding: 3px 8px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .related-pill {
    border-color: #000;
    background: #fff;
    color: #000;
  }

  .related-pill__chevron {
    background: #000;
    color: #fff;
  }

  .related-popover {
    border-color: #000;
    background: #fff;
    backdrop-filter: none;
  }

  .related-popover__header {
    border-bottom-color: #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .related-pill {
    transition: none;
  }

  .related-pill__chevron {
    transition: none;
  }

  .related-popover {
    animation: none;
  }

  .related-pill:hover:not(.disabled) {
    transform: none;
  }
}

/* Dark mode support (if implemented in the future) */
@media (prefers-color-scheme: dark) {
  .related-pill {
    background: rgba(17, 24, 39, 0.92);
    border-color: rgba(75, 85, 99, 0.5);
    color: #f9fafb;
  }

  .related-pill__text {
    color: #f9fafb;
  }

  .related-popover {
    background: rgba(17, 24, 39, 0.97);
    border-color: rgba(75, 85, 99, 0.5);
  }

  .related-popover__title {
    color: #f9fafb;
  }

  .related-popover__header {
    border-bottom-color: rgba(75, 85, 99, 0.3);
  }
}
