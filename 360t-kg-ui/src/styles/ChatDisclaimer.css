.chat-disclaimer {
  display: flex !important; /* Force display to prevent accidental hiding */
  align-items: flex-start;
  gap: 12px;
  padding: 10px 12px;
  border-radius: 8px;
  border: 1px solid transparent;
  margin: 8px 12px 12px 12px;
  /* Ensure disclaimer is positioned above messages but below header and floating controls */
  position: sticky;
  top: 88px;
  z-index: 50; /* Higher than messages (10) but lower than floating controls (100) */
  width: calc(100% - 24px); /* Account for horizontal margins */
  box-sizing: border-box;
  /* Ensure the disclaimer is always visible and properly spaced */
  flex-shrink: 0;
  /* Defensive styling to prevent accidental hiding */
  visibility: visible !important;
  opacity: 1 !important;
  height: auto !important;
  overflow: visible !important;
  transform: none !important;
}

.chat-disclaimer.info {
  background: #e8f1fd; /* light blue */
  border-color: #b6d4fe;
  color: #0b3d91; /* dark blue for 4.5:1 contrast */
}

.chat-disclaimer.warning {
  background: #fff4e5; /* soft amber */
  border-color: #ffd7a8;
  color: #7a3e00; /* dark amber-brown for contrast */
}

.chat-disclaimer-icon {
  flex: 0 0 auto;
  line-height: 0;
  color: currentColor;
  margin-top: 2px;
}

.chat-disclaimer-content {
  flex: 1 1 auto;
}

.chat-disclaimer-text {
  margin: 0;
  font-size: 0.92rem;
  line-height: 1.35rem;
}

.chat-disclaimer-actions {
  flex: 0 0 auto;
  display: flex;
  gap: 8px;
}

.chat-disclaimer-btn {
  background: transparent;
  border: 1px solid currentColor;
  color: inherit;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.85rem;
  cursor: pointer;
}

.chat-disclaimer-btn.strong {
  font-weight: 600;
}

.chat-disclaimer-btn:focus {
  outline: 2px solid #0a58ca; /* visible focus */
  outline-offset: 2px;
}

/* Mobile responsive styles for disclaimer */
@media (max-width: 768px) {
  .chat-disclaimer {
    margin: 6px 8px 10px 8px;
    padding: 8px 10px;
    width: calc(100% - 16px); /* Adjust for smaller margins */
    top: 76px;
  }

  .chat-disclaimer-text {
    font-size: 0.88rem;
    line-height: 1.3rem;
  }

  .chat-disclaimer-actions {
    gap: 6px;
  }

  .chat-disclaimer-btn {
    padding: 3px 6px;
    font-size: 0.8rem;
  }
}

/* Very small screens */
@media (max-width: 480px) {
  .chat-disclaimer {
    flex-direction: column;
    gap: 8px;
    margin: 4px 6px 8px 6px;
    padding: 8px;
    width: calc(100% - 12px);
    top: 72px;
  }

  .chat-disclaimer-actions {
    align-self: flex-end;
    margin-top: 4px;
  }
}

/* High contrast mode consideration */
@media (prefers-contrast: more) {
  .chat-disclaimer {
    border-width: 2px;
  }
  .chat-disclaimer-btn {
    border-width: 2px;
  }
}
