.chat-category-inline {
  position: absolute;
  top: -18px;
  right: 28px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 10px;
  z-index: 5;
}

.chat-category-pill {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 999px;
  border: 1px solid rgba(148, 163, 184, 0.35);
  background: rgba(255, 255, 255, 0.92);
  box-shadow: 0 6px 18px rgba(15, 23, 42, 0.12);
  color: #1f2937;
  font-size: 11px;
  font-weight: 600;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  transition: transform 0.18s ease, box-shadow 0.18s ease, border-color 0.18s ease;
  pointer-events: auto;
}

.chat-category-pill:hover:not(.disabled) {
  transform: translateY(-1px);
  box-shadow: 0 10px 26px rgba(15, 23, 42, 0.16);
  border-color: rgba(37, 99, 235, 0.45);
}

.chat-category-pill.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  box-shadow: none;
}

.chat-category-pill__label {
  color: #475467;
}

.chat-category-pill__summary {
  color: #1f2937;
  font-weight: 500;
  text-transform: none;
  letter-spacing: 0;
}

.chat-category-pill__chevron {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 999px;
  background: rgba(37, 99, 235, 0.12);
  color: #2563eb;
  transition: transform 0.2s ease;
}

.chat-category-pill__chevron.open {
  transform: rotate(180deg);
}

.chat-category-popover {
  position: absolute;
  z-index: 20;
  right: 0;
  min-width: 520px;
  max-width: 640px;
  background: rgba(255, 255, 255, 0.97);
  backdrop-filter: blur(14px);
  border-radius: 18px;
  border: 1px solid rgba(148, 163, 184, 0.25);
  box-shadow: 0 16px 40px rgba(15, 23, 42, 0.24);
  padding: 14px 16px 16px 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  pointer-events: auto;
}

.chat-category-popover.pop-above {
  bottom: calc(100% + 10px);
  top: auto;
  transform-origin: bottom right;
}

.chat-category-popover.pop-below {
  top: calc(100% + 10px);
  bottom: auto;
  transform-origin: top right;
}

.chat-category-popover__actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.chat-category-filter__action {
  background: rgba(37, 99, 235, 0.12);
  border: none;
  border-radius: 999px;
  font-size: 10px;
  font-weight: 600;
  letter-spacing: 0.04em;
  text-transform: uppercase;
  color: #2563eb;
  padding: 4px 10px;
  cursor: pointer;
  transition: background 0.2s ease, color 0.2s ease;
}

.chat-category-filter__action:hover:not(:disabled) {
  background: rgba(37, 99, 235, 0.2);
}

.chat-category-filter__action:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.chat-category-popover__chips {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  max-height: 180px;
  overflow-y: auto;
  padding-right: 4px;
}

.chat-category-chip {
  border-radius: 999px;
  border: 1px solid transparent;
  padding: 3px 9px;
  font-size: 10px;
  font-weight: 600;
  letter-spacing: 0.04em;
  text-transform: uppercase;
  background: transparent;
  cursor: pointer;
  transition: transform 0.18s ease, box-shadow 0.18s ease, background 0.18s ease, color 0.18s ease;
}

.chat-category-chip.selected {
  box-shadow: 0 6px 16px rgba(15, 23, 42, 0.18);
}

.chat-category-chip.unselected {
  background: transparent !important;
  opacity: 0.65;
}

.chat-category-chip:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.chat-category-chip:not(:disabled):hover {
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 8px 24px rgba(15, 23, 42, 0.12);
}

.chat-category-chip__label {
  white-space: nowrap;
}

@media (max-width: 768px) {
  .chat-category-inline {
    top: -12px;
    right: 16px;
  }

  .chat-category-pill {
    font-size: 10px;
    padding: 5px 10px;
  }

  .chat-category-popover__chips {
    max-height: 160px;
  }

  .chat-category-popover {
    min-width: min(520px, calc(100vw - 40px));
    max-width: calc(100vw - 40px);
  }
}
