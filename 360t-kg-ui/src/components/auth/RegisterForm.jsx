import React, { useState, useEffect, useRef } from 'react';
import useAuthStore from '../../stores/authStore';
import '../../styles/auth.css';

// SVG Icons for better accessibility (replacing emoji)
const EyeIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" aria-hidden="true">
    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
    <circle cx="12" cy="12" r="3"/>
  </svg>
);

const EyeOffIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" aria-hidden="true">
    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94"/>
    <path d="M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19"/>
    <path d="M14.12 14.12a3 3 0 1 1-4.24-4.24"/>
    <path d="M1 1l22 22"/>
  </svg>
);

const WarningIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" aria-hidden="true">
    <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
    <line x1="12" y1="9" x2="12" y2="13"/>
    <circle cx="12" cy="17" r="1"/>
  </svg>
);

const RegisterForm = ({ onToggleMode, onClose }) => {
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const firstInputRef = useRef(null);

  const { register, isLoading, error, clearError } = useAuthStore();

  // Auto-focus first input when component mounts
  useEffect(() => {
    if (firstInputRef.current) {
      // Small delay to ensure modal transition is complete
      const timeoutId = setTimeout(() => {
        firstInputRef.current.focus();
      }, 150);
      
      return () => clearTimeout(timeoutId);
    }
  }, []);

  // Clear errors when component mounts or form data changes
  useEffect(() => {
    clearError();
  }, [clearError]);

  useEffect(() => {
    if (Object.values(formData).some(value => value)) {
      clearError();
      setFormErrors({});
    }
  }, [formData, clearError]);

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePassword = (password) => {
    // Minimum 8 characters, at least one letter and one number
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/;
    return passwordRegex.test(password);
  };

  const validateForm = () => {
    const errors = {};

    // Username validation - more actionable error messages
    if (!formData.username.trim()) {
      errors.username = 'Please enter a username';
    } else if (formData.username.length < 3) {
      errors.username = 'Username must be at least 3 characters long';
    } else if (formData.username.length > 30) {
      errors.username = 'Username must be 30 characters or less';
    } else if (!/^[a-zA-Z0-9_-]+$/.test(formData.username)) {
      errors.username = 'Username can only contain letters, numbers, hyphens (-), and underscores (_)';
    }

    // Email validation - more actionable error message
    if (!formData.email.trim()) {
      errors.email = 'Please enter your email address';
    } else if (!validateEmail(formData.email)) {
      errors.email = 'Please enter a valid email address (e.g., <EMAIL>)';
    }

    // Password validation - more actionable error message
    if (!formData.password) {
      errors.password = 'Please create a password';
    } else if (!validatePassword(formData.password)) {
      errors.password = 'Password must be at least 8 characters with at least one letter and one number';
    }

    // Confirm password validation - more actionable error messages
    if (!formData.confirmPassword) {
      errors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match. Please re-enter your password';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear specific field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }

    // Clear confirm password error if password is being changed and they match
    if (name === 'password' && formData.confirmPassword && value === formData.confirmPassword) {
      setFormErrors(prev => ({
        ...prev,
        confirmPassword: ''
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      const result = await register({
        username: formData.username,
        email: formData.email,
        password: formData.password
      });
      
      if (result.success) {
        // Close modal on successful registration
        onClose?.();
      }
      // Error handling is managed by the auth store
    } catch (err) {
      console.error('Registration submission error:', err);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !isLoading) {
      handleSubmit(e);
    }
  };

  const getPasswordStrength = () => {
    const password = formData.password;
    if (!password) return { strength: 0, label: '' };
    
    let strength = 0;
    const checks = [
      password.length >= 8,
      /[a-z]/.test(password),
      /[A-Z]/.test(password),
      /\d/.test(password),
      /[@$!%*#?&]/.test(password)
    ];
    
    strength = checks.filter(Boolean).length;
    
    if (strength <= 2) return { strength, label: 'Weak', color: '#ff4444' };
    if (strength <= 3) return { strength, label: 'Fair', color: '#ffaa00' };
    if (strength <= 4) return { strength, label: 'Good', color: '#44aa44' };
    return { strength, label: 'Strong', color: '#00aa00' };
  };

  const passwordStrength = getPasswordStrength();

  return (
    <div className="auth-form">
      <div className="auth-header">
        <h2>Create Account</h2>
        <p>Join the Knowledge Graph Visualizer community</p>
      </div>

      <form onSubmit={handleSubmit} className="auth-form-content">
        {/* Global Error Message */}
        {error && (
          <div className="auth-error-message" role="alert">
            <span className="error-icon"><WarningIcon /></span>
            {error}
          </div>
        )}

        {/* Username Field */}
        <div className="form-group">
          <label htmlFor="register-username" className="form-label">
            Username
          </label>
          <input
            ref={firstInputRef}
            id="register-username"
            name="username"
            type="text"
            value={formData.username}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            className={`form-input ${formErrors.username ? 'error' : ''}`}
            placeholder="Choose a username"
            disabled={isLoading}
            autoComplete="username"
            inputMode="text"
            aria-invalid={!!formErrors.username}
            aria-describedby={formErrors.username ? 'username-error' : undefined}
          />
          {formErrors.username && (
            <span id="username-error" className="field-error" role="alert">
              {formErrors.username}
            </span>
          )}
        </div>

        {/* Email Field */}
        <div className="form-group">
          <label htmlFor="register-email" className="form-label">
            Email
          </label>
          <input
            id="register-email"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            className={`form-input ${formErrors.email ? 'error' : ''}`}
            placeholder="Enter your email"
            disabled={isLoading}
            autoComplete="email"
            inputMode="email"
            aria-invalid={!!formErrors.email}
            aria-describedby={formErrors.email ? 'email-error' : undefined}
          />
          {formErrors.email && (
            <span id="email-error" className="field-error" role="alert">
              {formErrors.email}
            </span>
          )}
        </div>

        {/* Password Field */}
        <div className="form-group">
          <label htmlFor="register-password" className="form-label">
            Password
          </label>
          <div className="password-input-container">
            <input
              id="register-password"
              name="password"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
              className={`form-input ${formErrors.password ? 'error' : ''}`}
              placeholder="Create a password"
              disabled={isLoading}
              autoComplete="new-password"
              aria-invalid={!!formErrors.password}
              aria-describedby={formErrors.password ? 'password-error' : 'password-strength'}
            />
            <button
              type="button"
              className="password-toggle"
              onClick={() => setShowPassword(!showPassword)}
              disabled={isLoading}
              aria-label={showPassword ? 'Hide password' : 'Show password'}
              tabIndex="0"
            >
              {showPassword ? <EyeOffIcon /> : <EyeIcon />}
            </button>
          </div>
          
          {/* Password Strength Indicator */}
          {formData.password && (
            <div id="password-strength" className="password-strength">
              <div 
                className="password-strength-bar" 
                style={{ 
                  width: `${(passwordStrength.strength / 5) * 100}%`,
                  backgroundColor: passwordStrength.color 
                }}
              ></div>
              <span 
                className="password-strength-label"
                style={{ color: passwordStrength.color }}
              >
                {passwordStrength.label}
              </span>
            </div>
          )}
          
          {formErrors.password && (
            <span id="password-error" className="field-error" role="alert">
              {formErrors.password}
            </span>
          )}
        </div>

        {/* Confirm Password Field */}
        <div className="form-group">
          <label htmlFor="register-confirm-password" className="form-label">
            Confirm Password
          </label>
          <div className="password-input-container">
            <input
              id="register-confirm-password"
              name="confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              value={formData.confirmPassword}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
              className={`form-input ${formErrors.confirmPassword ? 'error' : ''}`}
              placeholder="Confirm your password"
              disabled={isLoading}
              autoComplete="new-password"
              aria-invalid={!!formErrors.confirmPassword}
              aria-describedby={formErrors.confirmPassword ? 'confirm-password-error' : undefined}
            />
            <button
              type="button"
              className="password-toggle"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              disabled={isLoading}
              aria-label={showConfirmPassword ? 'Hide password confirmation' : 'Show password confirmation'}
              tabIndex="0"
            >
              {showConfirmPassword ? <EyeOffIcon /> : <EyeIcon />}
            </button>
          </div>
          {formErrors.confirmPassword && (
            <span id="confirm-password-error" className="field-error" role="alert">
              {formErrors.confirmPassword}
            </span>
          )}
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          className={`auth-submit-btn ${isLoading ? 'loading' : ''}`}
          disabled={isLoading}
          aria-describedby="register-status"
        >
          {isLoading ? (
            <>
              <span className="loading-spinner"></span>
              Creating Account...
            </>
          ) : (
            'Create Account'
          )}
        </button>

        {/* Toggle to Login */}
        <div className="auth-toggle">
          <p>
            Already have an account?{' '}
            <button
              type="button"
              className="auth-toggle-btn"
              onClick={onToggleMode}
              disabled={isLoading}
            >
              Sign in here
            </button>
          </p>
        </div>
      </form>

      {/* Screen reader status */}
      <div id="register-status" className="sr-only" aria-live="polite">
        {isLoading ? 'Creating account...' : ''}
        {error ? `Registration error: ${error}` : ''}
      </div>
    </div>
  );
};

export default RegisterForm;