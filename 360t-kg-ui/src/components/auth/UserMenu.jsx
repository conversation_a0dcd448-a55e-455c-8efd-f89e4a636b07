import React, { useState, useRef, useEffect } from 'react';
import useAuthStore from '../../stores/authStore';
import settingsService from '../../services/settingsService';
import ProfileModal from './ProfileModal';
import Avatar from '../Avatar';
import { DEFAULT_AVATAR_ID } from '../../constants/avatarIcons';
import '../../styles/auth.css';

const UserMenu = ({ onShowAuthModal }) => {
  // Use a selector to ensure proper re-renders on user changes
  const user = useAuthStore((state) => state.user);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const logout = useAuthStore((state) => state.logout);
  const isLoading = useAuthStore((state) => state.isLoading);
  
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const menuRef = useRef(null);
  const buttonRef = useRef(null);
  
  // Debug logging for avatar updates
  useEffect(() => {
    console.log('👤 UserMenu - User state updated:', user);
    console.log('  - Avatar ID:', user?.preferences?.selectedAvatar);
    console.log('  - Display name:', user?.display_name || user?.username);
    console.log('  - Full preferences:', user?.preferences);
  }, [user]);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        menuRef.current && 
        !menuRef.current.contains(event.target) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target)
      ) {
        setIsMenuOpen(false);
      }
    };

    if (isMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMenuOpen]);

  // Close menu on escape key
  useEffect(() => {
    const handleEscape = (event) => {
      if (event.key === 'Escape' && isMenuOpen) {
        setIsMenuOpen(false);
        buttonRef.current?.focus();
      }
    };

    if (isMenuOpen) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isMenuOpen]);

  const handleLogout = async () => {
    setIsMenuOpen(false);
    await logout();
  };

  const handleShowAuth = (mode = 'login') => {
    setIsMenuOpen(false);
    onShowAuthModal?.(mode);
  };

  const handleShowProfile = () => {
    setIsMenuOpen(false);
    setIsProfileModalOpen(true);
  };

  const handleCloseProfile = () => {
    setIsProfileModalOpen(false);
  };

  const getUserInitials = (user) => {
    if (!user) return '?';
    
    if (user.username) {
      return user.username.charAt(0).toUpperCase();
    }
    
    if (user.email) {
      return user.email.charAt(0).toUpperCase();
    }
    
    return '?';
  };

  const getUserDisplayName = (user) => {
    if (!user) return 'Anonymous';
    return user.username || user.email?.split('@')[0] || 'User';
  };

  if (!isAuthenticated) {
    return (
      <div className="user-menu-container">
        <button
          className="auth-trigger-btn user-menu-auth-btn"
          onClick={() => handleShowAuth('login')}
          disabled={isLoading}
        >
          {isLoading ? 'Loading...' : 'Sign In'}
        </button>
      </div>
    );
  }

  // Fallback to locally persisted avatar if backend user preferences are not present
  const selectedAvatarId = user?.preferences?.selectedAvatar || settingsService.getSelectedAvatar();

  return (
    <div className="user-menu-container">
      <button
        ref={buttonRef}
        className="user-menu-trigger"
        onClick={() => setIsMenuOpen(!isMenuOpen)}
        aria-expanded={isMenuOpen}
        aria-haspopup="true"
        aria-label={`User menu for ${getUserDisplayName(user)}`}
        title={`Signed in as ${getUserDisplayName(user)}`}
      >
        <Avatar
          avatarId={selectedAvatarId || DEFAULT_AVATAR_ID}
          size="sm"
          theme="primary"
          alt={`${getUserDisplayName(user)}'s avatar`}
        />
        <span className="user-menu-chevron">
          <svg
            width="12"
            height="12"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            style={{ transform: isMenuOpen ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'transform 0.2s ease' }}
          >
            <path d="M6 9l6 6 6-6" />
          </svg>
        </span>
      </button>

      {isMenuOpen && (
        <div ref={menuRef} className="user-menu-dropdown" role="menu">
          {/* User Info Section */}
          <div className="user-menu-header" role="none">
            <Avatar
              avatarId={selectedAvatarId || DEFAULT_AVATAR_ID}
              size="md"
              theme="primary"
              alt={`${getUserDisplayName(user)}'s avatar`}
            />
            <div className="user-menu-info">
              <div className="user-menu-name">
                {getUserDisplayName(user)}
              </div>
              {user?.email && (
                <div className="user-menu-email">
                  {user.email}
                </div>
              )}
            </div>
          </div>

          <div className="user-menu-divider" role="none"></div>

          {/* Menu Items */}
          <div className="user-menu-items">
            <button
              className="user-menu-item"
              role="menuitem"
              onClick={handleShowProfile}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
                <circle cx="12" cy="7" r="4" />
              </svg>
              <span>Profile</span>
            </button>

            <button
              className="user-menu-item"
              role="menuitem"
              onClick={() => {
                setIsMenuOpen(false);
                // TODO: Implement settings
                console.log('User settings not implemented yet');
              }}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="3" />
                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" />
              </svg>
              <span>Settings</span>
            </button>

            <div className="user-menu-divider" role="none"></div>

            <button
              className="user-menu-item user-menu-logout"
              role="menuitem"
              onClick={handleLogout}
              disabled={isLoading}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
                <polyline points="16,17 21,12 16,7" />
                <line x1="21" y1="12" x2="9" y2="12" />
              </svg>
              <span>{isLoading ? 'Signing out...' : 'Sign out'}</span>
            </button>
          </div>
        </div>
      )}

      {/* Profile Modal */}
      <ProfileModal
        isOpen={isProfileModalOpen}
        onClose={handleCloseProfile}
      />
    </div>
  );
};

export default UserMenu;
