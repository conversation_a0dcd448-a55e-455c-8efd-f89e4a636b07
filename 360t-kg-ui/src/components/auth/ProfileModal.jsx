/**
 * ProfileModal Component for 360T Knowledge Graph Visualizer
 * 
 * A comprehensive user profile management modal that integrates the professional
 * avatar system with user profile functionality. Provides an intuitive interface
 * for avatar selection, profile editing, and preferences management.
 * 
 * Features:
 * - Professional avatar selection with visual preview
 * - Profile information management (display name, email)
 * - Integration with auth store and backend API
 * - Full accessibility support with focus management
 * - Mobile-responsive design
 * - Form validation and error handling
 * - Unsaved changes warning
 * - 360T design system consistency
 * 
 * @module ProfileModal
 * @version 1.0.0
 * <AUTHOR> Knowledge Graph Visualizer Team
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import PropTypes from 'prop-types';
import useAuthStore from '../../stores/authStore';
import Avatar from '../Avatar';
import settingsService from '../../services/settingsService';
import AvatarSelector from '../AvatarSelector';
import { DEFAULT_AVATAR_ID, getAvatarIcon } from '../../constants/avatarIcons';
import '../../styles/auth.css';
import '../../styles/avatars.css';

const ProfileModal = ({ isOpen, onClose }) => {
  // Use selectors to ensure proper re-renders
  const user = useAuthStore((state) => state.user);
  const updateProfile = useAuthStore((state) => state.updateProfile);
  const isLoading = useAuthStore((state) => state.isLoading);
  const error = useAuthStore((state) => state.error);
  const clearError = useAuthStore((state) => state.clearError);
  
  const modalRef = useRef(null);
  const previousFocusRef = useRef(null);
  const formRef = useRef(null);
  
  // Form state
  const [formData, setFormData] = useState({
    display_name: '',
    email: '',
    selectedAvatar: DEFAULT_AVATAR_ID
  });
  
  // UI state
  const [isDirty, setIsDirty] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [showUnsavedWarning, setShowUnsavedWarning] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Initialize form data from user
  useEffect(() => {
    if (user && isOpen) {
      const initialData = {
        display_name: user.display_name || user.username || '',
        email: user.email || '',
        selectedAvatar: user.preferences?.selectedAvatar || DEFAULT_AVATAR_ID
      };
      setFormData(initialData);
      setIsDirty(false);
      setValidationErrors({});
      clearError();
    }
  }, [user, isOpen, clearError]);
  
  // Focus management for accessibility
  useEffect(() => {
    if (isOpen) {
      // Store previously focused element
      previousFocusRef.current = document.activeElement;
      
      // Focus the modal after a brief delay
      const focusTimer = setTimeout(() => {
        if (modalRef.current) {
          const firstFocusable = modalRef.current.querySelector(
            'input, button, select, textarea, [tabindex]:not([tabindex="-1"])'
          );
          if (firstFocusable) {
            firstFocusable.focus();
          } else {
            modalRef.current.focus();
          }
        }
      }, 100);

      return () => clearTimeout(focusTimer);
    } else {
      // Restore focus to previously focused element when modal closes
      if (previousFocusRef.current && typeof previousFocusRef.current.focus === 'function') {
        previousFocusRef.current.focus();
      }
    }
  }, [isOpen]);

  // Handle escape key and prevent body scroll
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isOpen) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Form validation
  const validateForm = useCallback((data) => {
    const errors = {};
    
    if (!data.display_name?.trim()) {
      errors.display_name = 'Display name is required';
    } else if (data.display_name.trim().length < 2) {
      errors.display_name = 'Display name must be at least 2 characters';
    } else if (data.display_name.trim().length > 50) {
      errors.display_name = 'Display name must be less than 50 characters';
    }
    
    // Email is typically read-only from auth provider, but validate if editable
    if (data.email && !data.email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      errors.email = 'Please enter a valid email address';
    }
    
    return errors;
  }, []);

  // Handle input changes
  const handleInputChange = useCallback((e) => {
    const { name, value } = e.target;
    
    setFormData(prev => {
      const newData = { ...prev, [name]: value };
      
      // Check if form is dirty
      const originalData = {
        display_name: user?.display_name || user?.username || '',
        email: user?.email || '',
        selectedAvatar: user?.preferences?.selectedAvatar || DEFAULT_AVATAR_ID
      };
      
      const hasChanges = Object.keys(newData).some(
        key => newData[key] !== originalData[key]
      );
      
      setIsDirty(hasChanges);
      
      // Clear validation errors for this field
      if (validationErrors[name]) {
        setValidationErrors(prev => {
          const updated = { ...prev };
          delete updated[name];
          return updated;
        });
      }
      
      return newData;
    });
  }, [user, validationErrors]);

  // Handle avatar selection
  const handleAvatarSelect = useCallback((avatarId) => {
    console.log('🎨 ProfileModal.handleAvatarSelect called with:', avatarId);
    console.log('  - Current user avatar:', user?.preferences?.selectedAvatar);
    
    setFormData(prev => {
      const newData = { ...prev, selectedAvatar: avatarId };
      
      // Check if form is dirty
      const originalAvatar = user?.preferences?.selectedAvatar || DEFAULT_AVATAR_ID;
      const hasChanges = avatarId !== originalAvatar || 
                        prev.display_name !== (user?.display_name || user?.username || '') ||
                        prev.email !== (user?.email || '');
      
      console.log('  - Form is dirty:', hasChanges);
      console.log('  - Original avatar:', originalAvatar);
      console.log('  - New avatar:', avatarId);
      
      setIsDirty(hasChanges);
      return newData;
    });
  }, [user]);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form
    const errors = validateForm(formData);
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      
      // Focus first error field
      const firstErrorField = Object.keys(errors)[0];
      const fieldElement = formRef.current?.querySelector(`[name="${firstErrorField}"]`);
      if (fieldElement) {
        fieldElement.focus();
      }
      return;
    }
    
    setIsSubmitting(true);
    setValidationErrors({});
    
    try {
      // Prepare profile data for API
      const profileData = {
        display_name: formData.display_name.trim(),
        preferences: {
          selectedAvatar: formData.selectedAvatar,
          // Preserve other existing preferences
          ...(user?.preferences || {})
        }
      };
      
      // Only include email if it's different (and if API supports email updates)
      if (formData.email !== user?.email && formData.email.trim()) {
        profileData.email = formData.email.trim();
      }
      
      console.log('📤 ProfileModal.handleSubmit - Calling updateProfile with:', profileData);
      const result = await updateProfile(profileData);
      
      console.log('📥 ProfileModal.handleSubmit - Result:', result);
      if (result.success) {
        console.log('✅ Profile update successful, closing modal');
        // Mirror avatar selection into local settings for persistence fallback
        try {
          if (formData.selectedAvatar) {
            await settingsService.setSelectedAvatar(formData.selectedAvatar);
          }
        } catch (e) {
          console.warn('Failed to mirror avatar to local settings:', e);
        }
        setIsDirty(false);
        onClose();
        
        // Announce success to screen readers
        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', 'polite');
        announcement.className = 'sr-only';
        announcement.textContent = 'Profile updated successfully';
        document.body.appendChild(announcement);
        setTimeout(() => document.body.removeChild(announcement), 1000);
      }
    } catch (error) {
      console.error('Profile update failed:', error);
      // Error is handled by the auth store
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle close with unsaved changes warning
  const handleClose = useCallback(() => {
    if (isDirty && !showUnsavedWarning) {
      setShowUnsavedWarning(true);
      return;
    }
    
    setShowUnsavedWarning(false);
    setIsDirty(false);
    setValidationErrors({});
    clearError();
    onClose();
  }, [isDirty, showUnsavedWarning, onClose, clearError]);

  // Handle backdrop click
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  // Focus trap functionality
  const handleKeyDown = (e) => {
    if (e.key === 'Tab') {
      const focusableElements = modalRef.current?.querySelectorAll(
        'button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      
      if (focusableElements && focusableElements.length > 0) {
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey) {
          // Shift + Tab
          if (document.activeElement === firstElement) {
            lastElement.focus();
            e.preventDefault();
          }
        } else {
          // Tab
          if (document.activeElement === lastElement) {
            firstElement.focus();
            e.preventDefault();
          }
        }
      }
    }
  };

  if (!isOpen) {
    return null;
  }

  const selectedAvatarConfig = getAvatarIcon(formData.selectedAvatar);

  return (
    <div 
      className="auth-modal-overlay" 
      onClick={handleBackdropClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="profile-modal-title"
      aria-describedby="profile-modal-description"
    >
      <div 
        ref={modalRef}
        className="auth-modal"
        tabIndex="-1"
        onKeyDown={handleKeyDown}
        style={{ maxWidth: '600px', width: '95%' }}
      >
        {/* Close Button */}
        <button
          className="auth-modal-close"
          onClick={handleClose}
          aria-label="Close profile modal"
          title="Close"
          disabled={isSubmitting}
        >
          <span aria-hidden="true">×</span>
        </button>

        {/* Modal Header */}
        <div className="auth-modal-content">
          <div className="auth-header">
            <h2 id="profile-modal-title">Edit Profile</h2>
            <p id="profile-modal-description">
              Update your profile information and select a professional avatar
            </p>
          </div>

          {/* Unsaved Changes Warning */}
          {showUnsavedWarning && (
            <div className="auth-error-message" style={{ marginBottom: 'var(--360t-space-4)' }}>
              <svg className="error-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                <line x1="12" y1="9" x2="12" y2="13"/>
                <line x1="12" y1="17" x2="12.01" y2="17"/>
              </svg>
              <div>
                <strong>Unsaved Changes</strong>
                <br />
                You have unsaved changes. Are you sure you want to close without saving?
                <div style={{ marginTop: 'var(--360t-space-2)', display: 'flex', gap: 'var(--360t-space-2)' }}>
                  <button
                    type="button"
                    className="auth-submit-btn"
                    style={{ fontSize: 'var(--360t-text-sm)', padding: 'var(--360t-space-2) var(--360t-space-3)', minHeight: 'auto' }}
                    onClick={() => setShowUnsavedWarning(false)}
                  >
                    Keep Editing
                  </button>
                  <button
                    type="button"
                    className="auth-toggle-btn"
                    style={{ fontSize: 'var(--360t-text-sm)', padding: 'var(--360t-space-2)' }}
                    onClick={onClose}
                  >
                    Discard Changes
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Profile Form */}
          <form ref={formRef} onSubmit={handleSubmit} className="auth-form">
            <div className="auth-form-content" style={{ gap: 'var(--360t-space-6)' }}>
              
              {/* Current Avatar Preview */}
              <div className="form-group">
                <label className="form-label">Current Avatar</label>
                <div style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: 'var(--360t-space-3)',
                  padding: 'var(--360t-space-4)',
                  backgroundColor: 'var(--360t-light-gray)',
                  borderRadius: 'var(--360t-radius-lg)',
                  border: '1px solid var(--360t-border)'
                }}>
                  <Avatar
                    avatarId={formData.selectedAvatar}
                    size="xl"
                    theme="primary"
                    showTooltip={false}
                  />
                  <div>
                    <div style={{ 
                      fontWeight: 'var(--360t-font-semibold)', 
                      color: 'var(--360t-text)', 
                      fontSize: 'var(--360t-text-sm)' 
                    }}>
                      {selectedAvatarConfig.name}
                    </div>
                    <div style={{ 
                      color: 'var(--360t-dark-gray)', 
                      fontSize: 'var(--360t-text-xs)', 
                      marginTop: '2px' 
                    }}>
                      {selectedAvatarConfig.professionalContext}
                    </div>
                  </div>
                </div>
              </div>

              {/* Avatar Selection */}
              <div className="form-group">
                <label className="form-label" id="avatar-selection-label">
                  Choose Your Professional Avatar
                </label>
                <p style={{ 
                  fontSize: 'var(--360t-text-sm)', 
                  color: 'var(--360t-dark-gray)', 
                  margin: '0 0 var(--360t-space-3) 0' 
                }}>
                  Select an avatar that represents your professional role
                </p>
                <AvatarSelector
                  selectedAvatarId={formData.selectedAvatar}
                  onAvatarSelect={handleAvatarSelect}
                  size="lg"
                  theme="primary"
                  showLabels={true}
                  disabled={isSubmitting}
                  ariaLabel="Select your professional avatar"
                />
              </div>

              {/* Personal Information */}
              <div style={{ 
                padding: 'var(--360t-space-4)',
                backgroundColor: 'var(--360t-light-gray)',
                borderRadius: 'var(--360t-radius-lg)',
                border: '1px solid var(--360t-border)'
              }}>
                <h3 style={{ 
                  fontSize: 'var(--360t-text-lg)', 
                  fontWeight: 'var(--360t-font-semibold)', 
                  color: 'var(--360t-text)',
                  margin: '0 0 var(--360t-space-4) 0'
                }}>
                  Personal Information
                </h3>

                {/* Display Name */}
                <div className="form-group">
                  <label htmlFor="display_name" className="form-label">
                    Display Name *
                  </label>
                  <input
                    id="display_name"
                    name="display_name"
                    type="text"
                    value={formData.display_name}
                    onChange={handleInputChange}
                    className={`form-input ${validationErrors.display_name ? 'error' : ''}`}
                    placeholder="Enter your display name"
                    disabled={isSubmitting}
                    maxLength={50}
                    aria-describedby="display_name-error"
                  />
                  {validationErrors.display_name && (
                    <span id="display_name-error" className="field-error" role="alert">
                      {validationErrors.display_name}
                    </span>
                  )}
                </div>

                {/* Email (typically read-only) */}
                <div className="form-group">
                  <label htmlFor="email" className="form-label">
                    Email Address
                  </label>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className={`form-input ${validationErrors.email ? 'error' : ''}`}
                    placeholder="<EMAIL>"
                    disabled={true} // Usually read-only from auth provider
                    aria-describedby="email-help email-error"
                  />
                  <p id="email-help" style={{
                    fontSize: 'var(--360t-text-xs)',
                    color: 'var(--360t-dark-gray)',
                    margin: 'var(--360t-space-1) 0 0 0'
                  }}>
                    Email address is managed by your authentication provider
                  </p>
                  {validationErrors.email && (
                    <span id="email-error" className="field-error" role="alert">
                      {validationErrors.email}
                    </span>
                  )}
                </div>
              </div>

              {/* API Error Display */}
              {error && (
                <div className="auth-error-message" role="alert">
                  <svg className="error-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <circle cx="12" cy="12" r="10"/>
                    <line x1="15" y1="9" x2="9" y2="15"/>
                    <line x1="9" y1="9" x2="15" y2="15"/>
                  </svg>
                  <span>{error}</span>
                </div>
              )}

              {/* Form Actions */}
              <div style={{ 
                display: 'flex', 
                gap: 'var(--360t-space-3)', 
                justifyContent: 'flex-end',
                paddingTop: 'var(--360t-space-4)',
                borderTop: '1px solid var(--360t-border)'
              }}>
                <button
                  type="button"
                  className="auth-toggle-btn"
                  onClick={handleClose}
                  disabled={isSubmitting}
                  style={{ 
                    padding: 'var(--360t-space-3) var(--360t-space-6)',
                    minHeight: '48px',
                    fontSize: 'var(--360t-text-base)'
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="auth-submit-btn"
                  disabled={isSubmitting || !isDirty}
                  style={{ minWidth: '120px' }}
                >
                  {isSubmitting ? (
                    <>
                      <div className="loading-spinner" />
                      <span>Saving...</span>
                    </>
                  ) : (
                    <span>Save Changes</span>
                  )}
                </button>
              </div>
            </div>
          </form>
        </div>

        {/* Modal Footer */}
        <div className="auth-modal-footer">
          <p className="auth-footer-text">
            Changes to your profile will be reflected throughout the application
          </p>
        </div>
      </div>
    </div>
  );
};

ProfileModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired
};

export default ProfileModal;
