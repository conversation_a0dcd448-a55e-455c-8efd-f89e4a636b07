import React, { useState, useEffect, useRef } from 'react';
import LoginForm from './LoginForm';
import RegisterForm from './RegisterForm';
import '../../styles/auth.css';

const AuthModal = ({ isOpen, onClose, initialMode = 'login' }) => {
  const [mode, setMode] = useState(initialMode);
  const modalRef = useRef(null);
  const previousFocusRef = useRef(null);

  // Focus management for accessibility
  useEffect(() => {
    if (isOpen) {
      // Store previously focused element
      previousFocusRef.current = document.activeElement;
      
      // Focus the modal after a brief delay
      const focusTimer = setTimeout(() => {
        if (modalRef.current) {
          modalRef.current.focus();
        }
      }, 100);

      return () => clearTimeout(focusTimer);
    } else {
      // Restore focus to previously focused element when modal closes
      if (previousFocusRef.current && typeof previousFocusRef.current.focus === 'function') {
        previousFocusRef.current.focus();
      }
    }
  }, [isOpen]);

  // Reset mode when modal opens
  useEffect(() => {
    if (isOpen) {
      setMode(initialMode);
    }
  }, [isOpen, initialMode]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // Handle backdrop click
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Focus trap functionality
  const handleKeyDown = (e) => {
    if (e.key === 'Tab') {
      const focusableElements = modalRef.current?.querySelectorAll(
        'button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      
      if (focusableElements && focusableElements.length > 0) {
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey) {
          // Shift + Tab
          if (document.activeElement === firstElement) {
            lastElement.focus();
            e.preventDefault();
          }
        } else {
          // Tab
          if (document.activeElement === lastElement) {
            firstElement.focus();
            e.preventDefault();
          }
        }
      }
    }
  };

  const toggleMode = () => {
    setMode(prev => prev === 'login' ? 'register' : 'login');
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div 
      className="auth-modal-overlay" 
      onClick={handleBackdropClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="auth-modal-title"
    >
      <div 
        ref={modalRef}
        className="auth-modal"
        tabIndex="-1"
        onKeyDown={handleKeyDown}
      >
        {/* Close Button */}
        <button
          className="auth-modal-close"
          onClick={onClose}
          aria-label="Close authentication modal"
          title="Close"
        >
          <span aria-hidden="true">×</span>
        </button>

        {/* Modal Header - Hidden but available to screen readers */}
        <h1 id="auth-modal-title" className="sr-only">
          {mode === 'login' ? 'Login' : 'Register'} Authentication Modal
        </h1>

        {/* Modal Content */}
        <div className="auth-modal-content">
          {mode === 'login' ? (
            <LoginForm 
              onToggleMode={toggleMode} 
              onClose={onClose}
            />
          ) : (
            <RegisterForm 
              onToggleMode={toggleMode} 
              onClose={onClose}
            />
          )}
        </div>

        {/* Modal Footer - Optional branding */}
        <div className="auth-modal-footer">
          <p className="auth-footer-text">
            Secure authentication powered by Knowledge Graph Visualizer
          </p>
        </div>
      </div>
    </div>
  );
};

export default AuthModal;