import React, { useEffect, useState } from 'react';
import useAuthStore from '../../stores/authStore';
import AuthModal from './AuthModal';

// SVG Icon for better accessibility (replacing emoji)
const LockIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" aria-hidden="true">
    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
    <circle cx="12" cy="16" r="1"/>
    <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
  </svg>
);

/**
 * ProtectedRoute Component
 * Wraps content that requires authentication
 * Shows auth modal if user is not authenticated
 */
const ProtectedRoute = ({ 
  children, 
  fallback = null,
  requireAuth = true,
  showModalOnUnauth = true,
  redirectMessage = "Please sign in to access this feature"
}) => {
  const { isAuthenticated, isLoading, isInitialized, checkAuth } = useAuthStore();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [hasCheckedAuth, setHasCheckedAuth] = useState(false);

  // Check authentication status on mount
  useEffect(() => {
    if (!isInitialized && !hasCheckedAuth) {
      checkAuth();
      setHasCheckedAuth(true);
    }
  }, [isInitialized, checkAuth, hasCheckedAuth]);

  // Show auth modal if required and user is not authenticated
  useEffect(() => {
    if (requireAuth && isInitialized && !isAuthenticated && !isLoading && showModalOnUnauth) {
      setShowAuthModal(true);
    } else {
      setShowAuthModal(false);
    }
  }, [requireAuth, isInitialized, isAuthenticated, isLoading, showModalOnUnauth]);

  const handleAuthModalClose = () => {
    setShowAuthModal(false);
  };

  // Show loading state while checking authentication
  if (isLoading || !isInitialized) {
    return (
      <div className="protected-route-loading">
        <div className="loading-spinner"></div>
        <p>Checking authentication...</p>
      </div>
    );
  }

  // If authentication is not required, always show children
  if (!requireAuth) {
    return <>{children}</>;
  }

  // If authenticated, show children
  if (isAuthenticated) {
    return <>{children}</>;
  }

  // Not authenticated - show auth modal or fallback
  return (
    <>
      {/* Show fallback content or blocking message */}
      {fallback || (
        <div className="protected-route-blocked">
          <div className="blocked-content">
            <div className="blocked-icon"><LockIcon /></div>
            <h3>Authentication Required</h3>
            <p>{redirectMessage}</p>
            <button 
              className="auth-trigger-btn"
              onClick={() => setShowAuthModal(true)}
            >
              Sign In
            </button>
          </div>
        </div>
      )}

      {/* Auth Modal */}
      {showAuthModal && (
        <AuthModal
          isOpen={showAuthModal}
          onClose={handleAuthModalClose}
          initialMode="login"
        />
      )}
    </>
  );
};

/**
 * Higher-order component version of ProtectedRoute
 * Usage: const ProtectedComponent = withAuth(MyComponent);
 */
export const withAuth = (Component, options = {}) => {
  return function ProtectedComponent(props) {
    return (
      <ProtectedRoute {...options}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
};

/**
 * Hook to check if content should be accessible
 * Returns { canAccess, showAuthPrompt }
 */
export const useAuthAccess = (requireAuth = true) => {
  const { isAuthenticated, isInitialized } = useAuthStore();
  
  return {
    canAccess: !requireAuth || (isInitialized && isAuthenticated),
    showAuthPrompt: requireAuth && isInitialized && !isAuthenticated,
    isReady: isInitialized
  };
};

export default ProtectedRoute;