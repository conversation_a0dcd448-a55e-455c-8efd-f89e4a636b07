/**
 * ProfileModal Integration Demo
 * 
 * This component demonstrates the fully integrated ProfileModal with the
 * avatar system and auth store. It provides a complete working example
 * of how users can manage their profiles and select professional avatars.
 * 
 * Features demonstrated:
 * - Professional avatar selection with 10 available avatars
 * - Profile form with validation
 * - Integration with auth store and API
 * - Unsaved changes handling
 * - Loading states and error handling
 * - Full accessibility support
 * - Mobile-responsive design
 */

import React, { useState } from 'react';
import { User } from 'lucide-react';
import useAuthStore from '../../stores/authStore';
import ProfileModal from './ProfileModal';
import Avatar from '../Avatar';
import { DEFAULT_AVATAR_ID } from '../../constants/avatarIcons';
import '../../styles/auth.css';

const ProfileModalDemo = () => {
  const { user, isAuthenticated } = useAuthStore();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  if (!isAuthenticated || !user) {
    return (
      <div style={{
        padding: 'var(--360t-space-4)',
        textAlign: 'center',
        color: 'var(--360t-dark-gray)'
      }}>
        <User size={48} style={{ marginBottom: 'var(--360t-space-3)' }} />
        <p>Please sign in to access your profile.</p>
      </div>
    );
  }

  return (
    <div style={{
      padding: 'var(--360t-space-6)',
      maxWidth: '800px',
      margin: '0 auto'
    }}>
      {/* Demo Header */}
      <div style={{ marginBottom: 'var(--360t-space-6)' }}>
        <h1 style={{
          fontSize: 'var(--360t-text-3xl)',
          fontWeight: 'var(--360t-font-bold)',
          color: 'var(--360t-text)',
          marginBottom: 'var(--360t-space-2)'
        }}>
          Profile Management Demo
        </h1>
        <p style={{
          color: 'var(--360t-dark-gray)',
          fontSize: 'var(--360t-text-lg)',
          lineHeight: 'var(--360t-leading-relaxed)'
        }}>
          This demonstrates the fully integrated ProfileModal component with professional avatar selection,
          form validation, and seamless integration with the authentication system.
        </p>
      </div>

      {/* Current Profile Display */}
      <div style={{
        backgroundColor: 'var(--360t-white)',
        border: '1px solid var(--360t-border)',
        borderRadius: 'var(--360t-radius-xl)',
        padding: 'var(--360t-space-6)',
        marginBottom: 'var(--360t-space-6)',
        boxShadow: 'var(--360t-shadow-sm)'
      }}>
        <h2 style={{
          fontSize: 'var(--360t-text-xl)',
          fontWeight: 'var(--360t-font-semibold)',
          color: 'var(--360t-text)',
          marginBottom: 'var(--360t-space-4)'
        }}>
          Current Profile
        </h2>

        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: 'var(--360t-space-4)',
          marginBottom: 'var(--360t-space-4)'
        }}>
          <Avatar
            avatarId={user.preferences?.selectedAvatar || DEFAULT_AVATAR_ID}
            size="xxl"
            theme="primary"
            alt={`${user.display_name || user.username}'s avatar`}
          />
          <div>
            <h3 style={{
              fontSize: 'var(--360t-text-2xl)',
              fontWeight: 'var(--360t-font-semibold)',
              color: 'var(--360t-text)',
              margin: '0 0 var(--360t-space-1) 0'
            }}>
              {user.display_name || user.username || 'User'}
            </h3>
            <p style={{
              color: 'var(--360t-dark-gray)',
              fontSize: 'var(--360t-text-base)',
              margin: '0 0 var(--360t-space-2) 0'
            }}>
              {user.email}
            </p>
            {user.preferences?.selectedAvatar && (
              <p style={{
                color: 'var(--360t-primary)',
                fontSize: 'var(--360t-text-sm)',
                fontWeight: 'var(--360t-font-medium)',
                margin: 0
              }}>
                Professional Avatar: {user.preferences.selectedAvatar}
              </p>
            )}
          </div>
        </div>

        <button
          onClick={handleOpenModal}
          style={{
            backgroundColor: 'var(--360t-primary)',
            color: 'var(--360t-white)',
            border: '1px solid var(--360t-primary)',
            padding: 'var(--360t-space-3) var(--360t-space-6)',
            fontSize: 'var(--360t-text-base)',
            fontWeight: 'var(--360t-font-semibold)',
            borderRadius: 'var(--360t-radius-lg)',
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            display: 'flex',
            alignItems: 'center',
            gap: 'var(--360t-space-2)'
          }}
          onMouseOver={(e) => {
            e.target.style.backgroundColor = 'var(--360t-primary-dark)';
            e.target.style.transform = 'translateY(-1px)';
          }}
          onMouseOut={(e) => {
            e.target.style.backgroundColor = 'var(--360t-primary)';
            e.target.style.transform = 'translateY(0)';
          }}
        >
          <User size={16} />
          Edit Profile
        </button>
      </div>

      {/* Feature Highlights */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: 'var(--360t-space-4)',
        marginBottom: 'var(--360t-space-6)'
      }}>
        <div style={{
          backgroundColor: 'var(--360t-light-gray)',
          padding: 'var(--360t-space-4)',
          borderRadius: 'var(--360t-radius-lg)',
          border: '1px solid var(--360t-border)'
        }}>
          <h3 style={{
            fontSize: 'var(--360t-text-lg)',
            fontWeight: 'var(--360t-font-semibold)',
            color: 'var(--360t-text)',
            marginBottom: 'var(--360t-space-2)'
          }}>
            Professional Avatars
          </h3>
          <p style={{
            color: 'var(--360t-dark-gray)',
            fontSize: 'var(--360t-text-sm)',
            lineHeight: 'var(--360t-leading-normal)',
            margin: 0
          }}>
            Choose from 10 carefully curated professional avatars, each designed for different
            business roles and contexts.
          </p>
        </div>

        <div style={{
          backgroundColor: 'var(--360t-light-gray)',
          padding: 'var(--360t-space-4)',
          borderRadius: 'var(--360t-radius-lg)',
          border: '1px solid var(--360t-border)'
        }}>
          <h3 style={{
            fontSize: 'var(--360t-text-lg)',
            fontWeight: 'var(--360t-font-semibold)',
            color: 'var(--360t-text)',
            marginBottom: 'var(--360t-space-2)'
          }}>
            Smart Validation
          </h3>
          <p style={{
            color: 'var(--360t-dark-gray)',
            fontSize: 'var(--360t-text-sm)',
            lineHeight: 'var(--360t-leading-normal)',
            margin: 0
          }}>
            Real-time form validation with helpful error messages and unsaved changes
            protection to prevent accidental data loss.
          </p>
        </div>

        <div style={{
          backgroundColor: 'var(--360t-light-gray)',
          padding: 'var(--360t-space-4)',
          borderRadius: 'var(--360t-radius-lg)',
          border: '1px solid var(--360t-border)'
        }}>
          <h3 style={{
            fontSize: 'var(--360t-text-lg)',
            fontWeight: 'var(--360t-font-semibold)',
            color: 'var(--360t-text)',
            marginBottom: 'var(--360t-space-2)'
          }}>
            Full Accessibility
          </h3>
          <p style={{
            color: 'var(--360t-dark-gray)',
            fontSize: 'var(--360t-text-sm)',
            lineHeight: 'var(--360t-leading-normal)',
            margin: 0
          }}>
            WCAG-compliant design with proper focus management, keyboard navigation,
            and screen reader support throughout.
          </p>
        </div>
      </div>

      {/* ProfileModal Integration */}
      <ProfileModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
};

export default ProfileModalDemo;