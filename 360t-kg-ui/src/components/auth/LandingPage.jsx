import React, { useState, useEffect } from 'react';
import AuthModal from './AuthModal';
import useAuthStore from '../../stores/authStore';
import { Network, Zap, Users, Database, TrendingUp } from 'lucide-react';
import '../../styles/auth.css';

const LandingPage = () => {
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authModalMode, setAuthModalMode] = useState('login');
  const { isAuthenticated, isLoading } = useAuthStore();

  // Close modal if user becomes authenticated
  useEffect(() => {
    if (isAuthenticated) {
      setShowAuthModal(false);
    }
  }, [isAuthenticated]);

  // Handle authentication modal opening
  const handleShowAuth = (mode = 'login') => {
    setAuthModalMode(mode);
    setShowAuthModal(true);
  };

  const handleCloseAuth = () => {
    setShowAuthModal(false);
  };

  // If authenticated, don't render landing page (App will handle main content)
  if (isAuthenticated) {
    return null;
  }

  return (
    <div className="landing-page">
      {/* Animated Graph Background */}
      <div className="landing-background">
        <div className="graph-pattern">
          {/* Animated Network Nodes */}
          <div className="bg-nodes">
            {[...Array(20)].map((_, i) => (
              <div 
                key={i} 
                className="bg-node" 
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 3}s`,
                  animationDuration: `${3 + Math.random() * 2}s`
                }}
              />
            ))}
          </div>
          {/* Animated Connection Lines */}
          <div className="bg-connections">
            {[...Array(15)].map((_, i) => (
              <div 
                key={i} 
                className="bg-connection" 
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  transform: `rotate(${Math.random() * 360}deg)`,
                  animationDelay: `${Math.random() * 4}s`
                }}
              />
            ))}
          </div>
        </div>
        <div className="background-overlay" />
      </div>

      {/* Main Landing Content */}
      <div className="landing-container">
        <div className="landing-content">
          {/* Brand Header */}
          <div className="landing-header">
            <div className="brand-icon">
              <Network size={48} className="brand-logo" />
            </div>
            <h1 className="landing-title">
              360T Knowledge Graph Visualizer
            </h1>
            <p className="landing-subtitle">
              Explore, analyze, and visualize complex system relationships with professional-grade graph technology
            </p>
          </div>

          {/* Feature Highlights */}
          <div className="landing-features">
            <div className="feature-grid">
              <div className="feature-item">
                <Zap size={24} className="feature-icon" />
                <h3>Interactive Visualization</h3>
                <p>2D and 3D graph exploration with real-time navigation</p>
              </div>
              <div className="feature-item">
                <Database size={24} className="feature-icon" />
                <h3>Neo4j Integration</h3>
                <p>Powered by enterprise-grade graph database technology</p>
              </div>
              <div className="feature-item">
                <TrendingUp size={24} className="feature-icon" />
                <h3>Advanced Analytics</h3>
                <p>Advanced analytics and relationship discovery</p>
              </div>
              {/* Removed Secure Access card for a cleaner trio layout */}
            </div>
          </div>

          {/* Authentication Section */}
          <div className="landing-auth-section">
            <div className="auth-welcome">
              <h2>Welcome Back</h2>
              <p>Access your knowledge graph workspace</p>
            </div>

            <div className="auth-actions">
              <button 
                className="auth-primary-btn"
                onClick={() => handleShowAuth('login')}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <div className="loading-spinner" />
                    <span>Authenticating...</span>
                  </>
                ) : (
                  <>
                    <Users size={20} />
                    <span>Sign In</span>
                  </>
                )}
              </button>
              
              <button 
                className="auth-secondary-btn"
                onClick={() => handleShowAuth('register')}
                disabled={isLoading}
              >
                Create Account
              </button>
            </div>

            <div className="auth-footer-info">
              <p>
                New to Knowledge Graph Visualizer?{' '}
                <button 
                  className="auth-link-btn"
                  onClick={() => handleShowAuth('register')}
                  disabled={isLoading}
                >
                  Create an account
                </button>
                {' '}to get started.
              </p>
            </div>
          </div>

          {/* Professional Footer */}
          <div className="landing-footer">
            <div className="footer-branding">
              <p>Powered by 360T Knowledge Graph Technology</p>
              <div className="tech-stack">
                <span className="tech-item">React</span>
                <span className="tech-divider">•</span>
                <span className="tech-item">Neo4j</span>
                <span className="tech-divider">•</span>
                <span className="tech-item">D3.js</span>
                <span className="tech-divider">•</span>
                <span className="tech-item">Three.js</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Authentication Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={handleCloseAuth}
        initialMode={authModalMode}
      />
    </div>
  );
};

export default LandingPage;
