import React, { useState, useEffect, useRef } from 'react';
import useAuthStore from '../../stores/authStore';
import '../../styles/auth.css';

// SVG Icons for better accessibility (replacing emoji)
const EyeIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" aria-hidden="true">
    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
    <circle cx="12" cy="12" r="3"/>
  </svg>
);

const EyeOffIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" aria-hidden="true">
    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94"/>
    <path d="M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19"/>
    <path d="M14.12 14.12a3 3 0 1 1-4.24-4.24"/>
    <path d="M1 1l22 22"/>
  </svg>
);

const WarningIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" aria-hidden="true">
    <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
    <line x1="12" y1="9" x2="12" y2="13"/>
    <circle cx="12" cy="17" r="1"/>
  </svg>
);

const LoginForm = ({ onToggleMode, onClose }) => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);
  const firstInputRef = useRef(null);

  const { login, isLoading, error, clearError } = useAuthStore();

  // Auto-focus first input when component mounts
  useEffect(() => {
    if (firstInputRef.current) {
      // Small delay to ensure modal transition is complete
      const timeoutId = setTimeout(() => {
        firstInputRef.current.focus();
      }, 150);
      
      return () => clearTimeout(timeoutId);
    }
  }, []);

  // Clear errors when component mounts or form data changes
  useEffect(() => {
    clearError();
  }, [clearError]);

  useEffect(() => {
    if (formData.email || formData.password) {
      clearError();
      setFormErrors({});
    }
  }, [formData, clearError]);

  const validateForm = () => {
    const errors = {};

    // Email/Username validation - more actionable error messages
    if (!formData.email.trim()) {
      errors.email = 'Please enter your email address or username';
    } else if (formData.email.includes('@') && !formData.email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      errors.email = 'Please enter a valid email address';
    }

    // Password validation - more actionable error message
    if (!formData.password) {
      errors.password = 'Please enter your password';
    } else if (formData.password.length < 6) {
      errors.password = 'Password must be at least 6 characters long';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear specific field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      const result = await login(formData);
      
      if (result.success) {
        // Close modal on successful login
        onClose?.();
      }
      // Error handling is managed by the auth store
    } catch (err) {
      console.error('Login submission error:', err);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !isLoading) {
      handleSubmit(e);
    }
  };

  return (
    <div className="auth-form">
      <div className="auth-header">
        <h2>Welcome Back</h2>
        <p>Sign in to access the Knowledge Graph Visualizer</p>
      </div>

      <form onSubmit={handleSubmit} className="auth-form-content">
        {/* Global Error Message */}
        {error && (
          <div className="auth-error-message" role="alert">
            <span className="error-icon"><WarningIcon /></span>
            {error}
          </div>
        )}

        {/* Email/Username Field */}
        <div className="form-group">
          <label htmlFor="login-email" className="form-label">
            Email or Username
          </label>
          <input
            ref={firstInputRef}
            id="login-email"
            name="email"
            type="text"
            value={formData.email}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            className={`form-input ${formErrors.email ? 'error' : ''}`}
            placeholder="Enter your email or username"
            disabled={isLoading}
            autoComplete="username"
            inputMode="email"
            aria-invalid={!!formErrors.email}
            aria-describedby={formErrors.email ? 'email-error' : undefined}
          />
          {formErrors.email && (
            <span id="email-error" className="field-error" role="alert">
              {formErrors.email}
            </span>
          )}
        </div>

        {/* Password Field */}
        <div className="form-group">
          <label htmlFor="login-password" className="form-label">
            Password
          </label>
          <div className="password-input-container">
            <input
              id="login-password"
              name="password"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
              className={`form-input ${formErrors.password ? 'error' : ''}`}
              placeholder="Enter your password"
              disabled={isLoading}
              autoComplete="current-password"
              aria-invalid={!!formErrors.password}
              aria-describedby={formErrors.password ? 'password-error' : undefined}
            />
            <button
              type="button"
              className="password-toggle"
              onClick={() => setShowPassword(!showPassword)}
              disabled={isLoading}
              aria-label={showPassword ? 'Hide password' : 'Show password'}
              tabIndex="0"
            >
              {showPassword ? <EyeOffIcon /> : <EyeIcon />}
            </button>
          </div>
          {formErrors.password && (
            <span id="password-error" className="field-error" role="alert">
              {formErrors.password}
            </span>
          )}
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          className={`auth-submit-btn ${isLoading ? 'loading' : ''}`}
          disabled={isLoading}
          aria-describedby="login-status"
        >
          {isLoading ? (
            <>
              <span className="loading-spinner"></span>
              Signing In...
            </>
          ) : (
            'Sign In'
          )}
        </button>

        {/* Toggle to Register */}
        <div className="auth-toggle">
          <p>
            Don't have an account?{' '}
            <button
              type="button"
              className="auth-toggle-btn"
              onClick={onToggleMode}
              disabled={isLoading}
            >
              Sign up here
            </button>
          </p>
        </div>
      </form>

      {/* Screen reader status */}
      <div id="login-status" className="sr-only" aria-live="polite">
        {isLoading ? 'Signing in...' : ''}
        {error ? `Login error: ${error}` : ''}
      </div>
    </div>
  );
};

export default LoginForm;