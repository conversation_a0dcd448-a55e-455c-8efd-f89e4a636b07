/**
 * ProfileModal Component Tests
 * 
 * Comprehensive test suite for the ProfileModal component covering:
 * - Avatar selection functionality
 * - Profile form handling
 * - Validation logic
 * - API integration
 * - Accessibility features
 * - User interactions
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import ProfileModal from '../ProfileModal';
import useAuthStore from '../../../stores/authStore';

// Mock the auth store
jest.mock('../../../stores/authStore');

// Mock the Avatar components
jest.mock('../../Avatar', () => {
  return function MockAvatar({ avatarId, size, theme, alt }) {
    return (
      <div data-testid="mock-avatar" data-avatar-id={avatarId} data-size={size} data-theme={theme}>
        {alt}
      </div>
    );
  };
});

jest.mock('../../AvatarSelector', () => {
  return function MockAvatarSelector({ selectedAvatarId, onAvatarSelect, disabled }) {
    const avatars = ['user', 'crown', 'star', 'shield'];
    return (
      <div data-testid="mock-avatar-selector">
        {avatars.map(id => (
          <button
            key={id}
            data-testid={`avatar-option-${id}`}
            onClick={() => !disabled && onAvatarSelect(id)}
            disabled={disabled}
            aria-pressed={selectedAvatarId === id}
          >
            {id}
          </button>
        ))}
      </div>
    );
  };
});

// Mock avatar icons
jest.mock('../../../constants/avatarIcons', () => ({
  DEFAULT_AVATAR_ID: 'user',
  getAvatarIcon: (id) => ({
    name: `${id} Avatar`,
    professionalContext: `Professional context for ${id}`,
    description: `Description for ${id}`
  })
}));

describe('ProfileModal', () => {
  const mockUser = {
    id: 'user-123',
    username: 'testuser',
    display_name: 'Test User',
    email: '<EMAIL>',
    preferences: {
      selectedAvatar: 'user'
    }
  };

  const mockAuthStore = {
    user: mockUser,
    updateProfile: jest.fn(),
    isLoading: false,
    error: null,
    clearError: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useAuthStore.mockReturnValue(mockAuthStore);
    
    // Mock focus methods
    HTMLElement.prototype.focus = jest.fn();
  });

  afterEach(() => {
    // Clean up any DOM modifications
    document.body.style.overflow = 'unset';
  });

  describe('Modal Behavior', () => {
    it('renders modal when open', () => {
      render(<ProfileModal isOpen={true} onClose={jest.fn()} />);
      
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText('Edit Profile')).toBeInTheDocument();
    });

    it('does not render when closed', () => {
      render(<ProfileModal isOpen={false} onClose={jest.fn()} />);
      
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });

    it('calls onClose when close button is clicked', async () => {
      const onClose = jest.fn();
      render(<ProfileModal isOpen={true} onClose={onClose} />);
      
      const closeButton = screen.getByRole('button', { name: /close profile modal/i });
      await userEvent.click(closeButton);
      
      expect(onClose).toHaveBeenCalledTimes(1);
    });

    it('calls onClose when escape key is pressed', () => {
      const onClose = jest.fn();
      render(<ProfileModal isOpen={true} onClose={onClose} />);
      
      fireEvent.keyDown(document, { key: 'Escape' });
      
      expect(onClose).toHaveBeenCalledTimes(1);
    });

    it('prevents body scroll when open', () => {
      render(<ProfileModal isOpen={true} onClose={jest.fn()} />);
      
      expect(document.body.style.overflow).toBe('hidden');
    });

    it('restores body scroll when closed', () => {
      const { rerender } = render(<ProfileModal isOpen={true} onClose={jest.fn()} />);
      
      expect(document.body.style.overflow).toBe('hidden');
      
      rerender(<ProfileModal isOpen={false} onClose={jest.fn()} />);
      
      expect(document.body.style.overflow).toBe('unset');
    });
  });

  describe('Form Initialization', () => {
    it('initializes form with user data', () => {
      render(<ProfileModal isOpen={true} onClose={jest.fn()} />);
      
      const displayNameInput = screen.getByLabelText(/display name/i);
      const emailInput = screen.getByLabelText(/email address/i);
      
      expect(displayNameInput).toHaveValue('Test User');
      expect(emailInput).toHaveValue('<EMAIL>');
      expect(emailInput).toBeDisabled(); // Email should be read-only
    });

    it('shows current avatar in preview', () => {
      render(<ProfileModal isOpen={true} onClose={jest.fn()} />);
      
      const avatarPreview = screen.getByTestId('mock-avatar');
      expect(avatarPreview).toHaveAttribute('data-avatar-id', 'user');
    });

    it('handles user without preferences gracefully', () => {
      const userWithoutPrefs = { ...mockUser };
      delete userWithoutPrefs.preferences;
      
      useAuthStore.mockReturnValue({
        ...mockAuthStore,
        user: userWithoutPrefs
      });
      
      render(<ProfileModal isOpen={true} onClose={jest.fn()} />);
      
      const avatarPreview = screen.getByTestId('mock-avatar');
      expect(avatarPreview).toHaveAttribute('data-avatar-id', 'user'); // Should use default
    });
  });

  describe('Avatar Selection', () => {
    it('allows avatar selection', async () => {
      render(<ProfileModal isOpen={true} onClose={jest.fn()} />);
      
      const crownAvatarButton = screen.getByTestId('avatar-option-crown');
      await userEvent.click(crownAvatarButton);
      
      // Avatar preview should update
      const avatarPreview = screen.getByTestId('mock-avatar');
      expect(avatarPreview).toHaveAttribute('data-avatar-id', 'crown');
    });

    it('marks form as dirty when avatar changes', async () => {
      render(<ProfileModal isOpen={true} onClose={jest.fn()} />);
      
      // Initially save button should be disabled (no changes)
      const saveButton = screen.getByRole('button', { name: /save changes/i });
      expect(saveButton).toBeDisabled();
      
      // Change avatar
      const starAvatarButton = screen.getByTestId('avatar-option-star');
      await userEvent.click(starAvatarButton);
      
      // Save button should now be enabled
      expect(saveButton).toBeEnabled();
    });
  });

  describe('Form Validation', () => {
    it('validates required display name', async () => {
      render(<ProfileModal isOpen={true} onClose={jest.fn()} />);
      
      const displayNameInput = screen.getByLabelText(/display name/i);
      const saveButton = screen.getByRole('button', { name: /save changes/i });
      
      // Clear the input
      await userEvent.clear(displayNameInput);
      await userEvent.click(saveButton);
      
      expect(screen.getByText('Display name is required')).toBeInTheDocument();
      expect(mockAuthStore.updateProfile).not.toHaveBeenCalled();
    });

    it('validates minimum display name length', async () => {
      render(<ProfileModal isOpen={true} onClose={jest.fn()} />);
      
      const displayNameInput = screen.getByLabelText(/display name/i);
      const saveButton = screen.getByRole('button', { name: /save changes/i });
      
      await userEvent.clear(displayNameInput);
      await userEvent.type(displayNameInput, 'A');
      await userEvent.click(saveButton);
      
      expect(screen.getByText('Display name must be at least 2 characters')).toBeInTheDocument();
      expect(mockAuthStore.updateProfile).not.toHaveBeenCalled();
    });

    it('validates maximum display name length', async () => {
      render(<ProfileModal isOpen={true} onClose={jest.fn()} />);
      
      const displayNameInput = screen.getByLabelText(/display name/i);
      const saveButton = screen.getByRole('button', { name: /save changes/i });
      
      const longName = 'A'.repeat(51);
      await userEvent.clear(displayNameInput);
      await userEvent.type(displayNameInput, longName);
      await userEvent.click(saveButton);
      
      expect(screen.getByText('Display name must be less than 50 characters')).toBeInTheDocument();
      expect(mockAuthStore.updateProfile).not.toHaveBeenCalled();
    });
  });

  describe('Form Submission', () => {
    it('submits valid form data', async () => {
      mockAuthStore.updateProfile.mockResolvedValueOnce({ success: true });
      const onClose = jest.fn();
      
      render(<ProfileModal isOpen={true} onClose={onClose} />);
      
      // Make changes
      const displayNameInput = screen.getByLabelText(/display name/i);
      await userEvent.clear(displayNameInput);
      await userEvent.type(displayNameInput, 'Updated Name');
      
      const starAvatarButton = screen.getByTestId('avatar-option-star');
      await userEvent.click(starAvatarButton);
      
      // Submit form
      const saveButton = screen.getByRole('button', { name: /save changes/i });
      await userEvent.click(saveButton);
      
      expect(mockAuthStore.updateProfile).toHaveBeenCalledWith({
        display_name: 'Updated Name',
        preferences: {
          selectedAvatar: 'star'
        }
      });
      
      await waitFor(() => {
        expect(onClose).toHaveBeenCalled();
      });
    });

    it('shows loading state during submission', async () => {
      // Create a promise that we can resolve manually
      let resolvePromise;
      const submissionPromise = new Promise(resolve => {
        resolvePromise = resolve;
      });
      
      mockAuthStore.updateProfile.mockReturnValueOnce(submissionPromise);
      
      render(<ProfileModal isOpen={true} onClose={jest.fn()} />);
      
      // Make a change and submit
      const displayNameInput = screen.getByLabelText(/display name/i);
      await userEvent.type(displayNameInput, ' Updated');
      
      const saveButton = screen.getByRole('button', { name: /save changes/i });
      await userEvent.click(saveButton);
      
      // Check loading state
      expect(screen.getByText('Saving...')).toBeInTheDocument();
      expect(saveButton).toBeDisabled();
      
      // Resolve the promise
      resolvePromise({ success: true });
      
      await waitFor(() => {
        expect(screen.queryByText('Saving...')).not.toBeInTheDocument();
      });
    });

    it('handles submission errors', async () => {
      const errorMessage = 'Profile update failed';
      mockAuthStore.updateProfile.mockResolvedValueOnce({ 
        success: false, 
        error: errorMessage 
      });
      
      render(<ProfileModal isOpen={true} onClose={jest.fn()} />);
      
      // Make changes and submit
      const displayNameInput = screen.getByLabelText(/display name/i);
      await userEvent.type(displayNameInput, ' Updated');
      
      const saveButton = screen.getByRole('button', { name: /save changes/i });
      await userEvent.click(saveButton);
      
      // Should show error from auth store
      await waitFor(() => {
        expect(mockAuthStore.updateProfile).toHaveBeenCalled();
      });
    });
  });

  describe('Unsaved Changes Warning', () => {
    it('shows warning when closing with unsaved changes', async () => {
      const onClose = jest.fn();
      render(<ProfileModal isOpen={true} onClose={onClose} />);
      
      // Make changes
      const displayNameInput = screen.getByLabelText(/display name/i);
      await userEvent.type(displayNameInput, ' Updated');
      
      // Try to close
      const closeButton = screen.getByRole('button', { name: /close profile modal/i });
      await userEvent.click(closeButton);
      
      // Should show warning, not close
      expect(screen.getByText('Unsaved Changes')).toBeInTheDocument();
      expect(onClose).not.toHaveBeenCalled();
    });

    it('allows keeping editing from warning', async () => {
      const onClose = jest.fn();
      render(<ProfileModal isOpen={true} onClose={onClose} />);
      
      // Make changes and trigger warning
      const displayNameInput = screen.getByLabelText(/display name/i);
      await userEvent.type(displayNameInput, ' Updated');
      
      const closeButton = screen.getByRole('button', { name: /close profile modal/i });
      await userEvent.click(closeButton);
      
      // Click "Keep Editing"
      const keepEditingButton = screen.getByRole('button', { name: /keep editing/i });
      await userEvent.click(keepEditingButton);
      
      // Warning should disappear, modal should stay open
      expect(screen.queryByText('Unsaved Changes')).not.toBeInTheDocument();
      expect(onClose).not.toHaveBeenCalled();
    });

    it('allows discarding changes from warning', async () => {
      const onClose = jest.fn();
      render(<ProfileModal isOpen={true} onClose={onClose} />);
      
      // Make changes and trigger warning
      const displayNameInput = screen.getByLabelText(/display name/i);
      await userEvent.type(displayNameInput, ' Updated');
      
      const closeButton = screen.getByRole('button', { name: /close profile modal/i });
      await userEvent.click(closeButton);
      
      // Click "Discard Changes"
      const discardButton = screen.getByRole('button', { name: /discard changes/i });
      await userEvent.click(discardButton);
      
      // Modal should close
      expect(onClose).toHaveBeenCalled();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(<ProfileModal isOpen={true} onClose={jest.fn()} />);
      
      const modal = screen.getByRole('dialog');
      expect(modal).toHaveAttribute('aria-modal', 'true');
      expect(modal).toHaveAttribute('aria-labelledby', 'profile-modal-title');
      expect(modal).toHaveAttribute('aria-describedby', 'profile-modal-description');
    });

    it('focuses first focusable element when opened', async () => {
      const focusSpy = jest.spyOn(HTMLElement.prototype, 'focus');
      
      render(<ProfileModal isOpen={true} onClose={jest.fn()} />);
      
      // Focus should be called (exact element may vary)
      await waitFor(() => {
        expect(focusSpy).toHaveBeenCalled();
      });
    });

    it('has proper form labels and descriptions', () => {
      render(<ProfileModal isOpen={true} onClose={jest.fn()} />);
      
      const displayNameInput = screen.getByLabelText(/display name/i);
      const emailInput = screen.getByLabelText(/email address/i);
      
      expect(displayNameInput).toBeInTheDocument();
      expect(emailInput).toBeInTheDocument();
      expect(screen.getByText('Email address is managed by your authentication provider')).toBeInTheDocument();
    });

    it('announces validation errors to screen readers', async () => {
      render(<ProfileModal isOpen={true} onClose={jest.fn()} />);
      
      const displayNameInput = screen.getByLabelText(/display name/i);
      const saveButton = screen.getByRole('button', { name: /save changes/i });
      
      await userEvent.clear(displayNameInput);
      await userEvent.click(saveButton);
      
      const errorMessage = screen.getByText('Display name is required');
      expect(errorMessage).toHaveAttribute('role', 'alert');
    });
  });

  describe('Integration', () => {
    it('preserves existing preferences when updating', async () => {
      const userWithExtraPrefs = {
        ...mockUser,
        preferences: {
          selectedAvatar: 'user',
          theme: 'dark',
          notifications: true
        }
      };
      
      useAuthStore.mockReturnValue({
        ...mockAuthStore,
        user: userWithExtraPrefs
      });
      
      mockAuthStore.updateProfile.mockResolvedValueOnce({ success: true });
      
      render(<ProfileModal isOpen={true} onClose={jest.fn()} />);
      
      // Change display name
      const displayNameInput = screen.getByLabelText(/display name/i);
      await userEvent.clear(displayNameInput);
      await userEvent.type(displayNameInput, 'New Name');
      
      // Submit
      const saveButton = screen.getByRole('button', { name: /save changes/i });
      await userEvent.click(saveButton);
      
      expect(mockAuthStore.updateProfile).toHaveBeenCalledWith({
        display_name: 'New Name',
        preferences: {
          selectedAvatar: 'user',
          theme: 'dark',
          notifications: true
        }
      });
    });

    it('handles auth store errors correctly', () => {
      const errorMessage = 'Network error';
      useAuthStore.mockReturnValue({
        ...mockAuthStore,
        error: errorMessage
      });
      
      render(<ProfileModal isOpen={true} onClose={jest.fn()} />);
      
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
      expect(screen.getByRole('alert')).toBeInTheDocument();
    });
  });
});