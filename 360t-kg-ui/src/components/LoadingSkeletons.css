/* Loading Skeletons CSS */

/* Base skeleton animations */
@keyframes skeleton-pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

@keyframes skeleton-shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes spinner-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.skeleton-pulse {
  animation: skeleton-pulse 1.5s ease-in-out infinite;
}

/* Graph Skeleton */
.graph-skeleton-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--background-color);
  border-radius: 8px;
  overflow: hidden;
}

.graph-skeleton-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.skeleton-title {
  width: 200px;
  height: 24px;
  background: var(--border-color);
  border-radius: 4px;
}

.skeleton-controls {
  width: 120px;
  height: 32px;
  background: var(--border-color);
  border-radius: 6px;
}

.graph-skeleton-canvas {
  flex: 1;
  position: relative;
  background: var(--background-color);
  overflow: hidden;
}

.skeleton-2d {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.skeleton-3d {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

.skeleton-nodes {
  position: absolute;
  width: 100%;
  height: 100%;
}

.skeleton-node {
  position: absolute;
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  border-radius: 50%;
  opacity: 0.6;
}

.skeleton-connections {
  position: absolute;
  width: 100%;
  height: 100%;
}

.skeleton-connection {
  position: absolute;
  height: 2px;
  background: var(--border-color);
  opacity: 0.4;
}

.skeleton-connection:nth-child(1) {
  width: 120px;
  top: 25%;
  left: 20%;
  transform: rotate(45deg);
}

.skeleton-connection:nth-child(2) {
  width: 100px;
  top: 35%;
  left: 40%;
  transform: rotate(-30deg);
}

.skeleton-connection:nth-child(3) {
  width: 80px;
  top: 55%;
  left: 25%;
  transform: rotate(15deg);
}

.skeleton-connection:nth-child(4) {
  width: 110px;
  top: 45%;
  left: 55%;
  transform: rotate(-45deg);
}

.skeleton-connection:nth-child(5) {
  width: 90px;
  top: 65%;
  left: 45%;
  transform: rotate(60deg);
}

.skeleton-connection:nth-child(6) {
  width: 70px;
  top: 75%;
  left: 35%;
  transform: rotate(-15deg);
}

.skeleton-loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10;
}

.skeleton-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spinner-rotate 1s linear infinite;
  margin: 0 auto 1rem;
}

.skeleton-loading-text {
  color: var(--text-color);
  font-size: 0.875rem;
  opacity: 0.7;
}

/* Data Processing Skeleton */
.data-processing-skeleton {
  padding: 2rem;
  background: var(--background-color);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.processing-steps {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.processing-step {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.step-icon {
  width: 32px;
  height: 32px;
  background: var(--primary-color);
  border-radius: 50%;
  opacity: 0.7;
}

.step-text {
  flex: 1;
}

.skeleton-text-line {
  width: 180px;
  height: 16px;
  background: var(--border-color);
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.skeleton-text-line-small {
  width: 120px;
  height: 12px;
  background: var(--border-color);
  border-radius: 4px;
  opacity: 0.6;
}

.step-progress {
  width: 120px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--border-color);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
}

.processing-animation {
  background: var(--primary-color);
  animation: processing-fill 2s ease-in-out infinite;
}

.transforming-animation {
  background: #f59e0b;
  animation: transforming-fill 1.8s ease-in-out infinite;
}

.rendering-animation {
  background: #00973a;
  animation: rendering-fill 1.5s ease-in-out infinite;
}

@keyframes processing-fill {
  0%, 100% { width: 0%; }
  50% { width: 70%; }
}

@keyframes transforming-fill {
  0%, 100% { width: 0%; }
  50% { width: 85%; }
}

@keyframes rendering-fill {
  0%, 100% { width: 0%; }
  50% { width: 95%; }
}

/* Compact Loading Skeleton */
.compact-loading-skeleton {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: var(--background-color);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.compact-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spinner-rotate 1s linear infinite;
}

.compact-message {
  color: var(--text-color);
  font-size: 0.875rem;
  opacity: 0.8;
}

/* Phase Loading Indicator */
.phase-loading-indicator {
  padding: 1rem;
  background: var(--background-color);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.phase-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.phase-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: skeleton-pulse 1.5s ease-in-out infinite;
}

.phase-label {
  color: var(--text-color);
  font-size: 0.875rem;
  font-weight: 500;
}

.phase-progress {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.phase-progress-bar {
  flex: 1;
  height: 6px;
  background: var(--border-color);
  border-radius: 3px;
  overflow: hidden;
}

.phase-progress-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.phase-progress-text {
  color: var(--text-color);
  font-size: 0.75rem;
  opacity: 0.7;
  min-width: 35px;
  text-align: right;
}

/* Network Request Skeleton */
.network-request-skeleton {
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: 4px;
  border: 1px solid var(--border-color);
  font-family: monospace;
  font-size: 0.75rem;
}

.request-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.request-method {
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
  font-weight: 600;
  font-size: 0.625rem;
}

.request-url {
  background: var(--border-color);
  height: 16px;
  border-radius: 3px;
  flex: 1;
}

.request-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-color);
  opacity: 0.7;
}

.status-indicator {
  width: 8px;
  height: 8px;
  background: var(--primary-color);
  border-radius: 50%;
}

.pulsing {
  animation: skeleton-pulse 1s ease-in-out infinite;
}
