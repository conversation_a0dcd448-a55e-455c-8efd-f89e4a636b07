import React, { useRef, useEffect, useState } from 'react';
import { getCategoryColor, getCategoryBackgroundColor, getContrastTextColor } from '../constants/categoryColors';

const SearchField = ({
  searchQuery = '',
  setSearchQuery = () => {},
  searchResults = [],
  showSearchResults = false,
  onSearch = () => {},
  onCenterOnNode = () => {}
}) => {
  const [selectedSearchIndex, setSelectedSearchIndex] = useState(-1);
  const searchContainerRef = useRef(null);

  // Handle search dropdown dismissal
  const dismissSearchDropdown = () => {
    setSearchQuery(''); // This calls the parent's setSearchQuery function
    setSelectedSearchIndex(-1);
  };

  // Handle keyboard navigation for search dropdown
  const handleSearchKeyDown = (e) => {
    if (!showSearchResults || searchResults.length === 0) {
      // <PERSON>le escape even when dropdown is not visible
      if (e.key === 'Escape') {
        e.preventDefault();
        dismissSearchDropdown();
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSearchIndex(prev =>
          prev < searchResults.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSearchIndex(prev =>
          prev > 0 ? prev - 1 : searchResults.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedSearchIndex >= 0 && selectedSearchIndex < searchResults.length) {
          onCenterOnNode(searchResults[selectedSearchIndex]);
          dismissSearchDropdown();
        }
        break;
      case 'Escape':
        e.preventDefault();
        dismissSearchDropdown();
        break;
      default:
        // Reset selection when typing
        if (e.key.length === 1 || e.key === 'Backspace' || e.key === 'Delete') {
          setSelectedSearchIndex(-1);
        }
        break;
    }
  };

  // Handle click outside to dismiss search dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showSearchResults && 
          searchContainerRef.current && 
          !searchContainerRef.current.contains(event.target)) {
        dismissSearchDropdown();
      }
    };

    // Add event listener
    document.addEventListener('mousedown', handleClickOutside);
    
    // Cleanup
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showSearchResults]);

  return (
    <div 
      className="search-field-container"
      style={{
        width: '280px',
        marginBottom: '8px',
        position: 'relative'
      }}
    >
      {/* Search Field */}
      <div 
        ref={searchContainerRef}
        style={{
          position: 'relative',
          width: '100%'
        }}
      >
        <div style={{
          padding: '8px',
          backgroundColor: '#f9fafb',
          borderRadius: '8px',
          border: '1px solid #e5e7eb',
          width: '100%',
          boxSizing: 'border-box'
        }}>
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '6px',
            width: '100%',
            boxSizing: 'border-box'
          }}>
            <span style={{ flexShrink: 0 }}>🔍</span>
            <input
              type="text"
              placeholder="Search nodes..."
              value={searchQuery}
              onChange={(e) => {
                const value = e.target.value;
                setSearchQuery(value); // Update UI immediately
                onSearch(value); // This is debounced, so won't cause immediate re-renders
                setSelectedSearchIndex(-1); // Reset selection when typing
              }}
              onKeyDown={handleSearchKeyDown}
              style={{
                flex: 1,
                width: '100%',
                padding: '6px 8px',
                border: '1px solid #d1d5db',
                borderRadius: '4px',
                fontSize: '12px',
                minWidth: 0, // Allows flex item to shrink below content size
                boxSizing: 'border-box',
                outline: 'none'
              }}
            />
          </div>
        </div>

        {/* Search Results Dropdown */}
        {showSearchResults && searchResults.length > 0 && (
          <div style={{
            position: 'absolute',
            top: '100%',
            left: '0',
            right: '0',
            background: 'white',
            border: '1px solid #e5e7eb',
            borderRadius: '4px',
            boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
            maxHeight: '200px',
            overflowY: 'auto',
            marginTop: '2px',
            zIndex: 1000,
            width: '100%',
            boxSizing: 'border-box'
          }}>
            {searchResults.map((node, index) => (
              <div
                key={node.id}
                onClick={(e) => {
                  e.stopPropagation(); // Prevent event bubbling
                  onCenterOnNode(node);
                  dismissSearchDropdown();
                }}
                onMouseEnter={() => setSelectedSearchIndex(index)}
                style={{
                  padding: '6px 8px',
                  cursor: 'pointer',
                  borderBottom: index < searchResults.length - 1 ? '1px solid #f3f4f6' : 'none',
                  fontSize: '11px',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  backgroundColor: index === selectedSearchIndex ? 'rgba(0, 151, 58, 0.1)' : 'transparent',
                  borderLeft: index === selectedSearchIndex ? '3px solid #00973A' : 'none'
                }}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                  <span 
                    style={{ fontWeight: '500', flex: 1, minWidth: 0, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}
                    title={node.name}
                  >
                    {node.name}
                  </span>
                  <div style={{ display: 'flex', gap: '4px', alignItems: 'center', flexShrink: 0 }}>
                    {node.category && (
                      <span style={{
                        color: getContrastTextColor(getCategoryColor(node.category)),
                        fontSize: '9px',
                        backgroundColor: getCategoryColor(node.category),
                        padding: '1px 4px',
                        borderRadius: '3px',
                        fontWeight: '500',
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px'
                      }}>
                        {node.category}
                      </span>
                    )}
                    <span style={{
                      color: node.color,
                      fontSize: '9px',
                      backgroundColor: node.color + '20',
                      padding: '1px 4px',
                      borderRadius: '3px',
                      textTransform: 'capitalize'
                    }}>
                      {node.group}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchField;
