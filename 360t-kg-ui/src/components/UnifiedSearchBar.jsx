import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Search, 
  Close, 
  FilterList, 
  Check, 
  ExpandMore, 
  Settings,
  KeyboardArrowUp 
} from '@mui/icons-material';
import { 
  Autocomplete, 
  TextField, 
  Chip, 
  Box, 
  Paper, 
  Typography,
  Button,
  Divider,
  IconButton,
  Badge
} from '@mui/material';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import debounce from 'lodash.debounce';
import PropTypes from 'prop-types';
import { 
  getCategoryColor, 
  getCategoryBackgroundColor, 
  getContrastTextColor,
  getAllCategoryColors 
} from '../constants/categoryColors';
import configStore from '../stores/configStore';
import './UnifiedSearchBar.css';

// Custom theme for the unified search bar
const unifiedSearchTheme = createTheme({
  components: {
    MuiAutocomplete: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            backgroundColor: 'rgba(255, 255, 255, 0.85)',
            backdropFilter: 'blur(12px)',
            border: 'none',
            borderRadius: '30px',
            height: '60px',
            padding: '0 24px',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
            },
            '&.Mui-focused': {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              boxShadow: '0 16px 48px rgba(0, 151, 58, 0.15)',
            },
          },
          '& .MuiOutlinedInput-notchedOutline': {
            border: '1px solid rgba(255, 255, 255, 0.3)',
          },
          '& .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline': {
            border: '1px solid rgba(0, 151, 58, 0.2)',
          },
          '& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline': {
            border: '2px solid #00973A',
          },
        },
        paper: {
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(16px)',
          border: '1px solid rgba(255, 255, 255, 0.3)',
          borderRadius: '16px',
          boxShadow: '0 16px 48px rgba(0, 0, 0, 0.1)',
          marginTop: '8px',
          maxHeight: '320px',
        },
        listbox: {
          padding: '8px',
          maxHeight: '280px',
          overflowY: 'auto',
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            background: 'rgba(0, 0, 0, 0.05)',
            borderRadius: '3px',
          },
          '&::-webkit-scrollbar-thumb': {
            background: 'rgba(0, 151, 58, 0.3)',
            borderRadius: '3px',
            '&:hover': {
              background: 'rgba(0, 151, 58, 0.5)',
            },
          },
        },
        option: {
          borderRadius: '8px',
          margin: '2px 0',
          '&.Mui-focused': {
            backgroundColor: 'rgba(0, 151, 58, 0.08)',
          },
          '&[aria-selected="true"]': {
            backgroundColor: 'rgba(0, 151, 58, 0.12)',
          },
        },
      },
    },
  },
});

const UnifiedSearchBar = ({
  searchQuery = '',
  onSearchChange = () => {},
  searchResults = [],
  onNodeSelect = () => {},
  onCenterOnNode = () => {},
  placeholder = 'Search knowledge graph...',
  disabled = false,
  showRecentSearches = true,
  maxResults = 10,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [recentSearches, setRecentSearches] = useState([]);
  const [reducedMotion, setReducedMotion] = useState(false);
  const [showCategoryFilter, setShowCategoryFilter] = useState(false);
  const allCategories = useMemo(() => Object.keys(getAllCategoryColors()), []);

  const createCategorySet = useCallback((filters) => {
    if (Array.isArray(filters)) {
      return new Set(filters.filter((category) => allCategories.includes(category)));
    }
    if (filters === null || typeof filters === 'undefined') {
      return new Set(allCategories);
    }
    return new Set();
  }, [allCategories]);

  const [selectedCategories, setSelectedCategories] = useState(() => {
    const config = configStore.getConfig();
    return createCategorySet(config?.graphiti?.categoryFilters);
  });
  
  const searchRef = useRef(null);
  const inputRef = useRef(null);
  const filterPanelRef = useRef(null);

  useEffect(() => {
    const unsubscribe = configStore.subscribe(
      (state) => state.config,
      (nextConfig) => {
        const filters = nextConfig?.graphiti?.categoryFilters;
        setSelectedCategories((prev) => {
          const candidate = createCategorySet(filters);
          if (candidate.size === prev.size) {
            for (const category of candidate) {
              if (!prev.has(category)) {
                return candidate;
              }
            }
            for (const category of prev) {
              if (!candidate.has(category)) {
                return candidate;
              }
            }
            return prev;
          }
          return candidate;
        });
      }
    );

    return () => {
      if (typeof unsubscribe === 'function') {
        unsubscribe();
      }
    };
  }, [createCategorySet]);

  const persistCategoryFilters = useCallback((categorySet) => {
    const filters = (() => {
      if (categorySet.size === 0) return [];
      if (categorySet.size === allCategories.length) return null;
      return Array.from(categorySet).filter((category) => allCategories.includes(category)).sort();
    })();

    configStore.updateGraphiti({ categoryFilters: filters });
  }, [allCategories]);

  // Check for reduced motion preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setReducedMotion(mediaQuery.matches);
    
    const handler = (e) => setReducedMotion(e.matches);
    mediaQuery.addEventListener('change', handler);
    
    return () => mediaQuery.removeEventListener('change', handler);
  }, []);

  // Load recent searches from localStorage
  useEffect(() => {
    try {
      const saved = localStorage.getItem('unifiedSearch.recentSearches');
      if (saved) {
        setRecentSearches(JSON.parse(saved));
      }
    } catch (error) {
      console.warn('Failed to load recent searches:', error);
    }
  }, []);

  // Save recent searches to localStorage
  const saveRecentSearch = useCallback((searchTerm) => {
    if (!searchTerm.trim()) return;
    
    try {
      setRecentSearches(prev => {
        const updated = [searchTerm, ...prev.filter(term => term !== searchTerm)].slice(0, 5);
        localStorage.setItem('unifiedSearch.recentSearches', JSON.stringify(updated));
        return updated;
      });
    } catch (error) {
      console.warn('Failed to save recent search:', error);
    }
  }, []);

  // Debounced search function
  const debouncedSearch = useMemo(
    () => debounce((query) => {
      onSearchChange(query);
    }, 300),
    [onSearchChange]
  );

  // Handle search input change
  const handleInputChange = useCallback((event, newValue) => {
    const value = typeof newValue === 'string' ? newValue : event?.target?.value || '';
    debouncedSearch(value);
  }, [debouncedSearch]);

  // Handle focus and expansion
  const handleFocus = useCallback(() => {
    setIsExpanded(true);
    setIsFocused(true);
  }, []);

  const handleBlur = useCallback(() => {
    // Increased delay to ensure click events complete
    setTimeout(() => {
      setIsExpanded(false);
      setIsFocused(false);
      setSelectedIndex(-1);
    }, 200);
  }, []);

  // Handle clear search
  const handleClear = useCallback(() => {
    onSearchChange('');
    setSelectedIndex(-1);
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, [onSearchChange]);

  // Handle keyboard navigation
  const handleKeyDown = useCallback((event) => {
    if (event.key === 'Escape') {
      setIsExpanded(false);
      setIsFocused(false);
      setShowCategoryFilter(false);
      if (inputRef.current) {
        inputRef.current.blur();
      }
    }
  }, []);

  // Get node type for styling
  const getNodeType = useCallback((node) => {
    if (node.labels && node.labels.length > 0) {
      return node.labels[0];
    }
    return node.group || 'Default';
  }, []);

  // Get node name for display
  const getNodeName = useCallback((node) => {
    return node.properties?.name || 
           node.properties?.test_case_id || 
           node.name || 
           node.id || 
           'Unknown';
  }, []);

  // Get node category for filtering and badge display
  const getNodeCategory = useCallback((node) => {
    return node.category || node.properties?.category || 'default';
  }, []);

  // Filter search results by selected categories
  const filteredResults = useMemo(() => {
    if (selectedCategories.size === allCategories.length) {
      return searchResults.slice(0, maxResults);
    }
    
    return searchResults
      .filter(node => selectedCategories.has(getNodeCategory(node)))
      .slice(0, maxResults);
  }, [searchResults, selectedCategories, allCategories, maxResults, getNodeCategory]);

  // Handle category filter toggle
  const handleCategoryToggle = useCallback((category) => {
    if (!allCategories.includes(category)) {
      return;
    }

    setSelectedCategories(prev => {
      const next = new Set(prev);
      if (next.has(category)) {
        next.delete(category);
      } else {
        next.add(category);
      }
      persistCategoryFilters(next);
      return next;
    });
  }, [allCategories, persistCategoryFilters]);

  // Handle select all categories
  const handleSelectAllCategories = useCallback(() => {
    setSelectedCategories(() => {
      const next = new Set(allCategories);
      persistCategoryFilters(next);
      return next;
    });
  }, [allCategories, persistCategoryFilters]);

  // Handle clear all categories
  const handleClearAllCategories = useCallback(() => {
    setSelectedCategories(() => {
      const next = new Set();
      persistCategoryFilters(next);
      return next;
    });
  }, [persistCategoryFilters]);

  // Handle node selection - MUI Autocomplete onChange signature: (event, value, reason, details)
  const handleNodeSelect = useCallback((event, value, reason, details) => {
    console.log('🔍 handleNodeSelect called:', { 
      value: value?.name || value?.id, 
      reason, 
      eventType: event?.type,
      hasEvent: !!event 
    });
    
    // Value is the selected node object from the autocomplete options
    if (value && reason !== 'clear') {
      const nodeName = getNodeName(value);
      console.log('✅ Opening node details for:', nodeName);
      saveRecentSearch(nodeName);
      onNodeSelect(value);
      onCenterOnNode(value);
    }
  }, [getNodeName, saveRecentSearch, onNodeSelect, onCenterOnNode]);

  // Custom option rendering with category badges
  const renderOption = useCallback((props, option) => {
    const { key, ...otherProps } = props;
    // Generate unique key based on node ID to prevent duplicate keys for nodes with same name
    // This fixes React warnings about duplicate keys when multiple nodes have identical names
    const uniqueKey = `search-option-${option.id || option.name || key || Math.random()}`;
    const nodeCategory = getNodeCategory(option);
    const categoryColor = getCategoryColor(nodeCategory);
    const contrastColor = getContrastTextColor(categoryColor);
    
    // Debug logging for key generation (can be removed in production)
    if (process.env.NODE_ENV === 'development' && !option.id) {
      console.warn('Search option missing ID, using fallback for key generation:', option);
    }
    
    return (
      <Box component="li" key={uniqueKey} {...otherProps}>
        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', gap: 1 }}>
          <Box
            sx={{
              width: 8,
              height: 8,
              borderRadius: '50%',
              backgroundColor: categoryColor,
              flexShrink: 0,
            }}
          />
          <Box sx={{ flex: 1, minWidth: 0 }}>
            <Typography
              variant="body2"
              sx={{
                fontWeight: 500,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {getNodeName(option)}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 0.5, flexShrink: 0 }}>
            <Chip
              label={nodeCategory}
              size="small"
              sx={{
                height: 20,
                fontSize: '0.75rem',
                backgroundColor: categoryColor,
                color: contrastColor,
                border: 'none',
                fontWeight: 600,
                textTransform: 'uppercase',
                letterSpacing: '0.5px',
              }}
            />
            <Chip
              label={getNodeType(option)}
              size="small"
              sx={{
                height: 18,
                fontSize: '0.7rem',
                backgroundColor: 'rgba(107, 114, 128, 0.1)',
                color: '#6b7280',
                border: 'none',
              }}
            />
          </Box>
        </Box>
      </Box>
    );
  }, [getNodeCategory, getNodeName, getNodeType]);

  // Calculate active filter count and result statistics
  const activeFilterCount = allCategories.length - selectedCategories.size;
  const isFiltering = activeFilterCount > 0;
  const resultCount = filteredResults.length;
  const totalAvailable = searchResults.length;

  // Animation variants
  const containerVariants = {
    collapsed: {
      width: 700,
      transition: {
        duration: reducedMotion ? 0 : 0.3,
        ease: 'easeInOut',
      },
    },
    expanded: {
      width: 750,
      transition: {
        duration: reducedMotion ? 0 : 0.3,
        ease: 'easeInOut',
      },
    },
  };

  // Close filter panel when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showCategoryFilter && 
          filterPanelRef.current && 
          !filterPanelRef.current.contains(event.target)) {
        setShowCategoryFilter(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showCategoryFilter]);

  return (
    <ThemeProvider theme={unifiedSearchTheme}>
      <motion.div
        ref={searchRef}
        className="unified-search-container"
        variants={containerVariants}
        animate={isExpanded ? 'expanded' : 'collapsed'}
        initial="collapsed"
        style={{
          position: 'absolute',
          top: '20px',
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 100,
          height: '60px',
        }}
      >
        <Autocomplete
          freeSolo
          options={filteredResults}
          getOptionLabel={(option) => getNodeName(option)}
          getOptionKey={(option) => option.id || option.name || `fallback-${Math.random()}`}
          renderOption={renderOption}
          onChange={handleNodeSelect}
          onInputChange={handleInputChange}
          onKeyDown={handleKeyDown}
          ListboxProps={{
            style: {
              maxHeight: '300px',
              padding: '8px',
              overflow: 'auto'
            }
          }}
          slotProps={{
            popper: {
              modifiers: [
                {
                  name: 'offset',
                  options: {
                    offset: [0, 8],
                  },
                },
              ],
            },
          }}
          disabled={disabled}
          renderInput={(params) => (
            <TextField
              {...params}
              inputRef={inputRef}
              placeholder={
                searchQuery && resultCount > 0 
                  ? isFiltering 
                    ? `Showing ${resultCount} of ${totalAvailable} results (filtered)` 
                    : `${resultCount} results found`
                  : searchQuery 
                    ? 'No results found' 
                    : placeholder
              }
              variant="outlined"
              fullWidth
              onFocus={handleFocus}
              onBlur={handleBlur}
              InputProps={{
                ...params.InputProps,
                startAdornment: (
                  <Search sx={{ color: '#6b7280', mr: 1 }} />
                ),
                endAdornment: (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {searchQuery && (
                      <IconButton
                        size="small"
                        onClick={handleClear}
                        sx={{ 
                          color: '#6b7280',
                          '&:hover': { backgroundColor: 'rgba(107, 114, 128, 0.1)' }
                        }}
                      >
                        <Close fontSize="small" />
                      </IconButton>
                    )}
                    <IconButton
                      size="small"
                      onClick={() => setShowCategoryFilter(!showCategoryFilter)}
                      sx={{ 
                        color: showCategoryFilter ? '#00973A' : '#6b7280',
                        backgroundColor: showCategoryFilter ? 'rgba(0, 151, 58, 0.1)' : 'transparent',
                        '&:hover': { 
                          backgroundColor: showCategoryFilter ? 'rgba(0, 151, 58, 0.15)' : 'rgba(107, 114, 128, 0.1)' 
                        }
                      }}
                    >
                      <Badge 
                        badgeContent={activeFilterCount > 0 ? activeFilterCount : null}
                        color="error"
                        variant="standard"
                      >
                        <FilterList fontSize="small" />
                      </Badge>
                    </IconButton>
                  </Box>
                ),
              }}
            />
          )}
        />

        {/* Category Filter Panel */}
        <AnimatePresence>
          {showCategoryFilter && (
            <motion.div
              ref={filterPanelRef}
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className="category-filter-panel"
              style={{
                position: 'absolute',
                top: 'calc(100% + 8px)',
                right: 0,
                width: 480, // Increased from 320 to accommodate more chips horizontally
                maxHeight: 400, // Increased back to 400px to allow vertical space if needed
                backgroundColor: 'rgba(255, 255, 255, 0.88)',
                backdropFilter: 'blur(20px) saturate(180%)',
                WebkitBackdropFilter: 'blur(20px) saturate(180%)',
                border: '1px solid rgba(255, 255, 255, 0.4)',
                borderRadius: 16,
                boxShadow: '0 16px 48px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(0, 151, 58, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.6)',
                padding: 10, // Reduced from 12px to 10px for tighter spacing
                overflowY: 'auto',
                zIndex: 1200,
              }}
            >
              {/* Category Chips Grid */}
              <Box 
                sx={{ 
                  display: 'grid',
                  gridTemplateColumns: {
                    xs: 'repeat(2, 1fr)', // 2 columns on mobile
                    sm: 'repeat(4, 1fr)', // 4 columns on desktop for wider panel
                    md: 'repeat(5, 1fr)', // 5 columns on larger screens to show more chips
                  },
                  gap: 0.75,
                  mb: 1.5,
                }}
              >
                {allCategories.map((category) => {
                  const categoryColor = getCategoryColor(category);
                  const isSelected = selectedCategories.has(category);
                  const contrastColor = getContrastTextColor(categoryColor);
                  
                  return (
                    <Chip
                      key={category}
                      label={category}
                      clickable
                      onClick={() => handleCategoryToggle(category)}
                      variant={isSelected ? "filled" : "outlined"}
                      size="small"
                      sx={{
                        height: 28,
                        fontSize: '0.75rem',
                        fontWeight: 500,
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px',
                        justifyContent: 'center',
                        backgroundColor: isSelected ? categoryColor : 'transparent',
                        color: isSelected ? contrastColor : categoryColor,
                        borderColor: categoryColor,
                        borderWidth: '1.5px',
                        '&:hover': {
                          backgroundColor: isSelected 
                            ? categoryColor 
                            : `${categoryColor}15`,
                          borderColor: categoryColor,
                          transform: 'scale(1.02)',
                          boxShadow: `0 2px 8px ${categoryColor}30`,
                        },
                        '&:active': {
                          transform: 'scale(0.98)',
                        },
                        transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                      }}
                    />
                  );
                })}
              </Box>

              {/* Compact Action Bar */}
              <Box 
                sx={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  alignItems: 'center',
                  pt: 1,
                  borderTop: '1px solid rgba(0, 151, 58, 0.1)',
                  gap: 1,
                }}
              >
                <Box sx={{ display: 'flex', gap: 0.75 }}>
                  <Button 
                    size="small" 
                    onClick={handleSelectAllCategories}
                    variant="text"
                    sx={{ 
                      fontSize: '0.7rem', 
                      textTransform: 'none', 
                      minWidth: 'auto', 
                      px: 1.5,
                      py: 0.5,
                      height: 24,
                      color: '#00973A',
                      '&:hover': {
                        backgroundColor: 'rgba(0, 151, 58, 0.08)',
                      }
                    }}
                  >
                    All
                  </Button>
                  <Button 
                    size="small" 
                    onClick={handleClearAllCategories}
                    variant="text"
                    sx={{ 
                      fontSize: '0.7rem', 
                      textTransform: 'none', 
                      minWidth: 'auto', 
                      px: 1.5,
                      py: 0.5,
                      height: 24,
                      color: '#6b7280',
                      '&:hover': {
                        backgroundColor: 'rgba(107, 114, 128, 0.08)',
                      }
                    }}
                  >
                    None
                  </Button>
                </Box>
                {isFiltering && (
                  <Chip 
                    label={`${activeFilterCount} hidden`}
                    size="small" 
                    sx={{ 
                      backgroundColor: 'rgba(0, 151, 58, 0.1)', 
                      color: '#00973A', 
                      fontSize: '0.65rem',
                      height: 20,
                      fontWeight: 500,
                    }} 
                  />
                )}
              </Box>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </ThemeProvider>
  );
};

UnifiedSearchBar.propTypes = {
  searchQuery: PropTypes.string,
  onSearchChange: PropTypes.func,
  searchResults: PropTypes.array,
  onNodeSelect: PropTypes.func,
  onCenterOnNode: PropTypes.func,
  placeholder: PropTypes.string,
  disabled: PropTypes.bool,
  showRecentSearches: PropTypes.bool,
  maxResults: PropTypes.number,
};

export default UnifiedSearchBar;
