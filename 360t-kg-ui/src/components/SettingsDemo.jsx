/**
 * Settings Service Demonstration Component
 * 
 * This component demonstrates the simplified settingsService features:
 * - Simple localStorage persistence
 * - User-specific legend color storage
 * - Avatar management
 * - Local settings handling (no cloud sync)
 */

import React, { useState, useEffect } from 'react';
import { useSettings } from '../hooks/useSettings';
import useAuthStore from '../stores/authStore';
import { getSettingsStatus, reloadLegendColors, clearUserLegendColors } from '../services/authSettingsIntegration';

const SettingsDemo = () => {
  const {
    settings,
    isReady,
    getSelectedAvatar,
    setSelectedAvatar,
    getAvailableAvatars,
    setNodeColor,
    getNodeColors,
    setCurrentUser
  } = useSettings();

  const { isAuthenticated, user } = useAuthStore();
  const [settingsStatus, setSettingsStatus] = useState(null);
  const [availableAvatars, setAvailableAvatars] = useState([]);
  const [selectedColor, setSelectedColor] = useState('#4f46e5');

  // Update settings status periodically
  useEffect(() => {
    const updateSettingsStatus = () => {
      const status = getSettingsStatus();
      setSettingsStatus(status);
    };

    updateSettingsStatus();
    const interval = setInterval(updateSettingsStatus, 2000);

    return () => clearInterval(interval);
  }, [isAuthenticated]);

  // Update user context when auth state changes
  useEffect(() => {
    if (setCurrentUser) {
      setCurrentUser(user);
    }
  }, [user, setCurrentUser]);

  // Load available avatars
  useEffect(() => {
    if (isReady) {
      const avatars = getAvailableAvatars();
      setAvailableAvatars(avatars);
    }
  }, [isReady, getAvailableAvatars]);

  const handleAvatarChange = async (avatarId) => {
    const success = await setSelectedAvatar(avatarId);
    if (success) {
      console.log(`Avatar changed to: ${avatarId}`);
      // Update available avatars to reflect selection
      const updatedAvatars = getAvailableAvatars();
      setAvailableAvatars(updatedAvatars);
    }
  };

  const handleColorChange = async (nodeType) => {
    const success = await setNodeColor(nodeType, selectedColor);
    if (success) {
      console.log(`Color changed for ${nodeType}: ${selectedColor}`);
    }
  };

  const handleReloadColors = () => {
    const success = reloadLegendColors();
    console.log('Reload legend colors:', success ? 'successful' : 'failed');
  };

  const handleClearUserColors = () => {
    if (user?.id) {
      const success = clearUserLegendColors(user.id);
      console.log('Clear user colors:', success ? 'successful' : 'failed');
      if (success) {
        handleReloadColors(); // Reload to show changes
      }
    }
  };

  if (!isReady) {
    return <div>Loading settings...</div>;
  }

  const currentAvatar = getSelectedAvatar();
  const nodeColors = getNodeColors();

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h2>Simplified Settings Service Demo</h2>
      
      {/* Authentication Status */}
      <div style={{ marginBottom: '20px', padding: '15px', background: '#f5f5f5', borderRadius: '8px' }}>
        <h3>Authentication Status</h3>
        <p><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
        {isAuthenticated && user && (
          <p><strong>User:</strong> {user.username || user.name} ({user.email})</p>
        )}
      </div>

      {/* Storage Status */}
      <div style={{ marginBottom: '20px', padding: '15px', background: '#f0f8ff', borderRadius: '8px' }}>
        <h3>Storage Status (Local Only)</h3>
        <p><strong>Settings Ready:</strong> {settingsStatus?.isReady ? 'Yes' : 'No'}</p>
        <p><strong>Integration Active:</strong> {settingsStatus?.integrated ? 'Yes' : 'No'}</p>
        <p><strong>Storage Type:</strong> {settingsStatus?.storageType}</p>
        <p><strong>Legend Colors Key:</strong> <code>{settingsStatus?.legendColorsKey}</code></p>
        
        <div style={{ marginTop: '10px' }}>
          <button onClick={handleReloadColors} style={{ marginRight: '10px' }}>
            Reload Legend Colors
          </button>
          {isAuthenticated && user?.id && (
            <button onClick={handleClearUserColors} style={{ backgroundColor: '#ff6b6b', color: 'white' }}>
              Clear User Colors
            </button>
          )}
        </div>
      </div>

      {/* Avatar Management */}
      <div style={{ marginBottom: '20px', padding: '15px', background: '#fff5ee', borderRadius: '8px' }}>
        <h3>Avatar Management</h3>
        <p><strong>Current Avatar:</strong> {currentAvatar}</p>
        
        <div style={{ marginTop: '10px' }}>
          <h4>Available Avatars:</h4>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '10px' }}>
            {availableAvatars.map(avatar => (
              <button
                key={avatar.id}
                onClick={() => handleAvatarChange(avatar.id)}
                style={{
                  padding: '10px',
                  border: avatar.selected ? '2px solid #00973A' : '1px solid #ccc',
                  borderRadius: '8px',
                  background: avatar.selected ? '#f0f8f0' : 'white',
                  cursor: 'pointer'
                }}
              >
                <div><strong>{avatar.id}</strong></div>
                <div style={{ fontSize: '0.9em', color: '#666' }}>{avatar.name}</div>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Legend Color Management */}
      <div style={{ marginBottom: '20px', padding: '15px', background: '#f9f9ff', borderRadius: '8px' }}>
        <h3>Legend Color Management</h3>
        <p><strong>Storage:</strong> Colors are saved to localStorage {user?.id ? `for user ${user.id}` : 'globally'}</p>
        
        <div style={{ marginBottom: '15px' }}>
          <label>
            Pick a color: 
            <input
              type="color"
              value={selectedColor}
              onChange={(e) => setSelectedColor(e.target.value)}
              style={{ marginLeft: '10px' }}
            />
          </label>
        </div>

        <h4>Current Node Colors:</h4>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>
          {Object.entries(nodeColors).map(([nodeType, color]) => (
            <div
              key={nodeType}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '10px',
                padding: '8px',
                border: '1px solid #ddd',
                borderRadius: '4px'
              }}
            >
              <div
                style={{
                  width: '20px',
                  height: '20px',
                  background: color,
                  borderRadius: '3px',
                  border: '1px solid #ccc'
                }}
              />
              <span style={{ flex: 1 }}>{nodeType}</span>
              <button
                onClick={() => handleColorChange(nodeType)}
                style={{
                  padding: '4px 8px',
                  fontSize: '0.8em',
                  border: '1px solid #ccc',
                  borderRadius: '3px',
                  cursor: 'pointer'
                }}
              >
                Apply
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Local Storage Information */}
      <div style={{ marginBottom: '20px', padding: '15px', background: '#f0fff0', borderRadius: '8px' }}>
        <h3>Local Storage Information</h3>
        <p><strong>How it works:</strong></p>
        <ul style={{ marginLeft: '20px' }}>
          <li>Legend colors are automatically saved to localStorage when you change them</li>
          <li>{user?.id ? 
            `Colors are stored with user-specific key: kg-visualizer-legend-colors-${user.id}` :
            'Colors are stored with general key: kg-visualizer-legend-colors'
          }</li>
          <li>Colors persist across browser sessions and page reloads</li>
          <li>No cloud sync - everything is stored locally in your browser</li>
          <li>If localStorage fails, the app continues to work with in-memory settings</li>
        </ul>
      </div>

      {/* Settings Information */}
      <div style={{ padding: '15px', background: '#f8f8f8', borderRadius: '8px' }}>
        <h3>Settings Information</h3>
        <p><strong>Settings Version:</strong> {settings.version}</p>
        <p><strong>Settings Ready:</strong> {isReady ? 'Yes' : 'No'}</p>
        <p><strong>Total Node Types:</strong> {Object.keys(nodeColors).length}</p>
        
        {/* Settings JSON (collapsed by default) */}
        <details style={{ marginTop: '15px' }}>
          <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>
            View Current Settings (JSON)
          </summary>
          <pre style={{ 
            background: 'white', 
            padding: '10px', 
            borderRadius: '4px', 
            fontSize: '0.8em',
            overflow: 'auto',
            maxHeight: '300px'
          }}>
            {JSON.stringify(settings, null, 2)}
          </pre>
        </details>
      </div>
    </div>
  );
};

export default SettingsDemo;