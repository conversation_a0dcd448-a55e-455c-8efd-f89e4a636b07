import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { axe, toHaveNoViolations } from 'jest-axe';
import EnhancedSearchBar from '../EnhancedSearchBar';

expect.extend(toHaveNoViolations);

// Mock framer-motion to avoid issues with animations in tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }) => children,
}));

// Mock lodash.debounce
jest.mock('lodash.debounce', () => (fn) => fn);

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

describe('EnhancedSearchBar', () => {
  const defaultProps = {
    searchQuery: '',
    onSearchChange: jest.fn(),
    searchResults: [],
    onNodeSelect: jest.fn(),
    onCenterOnNode: jest.fn(),
  };

  const mockSearchResults = [
    {
      id: '1',
      name: 'Test Node 1',
      group: 'TestGroup',
      color: '#ff0000',
      properties: { category: 'test' },
    },
    {
      id: '2',
      name: 'Test Node 2',
      group: 'AnotherGroup',
      color: '#00ff00',
      properties: { category: 'sample' },
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  describe('Rendering', () => {
    test('renders enhanced search container', () => {
      render(<EnhancedSearchBar {...defaultProps} />);
      
      const container = document.querySelector('.enhanced-search-container');
      expect(container).toBeInTheDocument();
    });

    test('renders with custom placeholder', () => {
      render(
        <EnhancedSearchBar
          {...defaultProps}
          placeholder="Custom search placeholder"
        />
      );
      
      expect(screen.getByPlaceholderText('Custom search placeholder')).toBeInTheDocument();
    });

    test('renders autocomplete component', () => {
      render(<EnhancedSearchBar {...defaultProps} />);
      
      // Material UI Autocomplete should render a combobox
      expect(screen.getByRole('combobox')).toBeInTheDocument();
    });

    test('applies correct styling classes', () => {
      render(<EnhancedSearchBar {...defaultProps} />);
      
      const container = document.querySelector('.enhanced-search-container');
      expect(container).toHaveStyle({
        position: 'absolute',
        zIndex: '100',
      });
    });
  });

  describe('Search Functionality', () => {
    test('calls onSearchChange when typing', async () => {
      const user = userEvent.setup();
      const onSearchChange = jest.fn();
      
      render(
        <EnhancedSearchBar
          {...defaultProps}
          onSearchChange={onSearchChange}
        />
      );
      
      const input = screen.getByRole('combobox');
      await user.type(input, 'test');
      
      expect(onSearchChange).toHaveBeenCalledWith('test');
    });

    test('displays search results in dropdown', async () => {
      render(
        <EnhancedSearchBar
          {...defaultProps}
          searchResults={mockSearchResults}
        />
      );
      
      const input = screen.getByRole('combobox');
      fireEvent.focus(input);
      
      await waitFor(() => {
        expect(screen.getByText('Test Node 1')).toBeInTheDocument();
        expect(screen.getByText('Test Node 2')).toBeInTheDocument();
      });
    });

    test('calls onNodeSelect when clicking a search result', async () => {
      const user = userEvent.setup();
      const onNodeSelect = jest.fn();
      
      render(
        <EnhancedSearchBar
          {...defaultProps}
          searchResults={mockSearchResults}
          onNodeSelect={onNodeSelect}
        />
      );
      
      const input = screen.getByRole('combobox');
      fireEvent.focus(input);
      
      await waitFor(() => {
        expect(screen.getByText('Test Node 1')).toBeInTheDocument();
      });
      
      await user.click(screen.getByText('Test Node 1'));
      
      expect(onNodeSelect).toHaveBeenCalledWith(
        expect.objectContaining({
          id: '1',
          name: 'Test Node 1',
        })
      );
    });

    test('clears search when clear button is clicked', async () => {
      const user = userEvent.setup();
      const onSearchChange = jest.fn();
      
      render(
        <EnhancedSearchBar
          {...defaultProps}
          searchQuery="test query"
          onSearchChange={onSearchChange}
        />
      );
      
      const clearButton = document.querySelector('[data-testid="ClearIcon"]');
      await user.click(clearButton);
      
      expect(onSearchChange).toHaveBeenCalledWith('');
    });
  });

  describe('Recent Searches', () => {
    test('loads recent searches from localStorage', () => {
      const recentSearches = ['recent search 1', 'recent search 2'];
      localStorageMock.getItem.mockReturnValue(JSON.stringify(recentSearches));
      
      render(<EnhancedSearchBar {...defaultProps} />);
      
      expect(localStorageMock.getItem).toHaveBeenCalledWith('enhancedSearch.recentSearches');
    });

    test('saves search to recent searches', async () => {
      const user = userEvent.setup();
      
      render(<EnhancedSearchBar {...defaultProps} />);
      
      const input = screen.getByRole('combobox');
      await user.type(input, 'test search');
      
      await waitFor(() => {
        expect(localStorageMock.setItem).toHaveBeenCalledWith(
          'enhancedSearch.recentSearches',
          JSON.stringify(['test search'])
        );
      });
    });

    test('displays recent searches when input is empty', async () => {
      const recentSearches = ['recent search 1', 'recent search 2'];
      localStorageMock.getItem.mockReturnValue(JSON.stringify(recentSearches));
      
      render(
        <EnhancedSearchBar
          {...defaultProps}
          showRecentSearches={true}
        />
      );
      
      const input = screen.getByRole('combobox');
      fireEvent.focus(input);
      
      await waitFor(() => {
        expect(screen.getByText('recent search 1')).toBeInTheDocument();
        expect(screen.getByText('recent search 2')).toBeInTheDocument();
      });
    });
  });

  describe('Keyboard Navigation', () => {
    test('closes dropdown on Escape key', async () => {
      const user = userEvent.setup();
      
      render(
        <EnhancedSearchBar
          {...defaultProps}
          searchResults={mockSearchResults}
        />
      );
      
      const input = screen.getByRole('combobox');
      fireEvent.focus(input);
      
      await waitFor(() => {
        expect(screen.getByText('Test Node 1')).toBeInTheDocument();
      });
      
      await user.keyboard('{Escape}');
      
      // Dropdown should be closed
      expect(screen.queryByText('Test Node 1')).not.toBeInTheDocument();
    });

    test('supports arrow key navigation', async () => {
      const user = userEvent.setup();
      
      render(
        <EnhancedSearchBar
          {...defaultProps}
          searchResults={mockSearchResults}
        />
      );
      
      const input = screen.getByRole('combobox');
      fireEvent.focus(input);
      
      await waitFor(() => {
        expect(screen.getByText('Test Node 1')).toBeInTheDocument();
      });
      
      // Test arrow down navigation
      await user.keyboard('{ArrowDown}');
      
      // First option should be highlighted
      const firstOption = screen.getByText('Test Node 1').closest('[role="option"]');
      expect(firstOption).toHaveAttribute('aria-selected', 'true');
    });
  });

  describe('Responsive Design', () => {
    test('applies responsive styles on mobile viewport', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 480,
      });
      
      render(<EnhancedSearchBar {...defaultProps} />);
      
      const container = document.querySelector('.enhanced-search-container');
      expect(container).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    test('has no accessibility violations', async () => {
      const { container } = render(<EnhancedSearchBar {...defaultProps} />);
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('has proper ARIA labels', () => {
      render(<EnhancedSearchBar {...defaultProps} />);
      
      const input = screen.getByRole('combobox');
      expect(input).toHaveAttribute('aria-expanded', 'false');
      expect(input).toHaveAttribute('aria-autocomplete', 'list');
    });

    test('updates ARIA expanded when dropdown is open', async () => {
      render(
        <EnhancedSearchBar
          {...defaultProps}
          searchResults={mockSearchResults}
        />
      );
      
      const input = screen.getByRole('combobox');
      fireEvent.focus(input);
      
      await waitFor(() => {
        expect(input).toHaveAttribute('aria-expanded', 'true');
      });
    });

    test('supports screen readers with proper option labeling', async () => {
      render(
        <EnhancedSearchBar
          {...defaultProps}
          searchResults={mockSearchResults}
        />
      );
      
      const input = screen.getByRole('combobox');
      fireEvent.focus(input);
      
      await waitFor(() => {
        const options = screen.getAllByRole('option');
        expect(options).toHaveLength(2);
        expect(options[0]).toHaveTextContent('Test Node 1');
        expect(options[1]).toHaveTextContent('Test Node 2');
      });
    });
  });

  describe('Performance', () => {
    test('respects reduced motion preferences', () => {
      // Mock reduced motion preference
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation(query => ({
          matches: query === '(prefers-reduced-motion: reduce)',
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
        })),
      });
      
      render(<EnhancedSearchBar {...defaultProps} />);
      
      // Component should handle reduced motion properly
      expect(window.matchMedia).toHaveBeenCalledWith('(prefers-reduced-motion: reduce)');
    });

    test('debounces search input', async () => {
      const onSearchChange = jest.fn();
      const user = userEvent.setup();
      
      render(
        <EnhancedSearchBar
          {...defaultProps}
          onSearchChange={onSearchChange}
        />
      );
      
      const input = screen.getByRole('combobox');
      
      // Type quickly
      await user.type(input, 'test');
      
      // With debouncing, should only call once (mocked to immediate)
      expect(onSearchChange).toHaveBeenCalledWith('test');
    });
  });

  describe('Error Handling', () => {
    test('handles localStorage errors gracefully', () => {
      localStorageMock.getItem.mockImplementation(() => {
        throw new Error('localStorage error');
      });
      
      // Should not throw
      expect(() => {
        render(<EnhancedSearchBar {...defaultProps} />);
      }).not.toThrow();
    });

    test('handles malformed localStorage data', () => {
      localStorageMock.getItem.mockReturnValue('invalid json');
      
      // Should not throw
      expect(() => {
        render(<EnhancedSearchBar {...defaultProps} />);
      }).not.toThrow();
    });
  });

  describe('Disabled State', () => {
    test('disables input when disabled prop is true', () => {
      render(<EnhancedSearchBar {...defaultProps} disabled={true} />);
      
      const input = screen.getByRole('combobox');
      expect(input).toBeDisabled();
    });

    test('prevents interaction when disabled', async () => {
      const user = userEvent.setup();
      const onSearchChange = jest.fn();
      
      render(
        <EnhancedSearchBar
          {...defaultProps}
          disabled={true}
          onSearchChange={onSearchChange}
        />
      );
      
      const input = screen.getByRole('combobox');
      await user.type(input, 'test');
      
      expect(onSearchChange).not.toHaveBeenCalled();
    });
  });
});