import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import DocumentationReader from '../DocumentationReader';

// Mock mermaid to avoid rendering issues in tests
jest.mock('mermaid', () => ({
  initialize: jest.fn(),
  parse: jest.fn().mockResolvedValue(true),
  render: jest.fn().mockResolvedValue({
    svg: '<svg><g><text>Test Diagram</text></g></svg>'
  })
}));

describe('DocumentationReader', () => {
  const defaultProps = {
    expandedDoc: 'test-doc',
    docContent: '<h1>Test Document</h1><p>This is a test document.</p>',
    docLoading: false,
    docError: null,
    onClose: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders empty state when no document is expanded', () => {
    render(
      <DocumentationReader
        {...defaultProps}
        expandedDoc={null}
      />
    );

    expect(screen.getByText('Welcome to Documentation')).toBeInTheDocument();
    expect(screen.getByText('Select a topic from the sidebar to get started')).toBeInTheDocument();
  });

  it('renders document content correctly', () => {
    render(<DocumentationReader {...defaultProps} />);

    expect(screen.getByText('Test Document')).toBeInTheDocument();
    expect(screen.getByText('This is a test document.')).toBeInTheDocument();
  });

  it('displays loading state', () => {
    const { container } = render(
      <DocumentationReader
        {...defaultProps}
        docLoading={true}
        docContent=""
      />
    );

    expect(screen.getByText('Loading documentation...')).toBeInTheDocument();
    expect(container.querySelector('.loading-spinner')).toBeInTheDocument();
  });

  it('displays error state', () => {
    const errorMessage = 'Failed to load document';
    render(
      <DocumentationReader
        {...defaultProps}
        docLoading={false}
        docError={errorMessage}
        docContent=""
      />
    );

    expect(screen.getByText('Error Loading Documentation')).toBeInTheDocument();
    expect(screen.getByText(errorMessage)).toBeInTheDocument();
    expect(screen.getByText('Try Again')).toBeInTheDocument();
  });

  it('processes mermaid diagrams', async () => {
    const mermaid = require('mermaid');
    const contentWithMermaid = `
      <h1>Test Document</h1>
      <pre><code class="language-mermaid">
        graph TD
        A[Start] --> B[Process]
        B --> C[End]
      </code></pre>
    `;

    render(
      <DocumentationReader
        {...defaultProps}
        docContent={contentWithMermaid}
      />
    );

    await waitFor(() => {
      expect(mermaid.parse).toHaveBeenCalledWith(expect.stringContaining('graph TD'));
      expect(mermaid.render).toHaveBeenCalledWith(
        expect.stringContaining('mermaid-diagram-test-doc-'),
        expect.stringContaining('graph TD')
      );
    });
  });

  it('handles mermaid parsing errors gracefully', async () => {
    const mermaid = require('mermaid');
    mermaid.parse.mockRejectedValueOnce(new Error('Invalid syntax'));

    const contentWithInvalidMermaid = `
      <h1>Test Document</h1>
      <pre><code class="language-mermaid">
        invalid mermaid syntax
      </code></pre>
    `;

    render(
      <DocumentationReader
        {...defaultProps}
        docContent={contentWithInvalidMermaid}
      />
    );

    await waitFor(() => {
      expect(mermaid.parse).toHaveBeenCalled();
    });

    // Should not crash and should handle the error gracefully
    expect(screen.getByText('Test Document')).toBeInTheDocument();
  });

  it('generates table of contents from headings', () => {
    const contentWithHeadings = `
      <h1>Main Title</h1>
      <h2>Section 1</h2>
      <h3>Subsection 1.1</h3>
      <h2>Section 2</h2>
    `;

    render(
      <DocumentationReader
        {...defaultProps}
        docContent={contentWithHeadings}
      />
    );

    // TOC should be collapsed by default, click to expand
    const tocToggle = screen.getByLabelText('Expand table of contents');
    expect(tocToggle).toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', () => {
    const mockOnClose = jest.fn();
    render(
      <DocumentationReader
        {...defaultProps}
        onClose={mockOnClose}
      />
    );

    const closeButton = screen.getByLabelText('Close documentation');
    closeButton.click();

    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('formats document title correctly', () => {
    render(
      <DocumentationReader
        {...defaultProps}
        expandedDoc="my-test-document"
      />
    );

    expect(screen.getByText('My Test Document')).toBeInTheDocument();
  });

  it('handles multiple mermaid diagrams in one document', async () => {
    const mermaid = require('mermaid');
    const contentWithMultipleMermaid = `
      <h1>Test Document</h1>
      <pre><code class="language-mermaid">
        graph TD
        A[Start] --> B[Process]
      </code></pre>
      <p>Some content between diagrams</p>
      <pre><code class="language-mermaid">
        sequenceDiagram
        Alice->>Bob: Hello Bob
      </code></pre>
    `;

    render(
      <DocumentationReader
        {...defaultProps}
        docContent={contentWithMultipleMermaid}
      />
    );

    await waitFor(() => {
      expect(mermaid.parse).toHaveBeenCalledTimes(2);
      expect(mermaid.render).toHaveBeenCalledTimes(2);
    });
  });
});