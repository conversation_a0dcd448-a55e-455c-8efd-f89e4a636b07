import React, { useState, useEffect } from 'react';
import { useSettings } from '../hooks/useSettings';
import configStore from '../stores/configStore';
import AtlasRAGConfigPanel from './AtlasRAGConfigPanel';
// import PerformanceMetrics from './PerformanceMetrics'; // Removed during revert
import './GraphitiConfigPanel.css';

const GraphitiConfigPanel = ({ isOpen, onClose }) => {
  const { settings, set } = useSettings();
  const [ollamaModels, setOllamaModels] = useState([]);
  const [loading, setLoading] = useState(false);
  const [validationState, setValidationState] = useState({});
  const [connectionStatus, setConnectionStatus] = useState({
    ollama: null, // null, 'testing', 'success', 'error'
    azure: null, // null, 'testing', 'success', 'error'
    lastTest: null
  });
  const [showPerformanceMetrics, setShowPerformanceMetrics] = useState(false);
  // Initialize searchType based on whether Atlas RAG is enabled
  const [searchType, setSearchType] = useState(() => {
    if (settings.graphiti?.useAtlasRAG) {
      return 'atlasrag';
    }
    // Ensure we never store 'atlasrag' as the actual searchType
    return (settings.searchType === 'atlasrag') ? 'COMBINED_HYBRID_SEARCH_MMR' : (settings.searchType || 'COMBINED_HYBRID_SEARCH_CROSS_ENCODER');
  });

  // Sync searchType with searchConfigService to persist mode
  useEffect(() => {
    if (configStore) {
      configStore.setMode(searchType);
      console.log('🔧 GraphitiConfigPanel: Set search mode to:', searchType);
    }
  }, [searchType]);

  const graphitiSettings = settings.graphiti || {};

  // Validation rules for Graphiti settings
  const validateSetting = (key, value) => {
    switch (key) {
      case 'searchType':
        return ['COMBINED_HYBRID_SEARCH_CROSS_ENCODER', 'COMBINED_HYBRID_SEARCH_MMR', 'COMBINED_HYBRID_SEARCH_RRF'].includes(value) ? 'valid' : 'invalid';
      case 'edgeCount':
        return value >= 1 && value <= 20 ? 'valid' : 'invalid';
      case 'nodeCount':
        return value >= 1 && value <= 10 ? 'valid' : 'invalid';
      case 'diversityFactor':
        return value >= 0 && value <= 1 ? 'valid' : 'invalid';
      case 'temperature':
        return value >= 0 && value <= 1 ? 'valid' : 'invalid';
      case 'timeout':
        return value >= 30 && value <= 600 ? 'valid' : 'invalid';
      case 'ollamaUrl':
        try {
          new URL(value);
          return 'valid';
        } catch {
          return value === '' ? 'warning' : 'invalid';
        }
      case 'ollamaModel':
      case 'graphitiModel':
        return value && value.trim() ? 'valid' : 'warning';
      case 'azureEndpoint':
        try {
          new URL(value);
          return value.includes('openai.azure.com') ? 'valid' : 'warning';
        } catch {
          return value === '' ? 'warning' : 'invalid';
        }
      case 'azureDeploymentName':
      case 'azureApiVersion':
      case 'azureModel':
        return value && value.trim() ? 'valid' : 'warning';
      case 'llmProvider':
        return ['ollama', 'azure-openai', 'openai', 'google'].includes(value) ? 'valid' : 'warning';
      default:
        return typeof value === 'boolean' ? 'valid' : 'warning';
    }
  };

  // Get validation icon
  const getValidationIcon = (key) => {
    const status = validationState[key];
    switch (status) {
      case 'valid': 
        return (
          <span className="validation-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#00973a" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <polyline points="20 6 9 17 4 12"/>
            </svg>
          </span>
        );
      case 'warning': 
        return (
          <span className="validation-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#f59e0b" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
              <line x1="12" y1="9" x2="12" y2="13"/>
              <line x1="12" y1="17" x2="12.01" y2="17"/>
            </svg>
          </span>
        );
      case 'invalid': 
        return (
          <span className="validation-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#ef4444" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="10"/>
              <line x1="15" y1="9" x2="9" y2="15"/>
              <line x1="9" y1="9" x2="15" y2="15"/>
            </svg>
          </span>
        );
      default: return null;
    }
  };

  // Get validation class for styling
  const getValidationClass = (key) => {
    const status = validationState[key];
    return status ? `validation-${status}` : '';
  };

  // Test Ollama connection
  const testOllamaConnection = async (url) => {
    if (!url) return;

    setConnectionStatus(prev => ({ ...prev, ollama: 'testing' }));

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(`${url}/api/tags`, {
        signal: controller.signal,
        method: 'GET'
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        setConnectionStatus({
          ollama: 'success',
          lastTest: new Date().toLocaleTimeString()
        });
      } else {
        setConnectionStatus({
          ollama: 'error',
          lastTest: new Date().toLocaleTimeString()
        });
      }
    } catch (error) {
      setConnectionStatus({
        ollama: 'error',
        lastTest: new Date().toLocaleTimeString()
      });
    }
  };

  // Test Azure OpenAI connection
  const testAzureConnection = async (endpoint, deploymentName, apiVersion) => {
    if (!endpoint || !deploymentName || !apiVersion) return;

    setConnectionStatus(prev => ({ ...prev, azure: 'testing' }));

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch('/api/azure/test-azure-connection', {
        signal: controller.signal,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          endpoint,
          deploymentName,
          apiVersion
        })
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const result = await response.json();
        setConnectionStatus(prev => ({
          ...prev,
          azure: result.success ? 'success' : 'error',
          lastTest: new Date().toLocaleTimeString()
        }));
      } else {
        setConnectionStatus(prev => ({ ...prev, azure: 'error', lastTest: new Date().toLocaleTimeString() }));
      }
    } catch (error) {
      setConnectionStatus(prev => ({ ...prev, azure: 'error', lastTest: new Date().toLocaleTimeString() }));
    }
  };

  // Auto-test Ollama connection when URL changes
  useEffect(() => {
    if (graphitiSettings.llmProvider === 'ollama' && graphitiSettings.ollamaUrl) {
      const timeoutId = setTimeout(() => {
        testOllamaConnection(graphitiSettings.ollamaUrl);
      }, 500);
      return () => clearTimeout(timeoutId);
    }
  }, [graphitiSettings.ollamaUrl, graphitiSettings.llmProvider]);

  // Auto-test Azure connection when settings change
  useEffect(() => {
    if (graphitiSettings.llmProvider === 'azure-openai' &&
        graphitiSettings.azureEndpoint &&
        graphitiSettings.azureDeploymentName &&
        graphitiSettings.azureApiVersion) {
      const timeoutId = setTimeout(() => {
        testAzureConnection(
          graphitiSettings.azureEndpoint,
          graphitiSettings.azureDeploymentName,
          graphitiSettings.azureApiVersion
        );
      }, 500);
      return () => clearTimeout(timeoutId);
    }
  }, [
    graphitiSettings.azureEndpoint,
    graphitiSettings.azureDeploymentName,
    graphitiSettings.azureApiVersion,
    graphitiSettings.llmProvider
  ]);

  // Update validation state when settings change
  useEffect(() => {
    const newValidationState = {};
    Object.entries(graphitiSettings).forEach(([key, value]) => {
      newValidationState[key] = validateSetting(key, value);
    });
    setValidationState(newValidationState);
  }, [graphitiSettings]);

  // Keep unified config store in sync with panel state
  useEffect(() => {
    const payload = {
      mode: 'graphiti',
      llmProvider: graphitiSettings.llmProvider || 'ollama',
      ollamaUrl: graphitiSettings.ollamaUrl || 'http://localhost:11434',
      timeoutSeconds: graphitiSettings.timeout ?? 180,
      graphiti: {
        searchType: graphitiSettings.searchType || 'COMBINED_HYBRID_SEARCH_CROSS_ENCODER',
        edgeCount: graphitiSettings.edgeCount ?? 6,
        nodeCount: graphitiSettings.nodeCount ?? 2,
        diversityFactor: graphitiSettings.diversityFactor ?? 0.3,
        temperature: graphitiSettings.temperature ?? 0.3,
        timeout: graphitiSettings.timeout ?? 180,
        ollamaModel: graphitiSettings.ollamaModel || 'gemma3:latest',
        graphitiModel: graphitiSettings.graphitiModel || 'gemma3:latest'
      },
      azureEndpoint: graphitiSettings.azureEndpoint || undefined,
      azureDeploymentName: graphitiSettings.azureDeploymentName || undefined,
      azureApiVersion: graphitiSettings.azureApiVersion || undefined,
      azureModel: graphitiSettings.azureModel || undefined
    };

    configStore.setConfig(payload);
  }, [graphitiSettings]);

  // Seed Azure defaults when provider is Azure and required fields are missing (ensures persistence)
  useEffect(() => {
    try {
      if (graphitiSettings.llmProvider === 'azure-openai') {
        if (!graphitiSettings.azureEndpoint) set('graphiti.azureEndpoint', 'https://360t-openai-development.openai.azure.com/');
        if (!graphitiSettings.azureDeploymentName) set('graphiti.azureDeploymentName', 'gpt-4.1-2');
        if (!graphitiSettings.azureApiVersion) set('graphiti.azureApiVersion', '2024-10-21');
        if (!graphitiSettings.azureModel) set('graphiti.azureModel', 'gpt-4.1-preview');
      }
    } catch (e) {
      // no-op
    }
  }, [graphitiSettings.llmProvider, graphitiSettings.azureEndpoint, graphitiSettings.azureDeploymentName, graphitiSettings.azureApiVersion, graphitiSettings.azureModel]);

  // Available Graphiti search types from the documentation
  const searchTypes = [
    { value: 'COMBINED_HYBRID_SEARCH_CROSS_ENCODER', label: 'Hybrid Search with Cross-Encoder (Precision)' },
    { value: 'COMBINED_HYBRID_SEARCH_MMR', label: 'Hybrid Search with MMR (Diversity)' },
    { value: 'COMBINED_HYBRID_SEARCH_RRF', label: 'Hybrid Search with RRF (Relevance)' },
    // Removed VECTOR_SEARCH_ONLY and BM25_SEARCH_ONLY as they're not supported by the Python script
  ];

  const llmProviders = [
    { value: 'ollama', label: 'Ollama (Local)' },
    { value: 'azure-openai', label: 'Azure OpenAI' },
    { value: 'openai', label: 'OpenAI' },
    { value: 'google', label: 'Google Gemini' },
  ];

  // Configuration presets with modern SVG icons
  const configPresets = {
    performance: {
      name: 'Performance',
      icon: (
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"/>
        </svg>
      ),
      description: 'Fast responses with minimal resources',
      settings: {
        searchType: 'COMBINED_HYBRID_SEARCH_MMR',
        edgeCount: 3,
        nodeCount: 1,
        temperature: 0.1,
        timeout: 60,
        diversityFactor: 0.2
      }
    },
    balanced: {
      name: 'Balanced',
      icon: (
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M12 2l3 7h7l-5.5 4 2 7-6.5-5-6.5 5 2-7L2 9h7z"/>
        </svg>
      ),
      description: 'Good balance of quality and speed (default)',
      settings: {
        searchType: 'COMBINED_HYBRID_SEARCH_MMR',
        edgeCount: 6,
        nodeCount: 2,
        temperature: 0.3,
        timeout: 180,
        diversityFactor: 0.3
      }
    },
    quality: {
      name: 'High Quality',
      icon: (
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M6 3h12l4 6-10 13L2 9l4-6z"/>
        </svg>
      ),
      description: 'Maximum detail and comprehensiveness',
      settings: {
        searchType: 'COMBINED_HYBRID_SEARCH_RRF',
        edgeCount: 15,
        nodeCount: 5,
        temperature: 0.5,
        timeout: 300,
        diversityFactor: 0.4
      }
    },
    research: {
      name: 'Research',
      icon: (
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M9 11a3 3 0 1 0 6 0a3 3 0 0 0-6 0"/>
          <path d="M17.5 17.5L22 22"/>
          <path d="M15 7a3 3 0 1 0-6 0"/>
        </svg>
      ),
      description: 'Maximum diversity for exploration',
      settings: {
        searchType: 'COMBINED_HYBRID_SEARCH_MMR',
        edgeCount: 20,
        nodeCount: 10,
        temperature: 0.7,
        timeout: 600,
        diversityFactor: 0.8
      }
    },
    debug: {
      name: 'Debug',
      icon: (
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M8 6h8"/>
          <path d="M6 12h12"/>
          <path d="M8 18h8"/>
        </svg>
      ),
      description: 'Single focused result for testing',
      settings: {
        searchType: 'COMBINED_HYBRID_SEARCH_CROSS_ENCODER',
        edgeCount: 1,
        nodeCount: 1,
        temperature: 0.0,
        timeout: 30,
        diversityFactor: 0.1
      }
    }
  };

  // Fetch available Ollama models when component mounts or when Ollama is selected
  useEffect(() => {
    if (graphitiSettings.llmProvider === 'ollama') {
      fetchOllamaModels();
    }
  }, [graphitiSettings.llmProvider]);

  const fetchOllamaModels = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/ollama/models');
      if (response.ok) {
        const models = await response.json();
        // Filter out embedding models
        const chatModels = models.filter(model =>
          !model.name.includes('embed') &&
          !model.name.includes('embedding') &&
          !model.name.includes('nomic')
        );
        setOllamaModels(chatModels);
      } else {
        console.warn('Failed to fetch Ollama models');
        setOllamaModels([{ name: 'qwen3:30b-a3b-q8_0' }, { name: 'gemma3' }]); // Fallback models
      }
    } catch (error) {
      console.warn('Error fetching Ollama models:', error);
      setOllamaModels([{ name: 'qwen3:30b-a3b-q8_0' }, { name: 'gemma3' }]); // Fallback models
    } finally {
      setLoading(false);
    }
  };

  const handleSetting = (key, value) => {
    set(`graphiti.${key}`, value);

    // Immediate validation feedback
    setValidationState(prev => ({
      ...prev,
      [key]: validateSetting(key, value)
    }));
  };

  const handleSearchTypeChange = (newSearchType) => {
    setSearchType(newSearchType);

    if (newSearchType === 'atlasrag') {
      // Enable Atlas RAG mode
      set('graphiti.useAtlasRAG', true);
      // Use a valid backend-compatible search type for Atlas RAG
      set('graphiti.searchType', 'COMBINED_HYBRID_SEARCH_MMR');

      if (process.env.NODE_ENV === 'development') {
        console.log('🔄 GraphitiConfigPanel: Switching TO Atlas RAG mode');
      }
    } else if (newSearchType === 'graphiti') {
      // Switch back to Graphiti mode
      set('graphiti.useAtlasRAG', false);
      // Set to a default Graphiti searchType
      set('graphiti.searchType', 'COMBINED_HYBRID_SEARCH_CROSS_ENCODER');

      if (process.env.NODE_ENV === 'development') {
        console.log('🔄 GraphitiConfigPanel: Switching BACK TO Graphiti mode');
      }
    } else {
      // Regular Graphiti searchType change
      set('graphiti.useAtlasRAG', false);
      set('graphiti.searchType', newSearchType);

      if (process.env.NODE_ENV === 'development') {
        console.log('🔄 GraphitiConfigPanel: Setting Graphiti searchType to', newSearchType);
      }
    }
  };



  // Get connection status icon
  const getConnectionIcon = (provider = 'ollama') => {
    const status = provider === 'azure' ? connectionStatus.azure : connectionStatus.ollama;
    switch (status) {
      case 'testing':
        return (
          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#2563eb" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{display: 'inline-block', verticalAlign: 'middle', animation: 'spin 1s linear infinite'}}>
            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
            <path d="M21 3v5h-5"/>
            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
            <path d="M8 16H3v5"/>
          </svg>
        );
      case 'success':
        return (
          <svg width="12" height="12" viewBox="0 0 24 24" fill="#00973a" style={{display: 'inline-block', verticalAlign: 'middle'}}>
            <circle cx="12" cy="12" r="10"/>
          </svg>
        );
      case 'error':
        return (
          <svg width="12" height="12" viewBox="0 0 24 24" fill="#ef4444" style={{display: 'inline-block', verticalAlign: 'middle'}}>
            <circle cx="12" cy="12" r="10"/>
          </svg>
        );
      default:
        return (
          <svg width="12" height="12" viewBox="0 0 24 24" fill="#9ca3af" style={{display: 'inline-block', verticalAlign: 'middle'}}>
            <circle cx="12" cy="12" r="10"/>
          </svg>
        );
    }
  };

  // Get overall configuration health status
  const getOverallHealth = () => {
    const criticalSettings = ['llmProvider', 'searchType'];
    const warningSettings = ['edgeCount', 'nodeCount', 'temperature'];
    
    // Check for missing critical settings
    const missingCritical = criticalSettings.some(key => !graphitiSettings[key]);
    if (missingCritical) {
      return 'error';
    }
    
    // Check for missing warning settings
    const missingWarning = warningSettings.some(key => !graphitiSettings[key]);
    if (missingWarning) {
      return 'warning';
    }
    
    // Check connection status
    if (graphitiSettings.llmProvider === 'ollama' && connectionStatus.ollama === 'error') {
      return 'error';
    }
    if (graphitiSettings.llmProvider === 'azure-openai' && connectionStatus.azure === 'error') {
      return 'error';
    }
    
    // Check for connection testing in progress
    if (connectionStatus.ollama === 'testing' || connectionStatus.azure === 'testing') {
      return 'warning';
    }
    
    return 'healthy';
  };

  // Apply a configuration preset
  const applyPreset = (presetKey) => {
    const preset = configPresets[presetKey];
    if (!preset) return;

    Object.entries(preset.settings).forEach(([key, value]) => {
      handleSetting(key, value);
    });
  };

  // Detect current preset (if any)
  const getCurrentPreset = () => {
    for (const [key, preset] of Object.entries(configPresets)) {
      const isMatch = Object.entries(preset.settings).every(([settingKey, settingValue]) => {
        const currentValue = graphitiSettings[settingKey];
        return currentValue === settingValue;
      });
      if (isMatch) return key;
    }
    return 'custom';
  };

  // Quick settings toggles with SVG icons
  const quickToggles = [];

  if (!isOpen) return null;

  return (
    <div className="graphiti-config-overlay">
      <div className="graphiti-config-panel">
        <div className="config-header">
          <div className="header-content">
            <h3>
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{marginRight: '8px', display: 'inline-block', verticalAlign: 'middle'}}>
                <circle cx="11" cy="11" r="8"/>
                <path d="M21 21l-4.35-4.35"/>
              </svg>
              Search Configuration
            </h3>
            <div className="config-health">
              <span className={`health-indicator health-${getOverallHealth()}`}>
                <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor" style={{display: 'inline-block', verticalAlign: 'middle'}}>
                  {getOverallHealth() === 'healthy' ? (
                    <circle cx="12" cy="12" r="10" fill="#00973a"/>
                  ) : getOverallHealth() === 'warning' ? (
                    <circle cx="12" cy="12" r="10" fill="#f59e0b"/>
                  ) : (
                    <circle cx="12" cy="12" r="10" fill="#ef4444"/>
                  )}
                </svg>
              </span>
              <span className="health-text">
                {getOverallHealth() === 'healthy' ? 'Optimal' :
                 getOverallHealth() === 'warning' ? 'Needs Attention' : 'Invalid Settings'}
              </span>
            </div>
          </div>
          <button className="close-button" onClick={onClose}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"/>
              <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
          </button>
        </div>

        <div className="config-content">
          {/* Search Type Selector */}
          <div className="config-section">
            <label className="config-label">Search Type</label>
            <select
              value={searchType}
              onChange={(e) => handleSearchTypeChange(e.target.value)}
              className="config-select"
            >
              <option value="graphiti">Graphiti</option>
              <option value="atlasrag">Atlas RAG</option>
            </select>
            <p className="config-hint">
              Choose between Graphiti knowledge graph search or Atlas RAG semantic search
            </p>
          </div>

          {searchType === 'atlasrag' ? (
            <AtlasRAGConfigPanel onApply={onClose} />
          ) : (
            <>
          {/* Configuration Presets */}
          <div className="config-section">
            <label className="config-label">
              Quick Presets
              <span className="current-preset">
                (Current: {configPresets[getCurrentPreset()]?.name || (
                  <span style={{display: 'inline-flex', alignItems: 'center', gap: '2px'}}>
                    <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                      <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                    </svg>
                    Custom
                  </span>
                )})
              </span>
            </label>
            <div className="preset-grid">
              {Object.entries(configPresets).map(([key, preset]) => (
                <button
                  key={key}
                  onClick={() => applyPreset(key)}
                  className={`preset-button ${getCurrentPreset() === key ? 'active' : ''}`}
                  title={preset.description}
                >
                  <div style={{display: 'flex', alignItems: 'center', gap: '6px', marginBottom: '4px'}}>
                    {preset.icon}
                    <span className="preset-name">{preset.name}</span>
                  </div>
                  <span className="preset-desc">{preset.description}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Quick Toggles - Hidden when empty */}
          {quickToggles.length > 0 && (
            <div className="config-section">
              <label className="config-label">Quick Settings</label>
              <div className="quick-toggles">
                {quickToggles.map(toggle => (
                  <button
                    key={toggle.key}
                    onClick={() => handleSetting(toggle.key, !graphitiSettings[toggle.key])}
                    className={`quick-toggle ${graphitiSettings[toggle.key] !== false ? 'active' : ''} ${toggle.prominent ? 'prominent' : ''}`}
                    title={toggle.description}
                  >
                    <span className="toggle-icon">{toggle.icon}</span>
                    <span className="toggle-label">{toggle.label}</span>
                    <span className="toggle-status">
                      {graphitiSettings[toggle.key] !== false ? (
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <polyline points="20 6 9 17 4 12"/>
                        </svg>
                      ) : (
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <rect x="6" y="4" width="4" height="16"/>
                          <rect x="14" y="4" width="4" height="16"/>
                        </svg>
                      )}
                    </span>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Atlas RAG Information Panel */}
          {graphitiSettings.useAtlasRAG && (
            <div className="atlas-rag-info">
              <div className="info-header">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"/>
                  <line x1="12" y1="16" x2="12" y2="12"/>
                  <line x1="12" y1="8" x2="12.01" y2="8"/>
                </svg>
                <span className="info-title">Atlas RAG Active</span>
              </div>
              <div className="info-content">
                <p className="info-description">
                  You're now using Atlas RAG for enhanced semantic search and generation. This provides:
                </p>
                <ul className="info-features">
                  <li>Advanced HippoRAG-based semantic search</li>
                  <li>Enhanced context window management</li>
                  <li>Improved relationship understanding</li>
                  <li>Better multi-hop reasoning capabilities</li>
                </ul>
                <p className="info-note">
                  <strong>Note:</strong> Atlas RAG uses optimized models and may provide different response characteristics compared to standard Graphiti.
                </p>
              </div>
            </div>
          )}

          {/* Search Type Selection */}
          <div className="config-section">
            <label className="config-label">
              Search Type {getValidationIcon('searchType')}
            </label>
            <select
              value={graphitiSettings.searchType || 'COMBINED_HYBRID_SEARCH_CROSS_ENCODER'}
              onChange={(e) => handleSetting('searchType', e.target.value)}
              className={`config-select ${getValidationClass('searchType')}`}
            >
              {searchTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
            <p className="config-hint">
              Cross-Encoder optimizes for precision, MMR for diversity, RRF for relevance
            </p>
          </div>

          {/* LLM Configuration Group */}
          <div className="llm-config-group">
            <div className="llm-config-header">
              <h4>LLM Configuration</h4>
            </div>
            <div className="llm-config-content">
              {/* LLM Provider Selection */}
              <div className="config-section">
                <label className="config-label">LLM Provider</label>
                <select
                  value={graphitiSettings.llmProvider || 'ollama'}
                  onChange={(e) => {
                    const value = e.target.value;
                    handleSetting('llmProvider', value);
                    // Seed Azure defaults when switching to Azure provider so fields persist without manual typing
                    if (value === 'azure-openai') {
                      if (!graphitiSettings.azureEndpoint) set('graphiti.azureEndpoint', 'https://your-resource.openai.azure.com');
                      if (!graphitiSettings.azureDeploymentName) set('graphiti.azureDeploymentName', 'gpt-4.1-2');
                      if (!graphitiSettings.azureApiVersion) set('graphiti.azureApiVersion', '2024-10-21');
                      if (!graphitiSettings.azureModel) set('graphiti.azureModel', 'gpt-4.1-preview');
                    }
                  }}
                  className="config-select"
                  aria-label="Select LLM Provider"
                >
                  {llmProviders.map(provider => (
                    <option key={provider.value} value={provider.value}>
                      {provider.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Ollama Model Selection (Response Generation) */}
              {graphitiSettings.llmProvider === 'ollama' && (
                <div className="config-section">
                  <label className="config-label">
                    Ollama Model (Response) {getValidationIcon('ollamaModel')}
                  </label>
                  <select
                    value={graphitiSettings.ollamaModel || 'qwen3:30b-a3b-q8_0'}
                    onChange={(e) => handleSetting('ollamaModel', e.target.value)}
                    className={`config-select ${getValidationClass('ollamaModel')}`}
                    disabled={loading}
                  >
                    {loading ? (
                      <option>Loading models...</option>
                    ) : ollamaModels.length > 0 ? (
                      ollamaModels.map(model => (
                        <option key={model.name} value={model.name}>
                          {model.name}
                        </option>
                      ))
                    ) : (
                      <option value="qwen3:30b-a3b-q8_0">qwen3:30b-a3b-q8_0 (default)</option>
                    )}
                  </select>
                  <p className="config-hint">Model used for final answer generation</p>
                  {loading && <p className="config-hint">Fetching available models...</p>}
                </div>
              )}

              {/* Graphiti Model Selection (Internal Operations) */}
              {graphitiSettings.llmProvider === 'ollama' && (
                <div className="config-section">
                  <label className="config-label">
                    Graphiti Model (Internal) {getValidationIcon('graphitiModel')}
                  </label>
                  <select
                    value={graphitiSettings.graphitiModel || 'gemma3:latest'}
                    onChange={(e) => handleSetting('graphitiModel', e.target.value)}
                    className={`config-select ${getValidationClass('graphitiModel')}`}
                    disabled={loading}
                  >
                    {loading ? (
                      <option>Loading models...</option>
                    ) : ollamaModels.length > 0 ? (
                      ollamaModels.map(model => (
                        <option key={model.name} value={model.name}>
                          {model.name}
                        </option>
                      ))
                    ) : (
                      <option value="gemma3:latest">gemma3:latest (default)</option>
                    )}
                  </select>
                  <p className="config-hint">Model used for knowledge graph operations and search</p>
                  {loading && <p className="config-hint">Fetching available models...</p>}
                </div>
              )}

          {/* Diversity Factor (MMR only) */}
          {graphitiSettings.searchType === 'COMBINED_HYBRID_SEARCH_MMR' && (
            <div className="config-section">
              <label className="config-label">
                Diversity Factor: {graphitiSettings.diversityFactor || 0.3} {getValidationIcon('diversityFactor')}
              </label>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={graphitiSettings.diversityFactor || 0.3}
                onChange={(e) => handleSetting('diversityFactor', parseFloat(e.target.value))}
                className={`config-slider ${getValidationClass('diversityFactor')}`}
              />
              <div className="slider-labels">
                <span>Relevance</span>
                <span>Diversity</span>
              </div>
              <p className="config-hint">
                Balance between answer relevance (0.0) and result diversity (1.0)
              </p>
            </div>
          )}

          {/* Result Count Configuration */}
          <div className="config-section">
            <label className="config-label">Search Results</label>
            <div className="number-inputs">
              <div className="number-input-group">
                <label>Edges: {getValidationIcon('edgeCount')}</label>
                <input
                  type="number"
                  min="1"
                  max="20"
                  value={graphitiSettings.edgeCount || 6}
                  onChange={(e) => handleSetting('edgeCount', parseInt(e.target.value))}
                  className={`config-number ${getValidationClass('edgeCount')}`}
                />
              </div>
              <div className="number-input-group">
                <label>Nodes: {getValidationIcon('nodeCount')}</label>
                <input
                  type="number"
                  min="1"
                  max="10"
                  value={graphitiSettings.nodeCount || 2}
                  onChange={(e) => handleSetting('nodeCount', parseInt(e.target.value))}
                  className={`config-number ${getValidationClass('nodeCount')}`}
                />
              </div>
              <div className="number-input-group">
                <label>Timeout (seconds): {getValidationIcon('timeout')}</label>
                <input
                  type="number"
                  min="30"
                  max="600"
                  value={graphitiSettings.timeout || 180}
                  onChange={(e) => handleSetting('timeout', parseInt(e.target.value))}
                  className={`config-number ${getValidationClass('timeout')}`}
                />
              </div>
            </div>
            <p className="config-hint">
              Edges provide specific facts, nodes provide entity summaries. Timeout controls request duration.
            </p>
          </div>

              {/* LLM Parameters */}
              <div className="config-section">
                <label className="config-label">
                  LLM Temperature: {graphitiSettings.temperature || 0.3} {getValidationIcon('temperature')}
                </label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={graphitiSettings.temperature || 0.3}
                  onChange={(e) => handleSetting('temperature', parseFloat(e.target.value))}
                  className={`config-slider ${getValidationClass('temperature')}`}
                />
                <div className="slider-labels">
                  <span>Focused</span>
                  <span>Creative</span>
                </div>
                <p className="config-hint">
                  Controls response creativity: lower = more focused, higher = more creative
                </p>
              </div>

              {/* Ollama URL Configuration */}
              {graphitiSettings.llmProvider === 'ollama' && (
                <div className="config-section">
                  <label className="config-label">
                    Ollama URL {getValidationIcon('ollamaUrl')}
                    <span className="connection-status">
                      {getConnectionIcon('ollama')}
                      {connectionStatus.lastTest && (
                        <span className="test-time">Last test: {connectionStatus.lastTest}</span>
                      )}
                    </span>
                  </label>
                  <div className="url-input-group">
                    <input
                      type="url"
                      value={graphitiSettings.ollamaUrl || 'http://localhost:11434'}
                      onChange={(e) => handleSetting('ollamaUrl', e.target.value)}
                      className={`config-input ${getValidationClass('ollamaUrl')}`}
                      placeholder="http://localhost:11434"
                    />
                    <button
                      type="button"
                      onClick={() => testOllamaConnection(graphitiSettings.ollamaUrl)}
                      className="test-connection-btn"
                      disabled={connectionStatus.ollama === 'testing'}
                    >
                      {connectionStatus.ollama === 'testing' ? 'Testing...' : 'Test'}
                    </button>
                  </div>
                  <p className="config-hint">
                    URL of your Ollama server (default: http://localhost:11434)
                    {connectionStatus.ollama === 'success' && (
                      <span className="success-hint"> ✅ Connection successful</span>
                    )}
                    {connectionStatus.ollama === 'error' && (
                      <span className="error-hint"> ❌ Connection failed</span>
                    )}
                  </p>
                </div>
              )}

              {/* Azure OpenAI Configuration */}
              {graphitiSettings.llmProvider === 'azure-openai' && (
                <>
                  <div className="config-section">
                    <label className="config-label">
                      Azure Endpoint URL {getValidationIcon('azureEndpoint')}
                      <span className="connection-status">
                        {getConnectionIcon('azure')}
                        {connectionStatus.lastTest && (
                          <span className="test-time">Last test: {connectionStatus.lastTest}</span>
                        )}
                      </span>
                    </label>
                    <div className="url-input-group">
                      <input
                        type="url"
                        value={graphitiSettings.azureEndpoint || 'https://360t-openai-development.openai.azure.com/'}
                        onChange={(e) => handleSetting('azureEndpoint', e.target.value)}
                        className={`config-input ${getValidationClass('azureEndpoint')}`}
                        placeholder="https://360t-openai-development.openai.azure.com/"
                      />
                      <button
                        type="button"
                        onClick={() => testAzureConnection(
                          graphitiSettings.azureEndpoint,
                          graphitiSettings.azureDeploymentName,
                          graphitiSettings.azureApiVersion
                        )}
                        className="test-connection-btn"
                        disabled={connectionStatus.azure === 'testing' ||
                                 !graphitiSettings.azureEndpoint ||
                                 !graphitiSettings.azureDeploymentName ||
                                 !graphitiSettings.azureApiVersion}
                      >
                        {connectionStatus.azure === 'testing' ? 'Testing...' : 'Test'}
                      </button>
                    </div>
                    <p className="config-hint">
                      Your Azure OpenAI endpoint URL (e.g., https://360t-openai-development.openai.azure.com/)
                      {connectionStatus.azure === 'success' && (
                        <span className="success-hint"> ✅ Connection successful</span>
                      )}
                      {connectionStatus.azure === 'error' && (
                        <span className="error-hint"> ❌ Connection failed</span>
                      )}
                    </p>
                  </div>

                  <div className="config-section">
                    <label className="config-label">
                      Deployment Name {getValidationIcon('azureDeploymentName')}
                    </label>
                    <input
                      type="text"
                      value={graphitiSettings.azureDeploymentName || 'gpt-4.1-2'}
                      onChange={(e) => handleSetting('azureDeploymentName', e.target.value)}
                      className={`config-input ${getValidationClass('azureDeploymentName')}`}
                      placeholder="gpt-4.1-2"
                    />
                    <p className="config-hint">Your Azure OpenAI deployment name as configured in Azure Portal</p>
                  </div>

                  <div className="config-section">
                    <label className="config-label">
                      API Version {getValidationIcon('azureApiVersion')}
                    </label>
                    <select
                      value={graphitiSettings.azureApiVersion || '2024-12-01-preview'}
                      onChange={(e) => handleSetting('azureApiVersion', e.target.value)}
                      className={`config-select ${getValidationClass('azureApiVersion')}`}
                    >
                      <option value="2024-12-01-preview">2024-12-01-preview (Latest, GPT-4.1 Support)</option>
                      <option value="2024-10-21">2024-10-21 (Stable, Recommended)</option>
                      <option value="2025-04-01-preview">2025-04-01-preview (GPT-5 Support)</option>
                      <option value="2024-08-01-preview">2024-08-01-preview (Previous Preview)</option>
                      <option value="2024-02-15-preview">2024-02-15-preview (Legacy)</option>
                    </select>
                    <p className="config-hint">API version for Azure OpenAI requests. Use preview versions for latest features.</p>
                  </div>

                  <div className="config-section">
                    <label className="config-label">
                      Model {getValidationIcon('azureModel')}
                    </label>
                    <select
                      value={graphitiSettings.azureModel || 'gpt-4.1-preview'}
                      onChange={(e) => handleSetting('azureModel', e.target.value)}
                      className={`config-select ${getValidationClass('azureModel')}`}
                    >
                      <optgroup label="GPT-4.1 Series (Latest AI)">
                        <option value="gpt-4.1-preview">GPT-4.1-preview (Latest AI - Default)</option>
                        <option value="gpt-4.1-turbo">GPT-4.1-turbo (Performance)</option>
                      </optgroup>
                      <optgroup label="GPT-4o Series (2025 Latest)">
                        <option value="gpt-4o">GPT-4o (Latest Stable)</option>
                        <option value="gpt-4o-mini">GPT-4o-mini (Efficient)</option>
                        <option value="gpt-4o-2024-08-06">GPT-4o (2024-08-06)</option>
                        <option value="gpt-4o-mini-2024-07-18">GPT-4o-mini (2024-07-18)</option>
                      </optgroup>
                      <optgroup label="GPT-5 Series (Preview)">
                        <option value="gpt-5-preview">GPT-5-preview (Experimental)</option>
                      </optgroup>
                      <optgroup label="Legacy Models">
                        <option value="gpt-4-turbo">GPT-4-turbo (Legacy)</option>
                        <option value="gpt-35-turbo">GPT-3.5-turbo (Legacy)</option>
                      </optgroup>
                    </select>
                    <p className="config-hint">Select the Azure OpenAI model. GPT-4o series recommended for best performance.</p>
                  </div>
                </>
              )}
            </div>
          </div>

            </>
          )}
        </div>

        {/* Apply Settings Footer - Moved outside conditional to prevent duplication */}
        <div className="config-footer">
            <div className="config-actions">
              {/* <button
                className="metrics-button"
                onClick={() => setShowPerformanceMetrics(true)}
                title="View performance metrics and optimization recommendations"
              >
                📊 Performance
              </button> */}
              <button
                className="reset-button"
                onClick={() => {
                  if (graphitiSettings.useAtlasRAG) {
                    // Reset to Atlas RAG defaults
                    const defaultAtlasRAG = {
                      topN: 10,
                      damping_factor: 0.85,
                      max_hops: 3,
                      use_ollama: true,
                      ollama_model: 'llama3.2',
                      temperature: 0.7,
                      max_tokens: 2048,
                      detail_level: 'normal',
                      response_mode: 'balanced',
                      system_prompt_template: 'balanced',
                      show_graph_traversal: true,
                      show_pagerank_scores: true,
                      show_context: true,
                      show_embeddings: false
                    };
                    Object.entries(defaultAtlasRAG).forEach(([key, value]) => {
                      set(`atlasRAGSettings.${key}`, value);
                    });
                  } else {
                    // Reset to Graphiti defaults
                    const defaultGraphiti = {
                      useAtlasRAG: false,
                      searchType: 'COMBINED_HYBRID_SEARCH_CROSS_ENCODER',
                      diversityFactor: 0.3,
                      edgeCount: 6,
                      nodeCount: 2,
                      llmProvider: 'ollama',
                      ollamaModel: 'qwen3:30b-a3b-q8_0',
                      graphitiModel: 'gemma3:latest',
                      ollamaUrl: 'http://localhost:11434',
                      temperature: 0.3,
                      timeout: 180
                    };
                    Object.entries(defaultGraphiti).forEach(([key, value]) => {
                      handleSetting(key, value);
                    });
                  }
                }}
              >
                Reset to Defaults
              </button>
              <button className="apply-button" onClick={onClose}>
                Apply Settings
              </button>
            </div>
            <div className="config-status-enhanced">
              <div className="status-row">
                <span className="status-label">Configuration:</span>
                <span className={`status-value status-${getOverallHealth()}`}>
                  {graphitiSettings.useAtlasRAG ? (
                    // Show Atlas RAG configuration when active
                    <>
                      Atlas RAG • {settings.atlasRAGSettings?.detail_level || 'normal'} detail • {settings.atlasRAGSettings?.response_mode || 'balanced'} mode •
                      {settings.atlasRAGSettings?.use_ollama ? `${settings.atlasRAGSettings?.ollama_model || 'llama3.2'}` : 'Cloud LLM'}
                    </>
                  ) : (
                    // Show Graphiti configuration when active
                    <>
                      Graphiti • {graphitiSettings.searchType?.replace(/_/g, ' ')} • {graphitiSettings.llmProvider}
                      {graphitiSettings.llmProvider === 'ollama' ? ` (${graphitiSettings.ollamaModel})` : ''} •
                      {graphitiSettings.edgeCount || 6}E/{graphitiSettings.nodeCount || 2}N
                    </>
                  )}
                </span>
              </div>
              {graphitiSettings.llmProvider === 'ollama' && (
                <div className="status-row">
                  <span className="status-label">Connection:</span>
                  <span className={`status-value connection-${connectionStatus.ollama || 'unknown'}`}>
                    {connectionStatus.ollama === 'success' ? (
                      <span style={{display: 'flex', alignItems: 'center', gap: '4px'}}>
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#00973a" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <polyline points="20 6 9 17 4 12"/>
                        </svg>
                        Connected
                      </span>
                    ) : connectionStatus.ollama === 'error' ? (
                      <span style={{display: 'flex', alignItems: 'center', gap: '4px'}}>
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#ef4444" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <line x1="18" y1="6" x2="6" y2="18"/>
                          <line x1="6" y1="6" x2="18" y2="18"/>
                        </svg>
                        Failed
                      </span>
                    ) : connectionStatus.ollama === 'testing' ? (
                      <span style={{display: 'flex', alignItems: 'center', gap: '4px'}}>
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#2563eb" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{animation: 'spin 1s linear infinite'}}>
                          <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                          <path d="M21 3v5h-5"/>
                          <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                          <path d="M8 16H3v5"/>
                        </svg>
                        Testing...
                      </span>
                    ) : (
                      <span style={{display: 'flex', alignItems: 'center', gap: '4px'}}>
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="#9ca3af">
                          <circle cx="12" cy="12" r="10"/>
                        </svg>
                        Not tested
                      </span>
                    )}
                  </span>
                </div>
              )}
            </div>
          </div>

      </div>

      {/* Performance Metrics Panel - Removed during revert */}
      {/* <PerformanceMetrics
        isOpen={showPerformanceMetrics}
        onClose={() => setShowPerformanceMetrics(false)}
      /> */}
    </div>
  );
};

export default GraphitiConfigPanel;
