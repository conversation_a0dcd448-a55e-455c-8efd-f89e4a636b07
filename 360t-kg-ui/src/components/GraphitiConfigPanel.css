/* Unified Configuration Panel Design System */

/* Unified Design Tokens for Both Graphiti and Atlas RAG */
:root {
  --config-primary: var(--360t-primary);
  --config-primary-dark: #007d30;
  --config-primary-light: #f0fdf4;
  --config-secondary: #3b82f6;
  --config-secondary-light: #eff6ff;
  --config-success: #00973a;
  --config-warning: #f59e0b;
  --config-error: #ef4444;
  --config-surface: var(--360t-white);
  --config-surface-elevated: #f9fafb;
  --config-border: #e5e7eb;
  --config-border-light: #f3f4f6;
  --config-text: #111827;
  --config-text-secondary: #6b7280;
  --config-text-muted: #9ca3af;
  
  /* Spacing Scale */
  --config-space-1: 4px;
  --config-space-2: 8px;
  --config-space-3: 12px;
  --config-space-4: 16px;
  --config-space-5: 20px;
  --config-space-6: 24px;
  --config-space-8: 32px;
  
  /* Border Radius */
  --config-radius-sm: 4px;
  --config-radius-md: 6px;
  --config-radius-lg: 8px;
  --config-radius-xl: 12px;
  
  /* Shadows */
  --config-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --config-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --config-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* LLM Configuration Grouping */
.llm-config-group {
  background: #f8f9fa;
  border: 1px solid var(--config-border-light);
  border-radius: var(--config-radius-lg);
  padding: 0;
  margin-bottom: var(--config-space-5);
}

.llm-config-header {
  background: var(--360t-light-gray, #f8f9fa);
  border-bottom: 1px solid var(--config-border-light);
  padding: var(--config-space-3) var(--config-space-5);
  border-radius: var(--config-radius-lg) var(--config-radius-lg) 0 0;
}

.llm-config-header h4 {
  margin: 0;
  font-size: var(--config-text-base);
  font-weight: var(--config-font-semibold);
  color: var(--config-text);
  display: flex;
  align-items: center;
  gap: var(--config-space-2);
}


.llm-config-content {
  padding: var(--config-space-5);
}

.llm-config-group .config-section {
  margin-bottom: var(--config-space-4);
}

.llm-config-group .config-section:last-child {
  margin-bottom: 0;
}

.llm-group-title {
  font-size: var(--config-text-base);
  font-weight: var(--config-font-semibold);
  color: var(--config-text);
  margin: 0 0 var(--config-space-4) 0;
  display: flex;
  align-items: center;
  gap: var(--config-space-2);
}

.llm-group-title::before {
  content: '';
  width: 3px;
  height: 16px;
  background: linear-gradient(135deg, var(--config-primary), var(--config-primary-dark));
  border-radius: 2px;
}

/* Use utility classes for overlay and panel */
.graphiti-config-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.graphiti-config-panel {
  background: var(--360t-white);
  border-radius: var(--360t-radius-xl);
  box-shadow: var(--360t-shadow-xl);
  width: 90%;
  max-width: 850px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Responsive breakpoints for modal width */
@media (max-width: 1024px) {
  .graphiti-config-panel {
    max-width: 700px;
  }
}

@media (max-width: 768px) {
  .graphiti-config-panel {
    width: 95%;
    max-width: none;
    margin: var(--config-space-3);
  }

  .config-header {
    padding: var(--config-space-4) var(--config-space-5);
  }

  .config-content {
    padding: var(--config-space-4);
  }

  .config-footer {
    flex-direction: column;
    gap: var(--config-space-2);
    padding: var(--config-space-3) var(--config-space-4);
  }

  .config-status-enhanced {
    padding: var(--config-space-3) var(--config-space-4);
    gap: var(--config-space-1);
  }

  .status-row {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--config-space-1);
  }

  .status-label {
    min-width: auto;
    font-size: var(--config-text-xs);
  }
}

@media (max-width: 480px) {
  .graphiti-config-panel {
    width: 98%;
    margin: var(--config-space-2);
  }

  .config-header {
    padding: var(--config-space-3) var(--config-space-4);
  }

  .config-content {
    padding: var(--config-space-3);
  }

  .apply-button,
  .reset-button,
  .metrics-button,
  .test-connection-btn {
    min-height: 44px; /* Better touch target for mobile */
    padding: var(--config-space-2) var(--config-space-4);
  }

  .url-input-group {
    flex-direction: column;
    gap: var(--config-space-2);
  }

  .test-connection-btn {
    width: 100%;
    justify-content: center;
  }
}

/* Unified Grid Layout System for Better Space Utilization */
.config-content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--config-space-6);
  margin-bottom: var(--config-space-6);
}

.config-content-grid-single {
  grid-column: 1 / -1;
}

/* Grid items for better organization */
.config-grid-full {
  grid-column: 1 / -1;
}

.config-grid-left {
  grid-column: 1;
}

.config-grid-right {
  grid-column: 2;
}

/* Responsive grid behavior */
@media (max-width: 1024px) {
  .config-content-grid {
    gap: var(--config-space-4);
  }
}

@media (max-width: 768px) {
  .config-content-grid {
    grid-template-columns: 1fr;
    gap: var(--config-space-4);
  }
  
  .config-grid-full,
  .config-grid-left,
  .config-grid-right {
    grid-column: 1;
  }
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--360t-space-5) var(--360t-space-6);
  border-bottom: 1px solid var(--360t-mid-gray);
  background: var(--360t-light-gray);
  flex-shrink: 0;
}

.config-header h3 {
  margin: 0;
  font-size: var(--360t-text-lg);
  font-weight: var(--360t-font-semibold);
  color: var(--360t-text);
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: var(--360t-space-2);
}

.config-health {
  display: flex;
  align-items: center;
  gap: var(--360t-space-2);
}

.health-indicator {
  font-size: var(--360t-text-sm);
}

.health-text {
  font-size: var(--360t-text-xs);
  font-weight: var(--360t-font-medium);
}

.health-text.health-healthy {
  color: #059669;
}

.health-text.health-warning {
  color: #d97706;
}

.health-text.health-error {
  color: #dc2626;
}

.close-button {
  background: none;
  border: none;
  font-size: var(--360t-text-xl);
  cursor: pointer;
  color: var(--360t-dark-gray);
  padding: var(--360t-space-1);
  line-height: 1;
  border-radius: var(--360t-radius-sm);
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  background: var(--360t-mid-gray);
  color: var(--360t-text);
}

.config-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--360t-space-6);
  scrollbar-width: thin;
  scrollbar-color: var(--360t-primary) var(--360t-light-gray);
}

.config-content::-webkit-scrollbar {
  width: 8px;
}

.config-content::-webkit-scrollbar-track {
  background: var(--360t-light-gray);
  border-radius: 4px;
}

.config-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--360t-primary), var(--360t-primary-dark));
  border-radius: 4px;
  border: 1px solid var(--360t-light-gray);
}

.config-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--360t-primary-dark), #005920);
}

.config-section {
  margin-bottom: var(--360t-space-5);
  border: 1px solid var(--360t-mid-gray);
  border-radius: var(--360t-radius-lg);
  overflow: hidden;
}

.config-section:last-child {
  margin-bottom: 0;
}

.config-label {
  display: flex;
  align-items: center;
  gap: var(--360t-space-2);
  font-size: var(--360t-text-sm);
  font-weight: var(--360t-font-semibold);
  color: var(--360t-text);
  margin-bottom: var(--360t-space-2);
}

.config-select {
  width: 100%;
  padding: var(--360t-space-3) var(--360t-space-12) var(--360t-space-3) var(--360t-space-4);
  border: 1px solid var(--360t-mid-gray);
  border-radius: var(--360t-radius-md);
  font-family: var(--360t-font-family-primary);
  font-size: var(--360t-text-sm);
  color: var(--360t-text);
  background-color: var(--360t-white);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%234a5568' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--360t-space-3) center;
  background-repeat: no-repeat;
  background-size: 1rem;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.config-select:focus {
  outline: none;
  border-color: var(--360t-primary);
  box-shadow: 0 0 0 3px rgba(0, 151, 58, 0.1);
}

.config-select:disabled {
  background-color: var(--360t-light-gray);
  color: var(--360t-dark-gray);
  cursor: not-allowed;
}

/* Custom Radio button styles - Keep unique styles */
.radio-group {
  display: flex;
  flex-direction: column;
  gap: var(--360t-space-1);
}

.radio-label {
  display: flex;
  align-items: center;
  gap: var(--360t-space-3);
  padding: var(--360t-space-3);
  border: 1px solid var(--360t-mid-gray);
  border-radius: var(--360t-radius-lg);
  background: var(--360t-white);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.radio-label:hover {
  background: var(--360t-light-gray);
  border-color: var(--360t-primary);
  box-shadow: var(--360t-shadow-sm);
}

.radio-label input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  width: 0;
  height: 0;
}

/* Custom radio button appearance */
.radio-label::before {
  content: '';
  width: 18px;
  height: 18px;
  border: 2px solid var(--360t-mid-gray);
  border-radius: 50%;
  background: var(--360t-white);
  transition: all 0.2s ease;
  flex-shrink: 0;
  position: relative;
}

.radio-label:hover::before {
  border-color: var(--360t-primary);
}

/* Checked state */
.radio-label input[type="radio"]:checked + span {
  color: var(--360t-primary);
  font-weight: var(--360t-font-semibold);
}

.radio-label:has(input[type="radio"]:checked) {
  background: #f0fdf4;
  border-color: var(--360t-primary);
  box-shadow: 0 0 0 1px rgba(0, 151, 58, 0.1);
}

.radio-label:has(input[type="radio"]:checked)::before {
  border-color: var(--360t-primary);
  background: var(--360t-primary);
  box-shadow: inset 0 0 0 3px var(--360t-white);
}

.radio-label span {
  font-size: var(--360t-text-sm);
  color: var(--360t-text);
  transition: all 0.2s ease;
}

.number-inputs {
  display: flex;
  gap: var(--360t-space-4);
}

.number-input-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--360t-space-2);
}

.number-input-group label {
  font-size: var(--360t-text-xs);
  font-weight: var(--360t-font-medium);
  color: var(--360t-dark-gray);
}

.config-number {
  width: 100%;
  padding: var(--360t-space-2) var(--360t-space-3);
  border: 1px solid var(--360t-mid-gray);
  border-radius: var(--360t-radius-md);
  font-family: var(--360t-font-family-primary);
  font-size: var(--360t-text-sm);
  color: var(--360t-text);
  background-color: var(--360t-white);
  transition: all 0.2s ease;
  box-sizing: border-box;
  text-align: center;
}

.config-number:focus {
  outline: none;
  border-color: var(--360t-primary);
  box-shadow: 0 0 0 3px rgba(0, 151, 58, 0.1);
}

.config-hint {
  font-size: var(--360t-text-xs);
  color: var(--360t-dark-gray);
  margin-top: var(--360t-space-1);
}

/* Slider Styles - Keep unique styling */
.config-slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: var(--360t-mid-gray);
  outline: none;
  -webkit-appearance: none;
  cursor: pointer;
  margin: var(--360t-space-2) 0;
}

.config-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--360t-primary);
  cursor: pointer;
  box-shadow: var(--360t-shadow-sm);
  transition: all 0.2s;
}

.config-slider::-webkit-slider-thumb:hover {
  background: var(--360t-primary-dark);
  transform: scale(1.1);
}

.config-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--360t-primary);
  cursor: pointer;
  border: none;
  box-shadow: var(--360t-shadow-sm);
  transition: all 0.2s;
}

.config-slider::-moz-range-thumb:hover {
  background: var(--360t-primary-dark);
  transform: scale(1.1);
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: var(--360t-space-1);
  font-size: var(--360t-text-xs);
  color: var(--360t-dark-gray);
}

.config-input {
  width: 100%;
  padding: var(--360t-space-3) var(--360t-space-4);
  border: 1px solid var(--360t-mid-gray);
  border-radius: var(--360t-radius-md);
  font-family: var(--360t-font-family-primary);
  font-size: var(--360t-text-sm);
  color: var(--360t-text);
  background-color: var(--360t-white);
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.config-input:focus {
  outline: none;
  border-color: var(--360t-primary);
  box-shadow: 0 0 0 3px rgba(0, 151, 58, 0.1);
}

/* Checkbox Styles */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: var(--360t-space-2);
  margin-top: var(--360t-space-3);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--360t-space-2);
  cursor: pointer;
  padding: var(--360t-space-2) 0;
  font-size: var(--360t-text-sm);
  color: var(--360t-text);
}

.checkbox-label input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--360t-primary);
  cursor: pointer;
}

/* Button Styles */
.config-actions {
  display: flex;
  gap: var(--360t-space-3);
  margin-bottom: var(--360t-space-3);
}

.reset-button, .apply-button, .metrics-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--config-space-3) var(--config-space-6);
  border: 1px solid transparent;
  border-radius: var(--config-radius-md);
  font-family: var(--360t-font-family-primary);
  font-size: var(--360t-text-sm);
  font-weight: var(--360t-font-medium);
  line-height: 1;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  white-space: nowrap;
  min-height: 38px;
}

.metrics-button {
  background: var(--config-warning);
  border: 1px solid var(--config-warning);
  color: white;
}

.metrics-button:hover {
  background: #d97706;
  border-color: #d97706;
  transform: translateY(-1px);
  box-shadow: var(--config-shadow-md);
}

.reset-button {
  background-color: var(--config-surface);
  color: var(--config-text);
  border: 1px solid var(--config-border);
}

.reset-button:hover:not(:disabled) {
  background-color: var(--config-surface-elevated);
  border-color: var(--config-text-secondary);
  transform: translateY(-1px);
  box-shadow: var(--config-shadow-sm);
}

.apply-button {
  background: linear-gradient(135deg, var(--config-primary), var(--config-primary-dark));
  color: white;
  box-shadow: var(--config-shadow-md);
  border-color: var(--config-primary);
  position: relative;
  overflow: hidden;
}

.apply-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.apply-button:hover::before {
  left: 100%;
}

.apply-button:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--config-primary-dark), #006925);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -5px rgba(0, 151, 58, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 20px rgba(0, 151, 58, 0.2);
}

.apply-button:active {
  transform: translateY(-1px);
  box-shadow: var(--config-shadow-md);
}

.config-footer {
  display: flex;
  gap: var(--360t-space-3);
  padding: var(--360t-space-4) var(--360t-space-6);
  border-top: 1px solid var(--360t-mid-gray);
  background: var(--360t-light-gray);
  flex-shrink: 0;
}

.config-status {
  margin: 0;
  font-size: var(--360t-text-xs);
  color: var(--360t-dark-gray);
  font-style: italic;
}

/* Validation states */
.validation-valid {
  border-color: var(--360t-primary) !important;
}

.validation-warning {
  border-color: #f59e0b !important;
}

.validation-invalid {
  border-color: #ef4444 !important;
}

.validation-valid:focus {
  box-shadow: 0 0 0 3px rgba(0, 151, 58, 0.1) !important;
}

.validation-warning:focus {
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1) !important;
}

.validation-invalid:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

/* Connection status */
.connection-status {
  display: flex;
  align-items: center;
  gap: var(--360t-space-2);
  margin-left: var(--360t-space-2);
  font-size: var(--360t-text-xs);
  color: var(--360t-dark-gray);
}

.test-time {
  font-size: var(--360t-text-xs);
  color: var(--360t-dark-gray);
  opacity: 0.8;
}

/* URL input with test button */
.url-input-group {
  display: flex;
  gap: var(--360t-space-2);
  align-items: stretch;
}

.url-input-group .config-input {
  flex: 1;
}

.test-connection-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--360t-space-2) var(--360t-space-4);
  background-color: var(--360t-primary);
  color: white;
  border: none;
  border-radius: var(--360t-radius-md);
  font-family: var(--360t-font-family-primary);
  font-size: var(--360t-text-sm);
  font-weight: var(--360t-font-medium);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
}

.test-connection-btn:hover:not(:disabled) {
  background-color: var(--360t-primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--360t-shadow-md);
}

.test-connection-btn:disabled {
  background: var(--360t-dark-gray);
  border-color: var(--360t-dark-gray);
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
}

/* Enhanced status hints */
.success-hint {
  color: #059669;
  font-weight: var(--360t-font-medium);
}

.error-hint {
  color: #dc2626;
  font-weight: var(--360t-font-medium);
}

/* Enhanced status footer */
.config-status-enhanced {
  display: flex;
  flex-direction: column;
  gap: var(--360t-space-2);
}

.status-row {
  display: flex;
  align-items: center;
  gap: var(--360t-space-2);
  font-size: var(--360t-text-xs);
}

.status-label {
  font-weight: var(--360t-font-semibold);
  color: var(--360t-text);
  min-width: 80px;
}

.status-value {
  font-family: var(--360t-font-family-mono);
  font-size: var(--360t-text-xs);
  padding: var(--360t-space-1) var(--360t-space-2);
  border-radius: var(--360t-radius-sm);
  background: var(--360t-light-gray);
}

.status-healthy {
  color: #059669;
  background: #ecfdf5;
}

.status-warning {
  color: #d97706;
  background: #fffbeb;
}

.status-error {
  color: #dc2626;
  background: #fef2f2;
}

.connection-success {
  color: #059669;
  background: #ecfdf5;
}

.connection-error {
  color: #dc2626;
  background: #fef2f2;
}

.connection-testing {
  color: #2563eb;
  background: #eff6ff;
}

.connection-unknown {
  color: var(--360t-dark-gray);
  background: var(--360t-light-gray);
}

/* Configuration Presets */
.current-preset {
  font-weight: var(--360t-font-normal);
  font-size: var(--360t-text-xs);
  color: var(--360t-dark-gray);
  margin-left: var(--360t-space-2);
}

.preset-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--360t-space-3);
  margin-top: var(--360t-space-2);
}

.preset-button {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: var(--360t-space-3);
  border: 2px solid var(--360t-mid-gray);
  border-radius: var(--360t-radius-lg);
  background: var(--360t-white);
  cursor: pointer;
  transition: all 0.2s;
  text-align: left;
}

.preset-button:hover {
  border-color: var(--360t-primary);
  box-shadow: var(--360t-shadow-sm);
}

.preset-button.active {
  border-color: var(--360t-primary);
  background: #f0fdf4;
  box-shadow: 0 0 0 1px rgba(0, 151, 58, 0.1);
}

.preset-name {
  font-size: var(--360t-text-sm);
  font-weight: var(--360t-font-semibold);
  color: var(--360t-text);
  margin-bottom: var(--360t-space-1);
}

.preset-desc {
  font-size: var(--360t-text-xs);
  color: var(--360t-dark-gray);
  line-height: 1.3;
}

.preset-button.active .preset-name {
  color: var(--360t-primary);
}

.preset-button.active .preset-desc {
  color: var(--360t-primary);
}

/* Quick Toggles */
.quick-toggles {
  display: flex;
  gap: var(--360t-space-2);
  flex-wrap: wrap;
}

.quick-toggle {
  display: flex;
  align-items: center;
  gap: var(--360t-space-2);
  padding: var(--360t-space-2) var(--360t-space-3);
  border: 1px solid var(--360t-mid-gray);
  border-radius: var(--360t-radius-md);
  background: var(--360t-white);
  cursor: pointer;
  transition: all 0.2s;
  font-size: var(--360t-text-xs);
}

.quick-toggle:hover {
  border-color: var(--360t-dark-gray);
  background: var(--360t-light-gray);
}

.quick-toggle.active {
  border-color: var(--360t-primary);
  background: #ecfdf5;
}

/* Prominent toggle styling for Atlas RAG */
.quick-toggle.prominent {
  border: 2px solid #3b82f6;
  background: #eff6ff;
  font-weight: var(--360t-font-semibold);
  position: relative;
}

.quick-toggle.prominent:hover {
  border-color: #2563eb;
  background: #dbeafe;
}

.quick-toggle.prominent.active {
  border-color: #1d4ed8;
  background: #1e40af;
  color: white;
}

.quick-toggle.prominent.active .toggle-label {
  color: white;
}

.quick-toggle.prominent.active .toggle-icon {
  color: white;
}

.quick-toggle.prominent::before {
  content: 'NEW';
  position: absolute;
  top: -6px;
  right: -6px;
  background: #ef4444;
  color: white;
  font-size: 9px;
  font-weight: 700;
  padding: 2px 4px;
  border-radius: 4px;
  line-height: 1;
}

.toggle-icon {
  font-size: var(--360t-text-sm);
}

.toggle-label {
  font-weight: var(--360t-font-medium);
  color: var(--360t-text);
}

.toggle-status {
  font-size: var(--360t-text-xs);
}

.quick-toggle.active .toggle-label {
  color: #059669;
}

/* Atlas RAG Information Panel */
.atlas-rag-info {
  margin-top: var(--360t-space-4);
  padding: var(--360t-space-4);
  background: #eff6ff;
  border: 1px solid #3b82f6;
  border-radius: var(--360t-radius-lg);
  font-size: var(--360t-text-sm);
}

.info-header {
  display: flex;
  align-items: center;
  gap: var(--360t-space-2);
  margin-bottom: var(--360t-space-3);
}

.info-title {
  font-weight: var(--360t-font-semibold);
  color: #1e40af;
}

.info-content {
  color: #1e3a8a;
}

.info-description {
  margin: 0 0 var(--360t-space-3) 0;
  line-height: 1.5;
}

.info-features {
  margin: 0 0 var(--360t-space-3) 0;
  padding-left: var(--360t-space-4);
}

.info-features li {
  margin-bottom: var(--360t-space-1);
  line-height: 1.4;
}

.info-note {
  margin: 0;
  padding: var(--360t-space-2) var(--360t-space-3);
  background: #dbeafe;
  border-radius: var(--360t-radius-md);
  font-size: var(--360t-text-xs);
  line-height: 1.4;
}

.info-note strong {
  color: #1d4ed8;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .graphiti-config-panel {
    background: #1f2937;
    color: #f9fafb;
  }

  .config-header {
    background: #111827;
    border-color: #374151;
  }

  .config-header h3 {
    color: #f9fafb;
  }

  .close-button {
    color: #9ca3af;
  }

  .close-button:hover {
    background: #374151;
    color: #d1d5db;
  }

  .config-label {
    color: #d1d5db;
  }

  .config-select {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }

  .config-select:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
  }

  .config-select:disabled {
    background: #4b5563;
    color: #9ca3af;
  }

  .radio-label {
    background: #374151;
    border-color: #4b5563;
  }

  .radio-label:hover {
    background: #4b5563;
    border-color: var(--360t-primary);
    box-shadow: 0 2px 4px rgba(0, 151, 58, 0.1);
  }

  .radio-label::before {
    background: #374151;
    border-color: #6b7280;
  }

  .radio-label:hover::before {
    border-color: var(--360t-primary);
  }

  .radio-label:has(input[type="radio"]:checked) {
    background: #064e3b;
    border-color: var(--360t-primary);
    box-shadow: 0 0 0 1px rgba(0, 151, 58, 0.1);
  }

  .radio-label:has(input[type="radio"]:checked)::before {
    border-color: var(--360t-primary);
    background: var(--360t-primary);
    box-shadow: inset 0 0 0 3px #374151;
  }

  .radio-label input[type="radio"]:checked + span {
    color: var(--360t-primary);
  }

  .radio-label span {
    color: #d1d5db;
  }

  .number-input-group label {
    color: #9ca3af;
  }

  .config-number {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }

  .config-number:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
  }

  .config-hint {
    color: #9ca3af;
  }

  .config-footer {
    background: #111827;
    border-color: #374151;
  }

  .config-status {
    color: #9ca3af;
  }

  .connection-status {
    color: #9ca3af;
  }

  .test-time {
    color: #6b7280;
  }

  .test-connection-btn {
    background: #3b82f6;
    border-color: #3b82f6;
  }

  .test-connection-btn:hover:not(:disabled) {
    background: #2563eb;
    border-color: #2563eb;
  }

  .test-connection-btn:disabled {
    background: #6b7280;
    border-color: #6b7280;
  }

  .status-label {
    color: #d1d5db;
  }

  .status-value {
    background: #374151;
    color: #d1d5db;
  }

  .status-healthy {
    color: var(--360t-primary);
    background: #064e3b;
  }

  .status-warning {
    color: #f59e0b;
    background: #451a03;
  }

  .status-error {
    color: #ef4444;
    background: #450a0a;
  }

  .connection-success {
    color: var(--360t-primary);
    background: #064e3b;
  }

  .connection-error {
    color: #ef4444;
    background: #450a0a;
  }

  .connection-testing {
    color: #60a5fa;
    background: #1e3a8a;
  }

  .connection-unknown {
    color: #9ca3af;
    background: #374151;
  }

  .config-slider {
    background: #4b5563;
  }

  .config-slider::-webkit-slider-thumb {
    background: #60a5fa;
  }

  .config-slider::-webkit-slider-thumb:hover {
    background: #3b82f6;
  }

  .config-slider::-moz-range-thumb {
    background: #60a5fa;
  }

  .config-slider::-moz-range-thumb:hover {
    background: #3b82f6;
  }

  .slider-labels {
    color: #9ca3af;
  }

  .config-input {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }

  .config-input:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
  }

  .checkbox-label {
    color: #d1d5db;
  }

  .checkbox-label input[type="checkbox"] {
    accent-color: #60a5fa;
  }

  .reset-button {
    background: #374151;
    border-color: #4b5563;
    color: #d1d5db;
  }

  .reset-button:hover {
    background: #4b5563;
    border-color: #6b7280;
  }

  .apply-button {
    background: #3b82f6;
    border-color: #3b82f6;
  }

  .apply-button:hover {
    background: #2563eb;
    border-color: #2563eb;
  }

  .current-preset {
    color: #9ca3af;
  }

  .preset-button {
    background: #374151;
    border-color: #4b5563;
  }

  .preset-button:hover {
    border-color: #60a5fa;
  }

  .preset-button.active {
    border-color: #60a5fa;
    background: #1e3a8a;
  }

  .preset-name {
    color: #d1d5db;
  }

  .preset-desc {
    color: #9ca3af;
  }

  .preset-button.active .preset-name {
    color: #60a5fa;
  }

  .preset-button.active .preset-desc {
    color: #93c5fd;
  }

  .quick-toggle {
    background: #374151;
    border-color: #4b5563;
  }

  .quick-toggle:hover {
    border-color: #6b7280;
    background: #4b5563;
  }

  .quick-toggle.active {
    border-color: var(--360t-primary);
    background: #064e3b;
  }

  .toggle-label {
    color: #d1d5db;
  }

  .quick-toggle.active .toggle-label {
    color: var(--360t-primary);
  }

  .quick-toggle.prominent {
    border-color: #60a5fa;
    background: #1e3a8a;
    color: #e0e7ff;
  }

  .quick-toggle.prominent:hover {
    border-color: #3b82f6;
    background: #1e40af;
  }

  .quick-toggle.prominent.active {
    border-color: #2563eb;
    background: #1d4ed8;
    color: white;
  }

  .quick-toggle.prominent.active .toggle-label {
    color: white;
  }

  .quick-toggle.prominent.active .toggle-icon {
    color: white;
  }

  .atlas-rag-info {
    background: #1e3a8a;
    border-color: #60a5fa;
  }

  .info-title {
    color: #93c5fd;
  }

  .info-content {
    color: #e0e7ff;
  }

  .info-note {
    background: #1e40af;
    color: #e0e7ff;
  }

  .info-note strong {
    color: #93c5fd;
  }
}

/* Enhanced Animations and Visual Polish */
@keyframes fadeInOverlay {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInModal {
  from {
    opacity: 0;
    transform: scale(0.96);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

@keyframes pulseGlow {
  0%, 100% { 
    box-shadow: var(--config-shadow-md);
    transform: scale(1);
  }
  50% { 
    box-shadow: var(--config-shadow-lg), 0 0 20px rgba(0, 151, 58, 0.2);
    transform: scale(1.02);
  }
}

@keyframes floatUp {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
