import React, { useC<PERSON>back, useMemo, useRef, useEffect, useState, useImperativeHandle } from 'react';
import ForceGraph3D from 'react-force-graph-3d';
import SpriteText from 'three-spritetext';
import * as THREE from 'three';
import neo4jService from '../services/neo4jService';
import { smartLimitGraphNodes } from '../utils/graphLimiting';
import { 
  TECHNICAL_TYPE_COLORS,
  DEFAULT_3D_NODE_SIZES,
  DEFAULT_3D_NODE_SHAPES,
  DEFAULT_RELATIONSHIP_COLORS
} from '../constants/graphDefaults.js';
import {
  getNodeType,
  getNodeName,
  getNodeColor,
  getNodeSize,
  getNodeShape,
  getLinkColor,
  getNodeTooltip
} from '../utils/nodeUtils.js';

// Default configurations from centralized constants
const defaultNodeColors = TECHNICAL_TYPE_COLORS;
const defaultNodeSizes = DEFAULT_3D_NODE_SIZES;
const defaultNodeShapes = DEFAULT_3D_NODE_SHAPES;
const defaultRelationshipColors = DEFAULT_RELATIONSHIP_COLORS;

/**
 * Unified 3D Graph Wrapper component using React Force Graph 3D
 * Provides 3D visualization with camera controls and WebGL rendering
 * Maintains API compatibility with UnifiedGraphWrapper for seamless mode switching
 */
const Unified3DGraphWrapper = React.forwardRef(({
  data,
  selectedNode, // Accept selectedNode as prop from parent
  onNodeSelect,
  onRelationshipSelect = () => {}, // Callback for relationship selection
  customConfig = {},
  nodeColors = defaultNodeColors,
  nodeSizes = defaultNodeSizes,
  onCenterOnNodeReady = () => {}, // Callback to provide centerOnNode function to parent
  onNodeClick = () => {}, // Callback for additional node click handling (e.g., close search dropdown)
  nodeShapes = defaultNodeShapes,
  relationshipColors = defaultRelationshipColors,
  relationshipLineStyles = {},
  relationshipTypeFilter = null, // Array of relationship types to show, null means show all
  isDetailView = false, // Flag to indicate if we're in detailed view mode
  // 3D control props from parent
  nodeSize3D = 2,
  onEscape = () => {}, // Callback for escape key handling to return to 2D mode
  onNodeExpand = () => {} // Callback for node expansion to update parent data
}, ref) => {
  // Debug logging for prop verification
  console.log('🔧 3D Graph Props Debug:', {
    nodeColorsReceived: nodeColors !== defaultNodeColors,
    nodeColorsKeys: Object.keys(nodeColors),
    customConfigKeys: Object.keys(customConfig),
    sampleColors: {
      Module: nodeColors.Module,
      Product: nodeColors.Product,
      Default: nodeColors.Default
    }
  });

  // Verify color application (development only)
  if (import.meta.env.DEV) {
    console.log('🎨 3D Color System Verification:', {
      defaultColors: defaultNodeColors,
      propsColors: nodeColors,
      colorValidation: {
        Module: {
          default: defaultNodeColors.Module,
          props: nodeColors.Module,
          valid: /^#[0-9A-F]{6}$/i.test(nodeColors.Module || '')
        },
        Product: {
          default: defaultNodeColors.Product,
          props: nodeColors.Product,
          valid: /^#[0-9A-F]{6}$/i.test(nodeColors.Product || '')
        }
      }
    });
  }

  const fgRef = useRef();
  // Note: selectedNode is now received as prop from parent instead of internal state
  // const [selectedNode, setSelectedNode] = useState(null);
  const selectedNodeRef = useRef(null);

  // Expose only safe methods to parent components via ref
  // DO NOT spread fgRef.current as it breaks Three.js WebGL context management
  useImperativeHandle(ref, () => ({
    // Expose essential Three.js methods safely with null checks
    d3Force: (...args) => {
      if (fgRef.current?.d3Force) {
        return fgRef.current.d3Force(...args);
      }
    },
    d3ReheatSimulation: () => {
      if (fgRef.current?.d3ReheatSimulation && !isForceUpdatingRef.current) {
        // Prevent concurrent reheat calls that cause WebGL context loss
        isForceUpdatingRef.current = true;
        try {
          fgRef.current.d3ReheatSimulation();
        } finally {
          // Reset flag after a brief delay
          setTimeout(() => {
            isForceUpdatingRef.current = false;
          }, 100);
        }
      }
    },
    graph2ScreenCoords: (...args) => {
      if (fgRef.current?.graph2ScreenCoords) {
        return fgRef.current.graph2ScreenCoords(...args);
      }
    },
    cameraPosition: (...args) => {
      if (fgRef.current?.cameraPosition) {
        return fgRef.current.cameraPosition(...args);
      }
    },
    // Custom force updating method
    setForceUpdating: (updating) => {
      console.log(`🔧 3D Graph: Force updating state changed to: ${updating}`);
      isForceUpdatingRef.current = updating;
    }
  }), []);
  const [renderStats, setRenderStats] = useState({ fps: 0, nodes: 0, links: 0 });
  const [expandedNodes, setExpandedNodes] = useState(new Set());
  const [isExpanding, setIsExpanding] = useState(false);

  // Use props for 3D control values instead of internal state
  const nodeSize = nodeSize3D;



  // Context menu state
  const [contextMenu, setContextMenu] = useState({
    visible: false,
    x: 0,
    y: 0,
    node: null
  });

  // Helper functions matching UnifiedGraphWrapper patterns
  // UPDATED: Use node.properties.category for business-relevant node styling
  // with fallback to existing node type logic for backward compatibility
  // Using centralized utility functions


  // Enhanced tooltip using centralized utility
  const getNodeTooltipForComponent = useCallback((node) => {
    // Priority order: summary > standard tooltip
    const summary = node.properties?.summary || node.data?.properties?.summary;
    if (summary && summary.trim()) {
      return summary;
    }
    
    // Use centralized tooltip generator
    return getNodeTooltip(node);
  }, []);

  const getNodeColorForComponent = useCallback((node) => {
    const color = getNodeColor(node, { nodeColors });
    
    // Debug logging for color verification
    if (Math.random() < 0.01) { // Log 1% of nodes to avoid spam
      console.log(`🎨 3D Node Color Debug: ${getNodeType(node)} → ${color}`);
    }

    return color;
  }, [nodeColors]);

  const getNodeSizeForComponent = useCallback((node) => {
    return getNodeSize(node, true, { nodeSizes }); // true for 3D mode
  }, [nodeSizes]);

  const getLinkColorForComponent = useCallback((link) => {
    return getLinkColor(link, { relationshipColors });
  }, [relationshipColors]);

  // Node expansion functionality
  const expandNode = useCallback(async (nodeId) => {
    if (expandedNodes.has(nodeId) || isExpanding) return;

    setIsExpanding(true);
    try {
      console.log(`🔄 Expanding node: ${nodeId}`);
      const expandedData = await neo4jService.expandNode(nodeId);

      if (expandedData && expandedData.nodes && expandedData.edges) {
        // Mark node as expanded
        setExpandedNodes(prev => new Set([...prev, nodeId]));

        // Merge expanded data with existing data
        const existingNodeIds = new Set(data.nodes.map(node => node.id));
        const existingEdgeIds = new Set(data.links.map(edge => edge.id));

        const newNodes = expandedData.nodes.filter(node => !existingNodeIds.has(node.id));
        const newEdges = expandedData.edges.filter(edge => !existingEdgeIds.has(edge.id));

        if (newNodes.length > 0 || newEdges.length > 0) {
          // Notify parent component of expansion
          onNodeExpand({
            nodes: newNodes,
            edges: newEdges.map(edge => ({
              ...edge,
              source: edge.source,
              target: edge.target
            }))
          });

          console.log(`✅ Node expanded: Added ${newNodes.length} new nodes and ${newEdges.length} new edges`);
        }
      }
    } catch (error) {
      console.error('Error expanding node:', error);
    } finally {
      setIsExpanding(false);
    }
  }, [data, expandedNodes, isExpanding, onNodeExpand]);

  // Pre-calculate connection counts for performance (moved before forceGraphData)
  const connectionCounts = useMemo(() => {
    if (!data || !data.links) return new Map();
    
    const counts = new Map();
    const linkArray = data.links || data.relationships || [];
    
    linkArray.forEach(link => {
      const sourceId = (link.source?.id || link.source || link.startNodeId || link.from)?.toString();
      const targetId = (link.target?.id || link.target || link.endNodeId || link.to)?.toString();
      
      if (sourceId) counts.set(sourceId, (counts.get(sourceId) || 0) + 1);
      if (targetId) counts.set(targetId, (counts.get(targetId) || 0) + 1);
    });
    
    return counts;
  }, [data]);

  // Calculate dynamic node size based on connections (moved before forceGraphData)
  const getNodeSizeWithConnections = useCallback((node) => {
    const baseSize = getNodeSizeForComponent(node);
    const connectionCount = connectionCounts.get(node.id) || 0;

    // 1 connection = default size, 10% increase for each additional connection
    // Maximum size limit to prevent oversized nodes (max 3x original size)
    const sizeMultiplier = Math.min(1 + Math.max(0, connectionCount - 1) * 0.1, 3.0);
    return baseSize * sizeMultiplier;
  }, [getNodeSizeForComponent, connectionCounts]);

  // Transform data for React Force Graph 3D format with performance optimizations
  const forceGraphData = useMemo(() => {
    const startTime = performance.now();
    console.log('🔄 Starting 3D graph data transformation...');

    if (!data || !data.nodes || !data.links) {
      console.log('❌ No data available, returning empty 3D graph');
      return { nodes: [], links: [] };
    }

    // Apply stricter node limit for 3D performance using smart limiting
    const nodeLimit3D = customConfig.nodeLimit3D || 1000;
    const nodeLimit = customConfig.nodeLimit || 0;
    const effectiveLimit = nodeLimit3D > 0 ? Math.min(nodeLimit3D, nodeLimit > 0 ? nodeLimit : nodeLimit3D) : nodeLimit;
    
    const limitedData = effectiveLimit > 0 && data.nodes.length > effectiveLimit
      ? smartLimitGraphNodes(data, effectiveLimit)
      : { nodes: data.nodes, links: data.links || [], statistics: { wasLimited: false } };
    
    const limitedNodes = limitedData.nodes;

    // Apply node type visibility filtering (same as 2D graph)
    const nodeTypeVisibility = customConfig.nodeTypeVisibility || {};
    const visibilityFilteredNodes = limitedNodes.filter(node => {
      const nodeType = getNodeType(node);
      // If visibility is not set for this node type, default to visible (true)
      const isVisible = nodeTypeVisibility[nodeType] !== false;
      if (!isVisible) {
        console.log(`🔍 3D: Filtering out node type: ${nodeType}, node: ${node.id}`);
      }
      return isVisible;
    });

    console.log(`🔍 3D Node visibility filtering: ${data.nodes.length} → ${limitedNodes.length} (after limit) → ${visibilityFilteredNodes.length} (after visibility filter)`);

    const transformedNodes = visibilityFilteredNodes.map((node, index) => {
      const nodeType = getNodeType(node);
      const nodeColor = getNodeColorForComponent(node);

      // Debug color application for first few nodes
      if (index < 5) {
        console.log(`🎨 3D Node Color Embedding for ${node.id}:`, {
          nodeType,
          nodeColor,
          isValidHex: /^#[0-9A-F]{6}$/i.test(nodeColor),
          nodeId: node.id,
          originalNode: node
        });
      }

      return {
        id: node.id?.toString(),
        name: getNodeName(node),
        group: nodeType,
        color: nodeColor, // Direct color embedding - this is key!
        val: 1, // Fixed size of 1 for all nodes
        data: node // Preserve original data
      };
    });

    // Create a set of valid node IDs for link filtering (matching UnifiedGraphWrapper pattern)
    const validNodeIds = new Set(transformedNodes.map(node => node.id));

    // Handle both 'links' and 'relationships' arrays for compatibility
    const linkArray = limitedData.links || data.relationships || [];

    // Filter links to only include those connecting visible nodes and matching relationship type filter
    const filteredLinks = linkArray.filter(link => {
      // Handle multiple possible source/target field names
      const sourceId = (link.source?.id || link.source || link.startNodeId || link.from)?.toString();
      const targetId = (link.target?.id || link.target || link.endNodeId || link.to)?.toString();
      
      // Basic validation: must have valid source and target
      if (!validNodeIds.has(sourceId) || !validNodeIds.has(targetId)) {
        return false;
      }
      
      // Apply relationship type filtering if specified
      if (relationshipTypeFilter && Array.isArray(relationshipTypeFilter)) {
        const linkType = link.type || link.label || 'RELATES_TO'; // Default to RELATES_TO if no type
        return relationshipTypeFilter.includes(linkType);
      }
      
      return true;
    });

    const transformedLinks = filteredLinks.map(link => ({
      source: (link.source?.id || link.source || link.startNodeId || link.from)?.toString(),
      target: (link.target?.id || link.target || link.endNodeId || link.to)?.toString(),
      label: link.label || link.type || '',
      type: link.type || link.label || 'RELATES_TO', // Preserve relationship type
      color: getLinkColorForComponent(link),
      data: link // Preserve original data
    }));

    const result = {
      nodes: transformedNodes,
      links: transformedLinks
    };

    const endTime = performance.now();
    const transformTime = endTime - startTime;
    const limitMessage = effectiveLimit > 0 && data.nodes.length > effectiveLimit
      ? ` (limited from ${data.nodes.length} for 3D performance)`
      : '';
    console.log(`✅ 3D graph data transformation completed in ${transformTime.toFixed(2)}ms: ${result.nodes.length} nodes${limitMessage}, ${result.links.length} links`);

    return result;
  }, [data, customConfig.nodeLimit, customConfig.nodeLimit3D, customConfig.nodeTypeVisibility, getNodeSizeWithConnections, getNodeColorForComponent, getLinkColorForComponent]);

  // 3D-specific camera positioning for selected nodes (click-to-focus pattern)
  const focusOnNode = useCallback((node, transitionDuration = 1000) => {
    try {
      if (!fgRef.current || !node) return;

      // Calculate camera position to focus on the node from outside
      const distance = customConfig.cameraDistance || 200; // Configurable distance from the node
      const nodePos = { x: node.x || 0, y: node.y || 0, z: node.z || 0 };

      // Calculate distance ratio for positioning camera outside the node
      const currentDistance = Math.hypot(nodePos.x, nodePos.y, nodePos.z);
      const distRatio = currentDistance > 0 ? 1 + distance / currentDistance : 1;

      let newCameraPos = {
        x: nodePos.x * distRatio,
        y: nodePos.y * distRatio,
        z: nodePos.z * distRatio
      };

      // Special case if node is at origin or very close to origin
      if (currentDistance < 1) {
        newCameraPos = {
          x: distance * 0.5,
          y: distance * 0.5,
          z: distance
        };
      }

      console.log(`🎥 Focusing camera on node ${node.id}: position(${newCameraPos.x.toFixed(1)}, ${newCameraPos.y.toFixed(1)}, ${newCameraPos.z.toFixed(1)}) -> lookAt(${nodePos.x.toFixed(1)}, ${nodePos.y.toFixed(1)}, ${nodePos.z.toFixed(1)})`);

      // Smooth camera transition to focus on the node
      fgRef.current.cameraPosition(
        newCameraPos, // new camera position
        nodePos, // look at the node
        transitionDuration // configurable transition duration
      );
    } catch (error) {
      console.error('Error focusing camera on node:', error);
    }
  }, [customConfig.cameraDistance]);

  // Fit-to-canvas functionality using zoomToFit
  const fitToCanvas = useCallback((transitionDuration = 400, padding = 20, nodeFilterFn = null) => {
    if (!fgRef.current) return;

    try {
      fgRef.current.zoomToFit(transitionDuration, padding, nodeFilterFn);
      console.log(`📐 3D Graph fitted to canvas with ${transitionDuration}ms transition`);
    } catch (error) {
      console.error('Error fitting 3D graph to canvas:', error);
    }
  }, []);

  // Combined focus and fit functionality
  const focusAndFit = useCallback((node = null, options = {}) => {
    const {
      focusFirst = true,
      fitAfterFocus = false,
      focusDuration = 1000,
      fitDuration = 400,
      fitPadding = 20
    } = options;

    if (node && focusFirst) {
      focusOnNode(node, focusDuration);

      if (fitAfterFocus) {
        // Delay fit to allow focus animation to complete
        setTimeout(() => {
          fitToCanvas(fitDuration, fitPadding);
        }, focusDuration + 100);
      }
    } else {
      fitToCanvas(fitDuration, fitPadding);
    }
  }, [focusOnNode, fitToCanvas]);

  // Engine stop handler for auto-fit functionality
  const handleEngineStop = useCallback(() => {
    // Auto-fit to canvas when physics simulation completes (optional)
    if (customConfig.autoFitOnEngineStop) {
      fitToCanvas(
        customConfig.autoFitDuration || 400,
        customConfig.autoFitPadding || 20
      );
    }
  }, [fitToCanvas, customConfig.autoFitOnEngineStop, customConfig.autoFitDuration, customConfig.autoFitPadding]);

  // Performance monitoring for 3D rendering - optimized to reduce re-renders
  useEffect(() => {
    if (!fgRef.current) return;

    const updateStats = () => {
      try {
        // Skip stats update during force updates to prevent WebGL context access errors
        if (isForceUpdatingRef.current) return;

        const renderer = fgRef.current.renderer();
        if (renderer && renderer.info) {
          const info = renderer.info;
          const newStats = {
            fps: Math.round(1000 / (performance.now() - (window.lastFrameTime || performance.now()))),
            nodes: forceGraphData.nodes.length,
            links: forceGraphData.links.length,
            geometries: info.memory?.geometries || 0,
            textures: info.memory?.textures || 0,
            calls: info.render?.calls || 0
          };
          
          // Only update state if stats actually changed to prevent unnecessary re-renders
          setRenderStats(prevStats => {
            if (JSON.stringify(prevStats) !== JSON.stringify(newStats)) {
              return newStats;
            }
            return prevStats;
          });
        }
        window.lastFrameTime = performance.now();
      } catch (error) {
        // Silently handle errors to avoid console spam
      }
    };

    const interval = setInterval(updateStats, 2000); // Reduced frequency to every 2 seconds
    return () => clearInterval(interval);
  }, [forceGraphData]);

  // Double-click detection for node expansion
  const [lastClickTime, setLastClickTime] = useState(0);
  const [lastClickedNode, setLastClickedNode] = useState(null);

  // Event handlers matching UnifiedGraphWrapper patterns
  const handleNodeClick = useCallback((node, event) => {
    try {
      if (event) event.stopPropagation();

      if (!node) return;

      const currentTime = Date.now();
      const timeDiff = currentTime - lastClickTime;

      // Double-click detection (within 500ms)
      if (timeDiff < 500 && lastClickedNode && lastClickedNode.id === node.id) {
        // Double-click detected - expand node
        expandNode(node.id);
        setLastClickTime(0); // Reset to prevent triple-click issues
        setLastClickedNode(null);
        return;
      }

      // Single click handling
      setLastClickTime(currentTime);
      setLastClickedNode(node);

      // Update ref and notify parent via callback
      selectedNodeRef.current = node;
      onNodeSelect(node.data || node);
      onNodeClick(node.data || node);



      // Close context menu if open
      setContextMenu(prev => ({ ...prev, visible: false }));

      // Focus camera on clicked node with optional fit-to-canvas
      if (customConfig.useFitToCanvas) {
        focusAndFit(node, {
          focusFirst: customConfig.focusBeforeFit !== false,
          fitAfterFocus: customConfig.fitAfterFocus === true,
          focusDuration: customConfig.focusDuration || 1000,
          fitDuration: customConfig.fitDuration || 400,
          fitPadding: customConfig.fitPadding || 20
        });
      } else {
        focusOnNode(node);
      }
    } catch (error) {
      console.error('Error handling node click in 3D graph:', error);
    }
  }, [onNodeSelect, onNodeClick, focusOnNode, focusAndFit, expandNode, lastClickTime, lastClickedNode, customConfig]);

  // Right-click context menu handler
  const handleNodeRightClick = useCallback((node, event) => {
    try {
      console.log('🖱️ 3D Node right-click event triggered!', {
        nodeId: node.id,
        nodeName: node.name,
        eventType: event?.type,
        clientX: event?.clientX,
        clientY: event?.clientY,
        hasEvent: !!event,
        useCustomNodes: customConfig.useCustomNodes !== false,
        nodeObject: node,
        isLibraryCall: !event?.clientX && !event?.clientY, // Library calls don't have client coordinates
        timestamp: new Date().toISOString()
      });

      // For library calls without proper event coordinates, use current mouse position
      let clientX = event?.clientX;
      let clientY = event?.clientY;
      
      if (!clientX && !clientY) {
        // Use center of screen as fallback
        clientX = window.innerWidth / 2;
        clientY = window.innerHeight / 2;
        console.log('📍 Using fallback coordinates for context menu');
      }

      if (event) {
        event.preventDefault(); // Prevent browser's default context menu
        event.stopPropagation(); // Stop event bubbling
        console.log('🚫 Browser default context menu prevented');
      }

      const menuState = {
        visible: true,
        x: clientX,
        y: clientY,
        node: node.data || node
      };

      setContextMenu(menuState);
      console.log('✅ Context menu state updated:', menuState);

      // Additional debugging - check if context menu is actually rendered
      setTimeout(() => {
        const contextMenuElement = document.querySelector('[data-context-menu]');
        console.log('🔍 Context menu element check:', {
          found: !!contextMenuElement,
          visible: contextMenuElement?.style.display !== 'none',
          position: contextMenuElement ? {
            left: contextMenuElement.style.left,
            top: contextMenuElement.style.top
          } : null
        });
      }, 100);

    } catch (error) {
      console.error('Error handling node right-click in 3D graph:', error);
    }
  }, [customConfig.useCustomNodes]);

  // Context menu action handlers
  const handleExpandNode = useCallback(() => {
    if (contextMenu.node) {
      expandNode(contextMenu.node.id);
      setContextMenu(prev => ({ ...prev, visible: false }));
    }
  }, [contextMenu.node, expandNode]);

  const handleCollapseNode = useCallback(() => {
    if (contextMenu.node) {
      // TODO: Implement collapse functionality
      console.log('🔄 Collapse node:', contextMenu.node.id);
      setContextMenu(prev => ({ ...prev, visible: false }));
    }
  }, [contextMenu.node]);

  const handleCloseContextMenu = useCallback(() => {
    setContextMenu(prev => ({ ...prev, visible: false }));
  }, []);

  // Custom tooltip state
  const [customTooltip, setCustomTooltip] = useState({ visible: false, content: '', x: 0, y: 0 });

  // Tooltip timeout ref for consistent delay behavior
  const tooltipTimeoutRef = useRef(null);

  // Use ref instead of state to prevent re-renders during WebGL operations
  const isForceUpdatingRef = useRef(false);

  // Enhanced WebGL context loss recovery with better error handling
  useEffect(() => {
    const handleContextLost = (event) => {
      console.warn('🚨 WebGL context lost, preventing default behavior and resetting state');
      event.preventDefault();
      isForceUpdatingRef.current = false; // Reset force updating state
      
      // Clear any pending animations or timeouts that might interfere with recovery
      if (fgRef.current) {
        try {
          fgRef.current.pauseAnimation();
        } catch (error) {
          console.warn('Error pausing animation during context loss:', error);
        }
      }
    };

    const handleContextRestored = () => {
      console.log('✅ WebGL context restored, reinitializing 3D graph');
      isForceUpdatingRef.current = false;
      
      // Give browser time to fully restore context before resuming
      setTimeout(() => {
        if (fgRef.current) {
          try {
            fgRef.current.resumeAnimation();
            // Force a re-render to refresh the scene
            fgRef.current.refresh();
            console.log('🔄 3D graph reinitialized after context restoration');
          } catch (error) {
            console.warn('Error reinitializing after context restoration:', error);
          }
        }
      }, 100);
    };

    // Set up context loss handlers with delay to ensure canvas is ready
    const setupContextHandlers = () => {
      const canvas = fgRef.current?.renderer?.domElement;
      if (canvas) {
        canvas.addEventListener('webglcontextlost', handleContextLost);
        canvas.addEventListener('webglcontextrestored', handleContextRestored);
        console.log('🔧 WebGL context handlers attached');
        return canvas;
      }
      return null;
    };

    // Try to set up handlers immediately, then retry after delay
    let canvas = setupContextHandlers();
    
    const timeoutId = !canvas ? setTimeout(() => {
      canvas = setupContextHandlers();
    }, 500) : null;

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
      if (canvas) {
        canvas.removeEventListener('webglcontextlost', handleContextLost);
        canvas.removeEventListener('webglcontextrestored', handleContextRestored);
        console.log('🔧 WebGL context handlers removed');
      }
    };
  }, []);

  const handleNodeHover = useCallback((node) => {
    try {
      // Skip hover handling during force updates to prevent canvas access errors
      if (isForceUpdatingRef.current) {
        return;
      }

      // Update cursor style based on hover state
      if (fgRef.current && fgRef.current.renderer && fgRef.current.renderer.domElement) {
        fgRef.current.renderer.domElement.style.cursor = node ? 'pointer' : 'default';
      }

      // Handle custom tooltip for better content display with delay
      if (tooltipTimeoutRef.current) {
        clearTimeout(tooltipTimeoutRef.current);
      }

      if (node) {
        const tooltipContent = getNodeTooltipForComponent(node);
        if (tooltipContent && fgRef.current && fgRef.current.renderer && fgRef.current.renderer.domElement) {
          const canvas = fgRef.current.renderer.domElement;

          // Additional safety check: ensure canvas is mounted and has getBoundingClientRect method
          if (canvas && typeof canvas.getBoundingClientRect === 'function') {
            tooltipTimeoutRef.current = setTimeout(() => {
              try {
                const rect = canvas.getBoundingClientRect();

                // Ensure graph2ScreenCoords method exists before calling
                if (fgRef.current.graph2ScreenCoords) {
                  // Convert 3D coordinates to screen coordinates
                  const screenCoords = fgRef.current.graph2ScreenCoords(node.x || 0, node.y || 0, node.z || 0);

                  setCustomTooltip({
                    visible: true,
                    content: tooltipContent,
                    x: rect.left + screenCoords.x,
                    y: rect.top + screenCoords.y - 10
                  });
                } else {
                  console.warn('3D Graph: graph2ScreenCoords method not available, skipping tooltip positioning');
                  setCustomTooltip({ visible: false, content: '', x: 0, y: 0 });
                }
              } catch (rectError) {
                console.warn('3D Graph: Error getting canvas bounding rect during force update:', rectError.message);
                setCustomTooltip({ visible: false, content: '', x: 0, y: 0 });
              }
            }, 500); // 500ms delay for consistency with 2D
          } else {
            console.warn('3D Graph: Canvas DOM element not ready for tooltip positioning');
            setCustomTooltip({ visible: false, content: '', x: 0, y: 0 });
          }
        }
      } else {
        setCustomTooltip({ visible: false, content: '', x: 0, y: 0 });
      }
    } catch (error) {
      console.error('Error handling node hover in 3D graph:', error);
      setCustomTooltip({ visible: false, content: '', x: 0, y: 0 });
    }
  }, [getNodeTooltipForComponent]); // Removed isForceUpdating dependency to prevent re-renders

  // Link hover handler for tooltips
  const handleLinkHover = useCallback((link, prevLink) => {
    try {
      // Clear any existing timeout
      if (tooltipTimeoutRef.current) {
        clearTimeout(tooltipTimeoutRef.current);
      }

      if (link) {
        // Get relationship fact for tooltip - use original link structure
        const originalLink = link.data; // Original link from API
        const fact = originalLink?.properties?.fact || originalLink?.fact || link.properties?.fact || link.fact;
        const tooltipContent = fact && fact.trim() ? fact : (link.label || link.type || 'Relationship');

        if (tooltipContent && fgRef.current && fgRef.current.renderer && fgRef.current.renderer.domElement) {
          const canvas = fgRef.current.renderer.domElement;

          if (canvas && typeof canvas.getBoundingClientRect === 'function') {
            tooltipTimeoutRef.current = setTimeout(() => {
              try {
                const rect = canvas.getBoundingClientRect();

                // Calculate midpoint of the link for tooltip positioning
                const sourceNode = forceGraphData.nodes.find(n => n.id === link.source?.id || n.id === link.source);
                const targetNode = forceGraphData.nodes.find(n => n.id === link.target?.id || n.id === link.target);

                if (sourceNode && targetNode && fgRef.current.graph2ScreenCoords) {
                  const midX = (sourceNode.x + targetNode.x) / 2;
                  const midY = (sourceNode.y + targetNode.y) / 2;
                  const midZ = ((sourceNode.z || 0) + (targetNode.z || 0)) / 2;

                  const screenCoords = fgRef.current.graph2ScreenCoords(midX, midY, midZ);

                  setCustomTooltip({
                    visible: true,
                    content: tooltipContent,
                    x: rect.left + screenCoords.x,
                    y: rect.top + screenCoords.y - 10
                  });
                }
              } catch (error) {
                console.warn('3D Graph: Error positioning link tooltip:', error);
                setCustomTooltip({ visible: false, content: '', x: 0, y: 0 });
              }
            }, 500); // 500ms delay for consistency
          }
        }
      } else {
        setCustomTooltip({ visible: false, content: '', x: 0, y: 0 });
      }
    } catch (error) {
      console.error('Error handling link hover in 3D graph:', error);
      setCustomTooltip({ visible: false, content: '', x: 0, y: 0 });
    }
  }, [forceGraphData.nodes]);

  const handleLinkClick = useCallback((link, event) => {
    try {
      console.log('🔗 3D STEP 1: handleLinkClick fired with:', { hasLink: !!link, hasEvent: !!event });
      
      if (event) event.stopPropagation();

      if (!link) {
        console.error('❌ 3D STEP 1: No link provided to handleLinkClick');
        return;
      }

      console.log('🔗 3D STEP 2: Link clicked with basic info:', {
        id: link.id,
        type: link.type,
        label: link.label,
        hasData: !!link.data,
        hasProperties: !!link.properties
      });
      
      // Test fact extraction at each step - the original link is in link.data
      const originalLink = link.data; // This is the original link object from the API
      const factPath1 = originalLink?.properties?.fact;
      const factPath2 = originalLink?.fact;
      const factPath3 = link.properties?.fact;
      const factPath4 = link.fact;
      
      console.log('🔍 3D STEP 2: Fact property paths:', {
        'originalLink?.properties?.fact': factPath1,
        'originalLink?.fact': factPath2,
        'link.properties?.fact': factPath3,
        'link.fact': factPath4,
        finalFact: factPath1 || factPath2 || factPath3 || factPath4,
        originalLinkStructure: originalLink
      });

      // Find source and target nodes from the graph data
      const sourceNode = forceGraphData.nodes.find(n => n.id === link.source?.id || n.id === link.source);
      const targetNode = forceGraphData.nodes.find(n => n.id === link.target?.id || n.id === link.target);
      
      console.log('🔍 3D STEP 3: Found nodes:', { hasSourceNode: !!sourceNode, hasTargetNode: !!targetNode });

      // Create relationship object with enhanced data
      const relationship = {
        id: link.id || `${link.source?.id || link.source}-${link.target?.id || link.target}`,
        type: originalLink?.type || originalLink?.label || link.type || link.label || 'RELATES_TO',
        source: link.source?.id || link.source,
        target: link.target?.id || link.target,
        properties: originalLink?.properties || link.properties || {},
        // Extract fact property for RelationshipDetails display - use originalLink
        fact: originalLink?.properties?.fact || originalLink?.fact || link.properties?.fact || link.fact,
        sourceNode: sourceNode?.data || sourceNode,
        targetNode: targetNode?.data || targetNode,
        // Include original link data (excluding circular references)
        originalLink: {
          id: link.id,
          type: link.type,
          label: link.label,
          properties: link.properties,
          data: link.data
        }
      };

      console.log('🚀 3D STEP 4: Created relationship object with fact:', {
        id: relationship.id,
        type: relationship.type,
        fact: relationship.fact,
        hasFact: !!relationship.fact,
        factLength: relationship.fact?.length || 0,
        fullRelationshipObject: relationship
      });
      console.log('🚀 3D STEP 5: onRelationshipSelect callback type:', typeof onRelationshipSelect);
      console.log('🚀 3D STEP 5: onRelationshipSelect callback exists:', !!onRelationshipSelect);
      
      // ⚠️ CRITICAL DEBUG: Log the actual data being passed to RelationshipDetails (avoiding circular references)
      const safeOriginalLink = originalLink ? {
        id: originalLink.id,
        type: originalLink.type,
        label: originalLink.label,
        properties: originalLink.properties,
        source: originalLink.source,
        target: originalLink.target
      } : null;
      
      const safeLink = {
        id: link.id,
        type: link.type,
        label: link.label,
        properties: link.properties,
        source: link.source,
        target: link.target
      };
      
      console.log('🔍 3D CRITICAL DEBUG: Full relationship data for RelationshipDetails:', {
        relationship,
        originalLinkProperties: originalLink?.properties,
        linkProperties: link.properties,
        extractedFact: relationship.fact,
        dataStructure: {
          hasOriginalLinkProperties: !!originalLink?.properties,
          hasLinkProperties: !!link.properties,
          safeOriginalLink,
          safeLink
        }
      });
      
      // Call the relationship selection callback
      if (onRelationshipSelect) {
        console.log('🚀 3D STEP 6: Calling onRelationshipSelect with relationship');
        onRelationshipSelect(relationship);
        console.log('🚀 3D STEP 7: onRelationshipSelect call completed');
      } else {
        console.error('❌ 3D STEP 6: onRelationshipSelect callback is missing!');
      }

    } catch (error) {
      console.error('Error handling link click in 3D graph:', error);
    }
  }, [forceGraphData, onRelationshipSelect]);

  const handleBackgroundClick = useCallback((event) => {
    try {
      if (event) event.stopPropagation();
      // Clear selection when clicking on background (notify parent)
      onNodeSelect(null);
      // Close context menu when clicking on background
      setContextMenu(prev => ({ ...prev, visible: false }));
    } catch (error) {
      console.error('Error handling background click in 3D graph:', error);
    }
  }, []);

  // Focus on selected node when it changes (with delay to ensure node positions are stable)
  useEffect(() => {
    if (selectedNode && fgRef.current && forceGraphData.nodes.length > 0) {
      // Small delay to ensure the force simulation has positioned the nodes
      const timeoutId = setTimeout(() => {
        const node = forceGraphData.nodes.find(n => n.id === selectedNode.id?.toString());
        if (node) {
          focusOnNode(node);
        } else {
          console.warn(`Selected node ${selectedNode.id} not found in 3D graph data`);
        }
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [selectedNode, forceGraphData, focusOnNode]);

  // Provide centerOnNode function to parent (API compatibility)
  useEffect(() => {
    const centerOnNode = (nodeId) => {
      const node = forceGraphData.nodes.find(n => n.id === nodeId?.toString());
      if (node) {
        onNodeSelect(node); // Notify parent of selection
        focusOnNode(node);
      }
    };
    onCenterOnNodeReady(centerOnNode);
  }, [forceGraphData, focusOnNode, onCenterOnNodeReady]);






  // Manual right-click detection using THREE.js raycaster
  const [raycaster] = useState(() => new THREE.Raycaster());
  const [mouse] = useState(() => new THREE.Vector2());

  // Manual event handling for custom nodes
  const handleCanvasRightClick = useCallback((event) => {
    if (!fgRef.current || !customConfig.useCustomNodes) return;

    event.preventDefault();
    event.stopPropagation();

    try {
      // Get canvas bounds
      const canvas = fgRef.current.renderer().domElement;
      const rect = canvas.getBoundingClientRect();
      
      // Calculate mouse position in normalized device coordinates
      mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

      // Update raycaster
      const camera = fgRef.current.camera();
      raycaster.setFromCamera(mouse, camera);

      // Get all node meshes from the scene (including both groups and individual meshes)
      const scene = fgRef.current.scene();
      const nodeMeshes = [];
      
      scene.traverse((child) => {
        // Check for sphere meshes (which are clickable) within groups or as standalone objects
        if (child.isMesh && child.userData && child.userData.nodeId) {
          nodeMeshes.push(child);
        }
        // Also check for groups that contain node data
        else if (child.isGroup && child.userData && child.userData.nodeId) {
          // For groups, add all child meshes to the intersection test
          child.traverse((groupChild) => {
            if (groupChild.isMesh && groupChild.userData && groupChild.userData.nodeId) {
              nodeMeshes.push(groupChild);
            }
          });
        }
      });

      if (import.meta.env.DEV) {
        console.log(`🔍 Found ${nodeMeshes.length} meshes for raycasting`);
      }

      // Check for intersections
      const intersects = raycaster.intersectObjects(nodeMeshes);
      
      if (intersects.length > 0) {
        const intersectedMesh = intersects[0].object;
        const nodeData = intersectedMesh.userData;
        
        if (nodeData && nodeData.originalNode) {
          if (import.meta.env.DEV) {
            console.log('🎯 Manual right-click detected on node:', nodeData.nodeId);
          }
          handleNodeRightClick(nodeData.originalNode, event);
        }
      } else {
        if (import.meta.env.DEV) {
          console.log('🚫 No intersections found for right-click');
        }
      }
    } catch (error) {
      console.error('Error in manual right-click detection:', error);
    }
  }, [customConfig.useCustomNodes, handleNodeRightClick, mouse, raycaster]);

  // Enhanced 3D Node rendering with error handling to prevent shader issues
  const nodeThreeObject = useCallback((node) => {
    try {
      // Check if this node is selected for highlighting
      const isSelected = selectedNode?.id?.toString() === node.id?.toString();
      
      // Use larger radius for selected nodes
      const sphereRadius = isSelected ? nodeSize * 1.8 : nodeSize;
      
      // Use embedded color first, then fallback to computed color
      let finalColor = node.color || getNodeColorForComponent(node);
      
      // Override color for selected nodes
      if (isSelected) {
        finalColor = '#00973A'; // 360T green for selection
      }
      
      // Validate color and provide fallback
      if (!finalColor || !/^#[0-9A-F]{6}$/i.test(finalColor)) {
        finalColor = defaultNodeColors[getNodeType(node)] || defaultNodeColors.Default;
      }
      
      const nodeName = getNodeName(node);

      // Create a group to hold both sphere and text
      const nodeGroup = new THREE.Group();

      // Create sphere geometry and material with error handling
      const sphereGeometry = new THREE.SphereGeometry(sphereRadius, 8, 6); // Reduced complexity for better performance
      
      const sphereMaterial = new THREE.MeshLambertMaterial({
        color: new THREE.Color(finalColor), // Ensure proper THREE.Color construction
        transparent: true,
        opacity: 0.8
      });
      const sphereMesh = new THREE.Mesh(sphereGeometry, sphereMaterial);
      
      // Add pulsing animation for selected nodes
      if (isSelected) {
        sphereMesh.userData.isSelected = true;
        sphereMesh.userData.originalScale = sphereMesh.scale.clone();
        sphereMesh.userData.pulseSpeed = 0.03;
        sphereMesh.userData.pulseAmount = 0.2;
      }

      // Create text label using SpriteText with error handling
      const textLabel = new SpriteText(nodeName);
      textLabel.material.alphaTest = 0.1;
      textLabel.color = finalColor; // Apply same color as sphere
      textLabel.textHeight = Math.max(2.4, sphereRadius * 0.4 * 0.5); // Scale text with node size (fixed size factor)
      textLabel.position.y = -(sphereRadius + textLabel.textHeight); // Position below the sphere
      textLabel.fontFace = 'Arial, sans-serif';
      textLabel.fontWeight = 'bold';
      textLabel.strokeColor = '#ffffff';
      textLabel.strokeWidth = 0.5;

      // Add both sphere and text to the group
      nodeGroup.add(sphereMesh);
      nodeGroup.add(textLabel);

      // Get connection count from memoized data
      const connectionCount = connectionCounts.get(node.id) || 0;

      // Store node data on the group for debugging and event handling
      nodeGroup.userData = {
        nodeId: node.id,
        nodeName: nodeName,
        connectionCount: connectionCount,
        originalNode: node
      };

      // Store node data on the sphere mesh as well for raycasting
      sphereMesh.userData = {
        nodeId: node.id,
        nodeName: nodeName,
        connectionCount: connectionCount,
        originalNode: node
      };

      // Enable raycasting for the sphere mesh
      sphereMesh.raycast = THREE.Mesh.prototype.raycast;

      return nodeGroup;
    } catch (error) {
      console.error('Error creating 3D node object:', error);
      // Return a simple fallback object to prevent complete failure
      const fallbackGeometry = new THREE.SphereGeometry(nodeSize, 4, 3);
      const fallbackMaterial = new THREE.MeshBasicMaterial({ color: 0x888888 });
      return new THREE.Mesh(fallbackGeometry, fallbackMaterial);
    }
  }, [connectionCounts, nodeSize, selectedNode, getNodeColorForComponent]);

  // Simplified 3D Link rendering to avoid WebGL context issues
  const linkThreeObject = useCallback((link) => {
    try {
      // Get the link label from the data - check properties first for actual relationship name
      const linkLabel = link.data?.properties?.name || link.data?.properties?.fact || link.data?.name || link.label || link.type || '';

      // Only return text sprite if there's a label, let library handle arrow rendering via different method
      if (linkLabel && linkLabel.length > 0) {
        const sprite = new SpriteText(linkLabel);
        sprite.color = 'rgba(107, 114, 128, 0.8)'; // Subtle gray color
        sprite.textHeight = 1.2; // Smaller text for less clutter
        sprite.material.alphaTest = 0.1;
        sprite.fontFace = 'Arial, sans-serif';
        sprite.fontWeight = 'normal';
        
        // Store text data for positioning
        sprite.userData = {
          isText: true,
          linkData: link
        };
        
        return sprite;
      }

      // Return null if no label - library will render default line
      return null;
    } catch (error) {
      console.error('Error creating 3D link object:', error);
      return null;
    }
  }, []);

  // Simplified position update function for text labels only
  const linkPositionUpdate = useCallback((sprite, { start, end }) => {
    try {
      if (!sprite || !start || !end) return;

      // Calculate midpoint position for text labels
      const middlePos = {
        x: (start.x || 0) + ((end.x || 0) - (start.x || 0)) / 2,
        y: (start.y || 0) + ((end.y || 0) - (start.y || 0)) / 2,
        z: (start.z || 0) + ((end.z || 0) - (start.z || 0)) / 2
      };

      // Position sprite at the midpoint of the link
      Object.assign(sprite.position, middlePos);
    } catch (error) {
      console.error('Error updating link position:', error);
    }
  }, []);



  // Global escape key handler and canvas right-click handler
  useEffect(() => {
    const handleKeyPress = (event) => {
      if (event.key === 'Escape') {
        onEscape();
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    
    // Add manual right-click handler to canvas when using custom nodes
    let canvas = null;
    const setupCanvasEvents = () => {
      if (fgRef.current && customConfig.useCustomNodes !== false) {
        canvas = fgRef.current.renderer().domElement;
        if (canvas) {
          canvas.addEventListener('contextmenu', handleCanvasRightClick);
          console.log('🔧 Manual right-click handler attached to 3D canvas');
        }
      }
    };

    // Setup canvas events after a brief delay to ensure graph is initialized
    const timeoutId = setTimeout(setupCanvasEvents, 100);
    
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
      clearTimeout(timeoutId);
      if (canvas) {
        canvas.removeEventListener('contextmenu', handleCanvasRightClick);
        console.log('🔧 Manual right-click handler removed from 3D canvas');
      }
    };
  }, [onEscape, handleCanvasRightClick, customConfig.useCustomNodes]);

  // WebGL cleanup and memory management
  useEffect(() => {
    return () => {
      if (fgRef.current) {
        try {
          // Pause animation to free up resources
          fgRef.current.pauseAnimation();

          // Access WebGL context for cleanup
          const renderer = fgRef.current.renderer();
          if (renderer) {
            // Dispose of geometries and materials
            renderer.dispose();

            // Clear WebGL context
            const gl = renderer.getContext();
            if (gl) {
              const loseContext = gl.getExtension('WEBGL_lose_context');
              if (loseContext) {
                loseContext.loseContext();
              }
            }
          }

          // Clear scene objects
          const scene = fgRef.current.scene();
          if (scene) {
            scene.clear();
          }

          console.log('🧹 3D WebGL resources cleaned up');
        } catch (error) {
          console.error('Error during 3D WebGL cleanup:', error);
        }
      }
    };
  }, []);

  // Animation loop for pulsing selected nodes
  useEffect(() => {
    let animationFrame;
    
    const animate = () => {
      if (fgRef.current) {
        const scene = fgRef.current.scene();
        if (scene) {
          scene.traverse((object) => {
            if (object.userData && object.userData.isSelected) {
              const time = Date.now() * object.userData.pulseSpeed;
              const scale = 1 + Math.sin(time) * object.userData.pulseAmount;
              object.scale.setScalar(scale);
            }
          });
        }
      }
      animationFrame = requestAnimationFrame(animate);
    };
    
    if (selectedNode) {
      animate();
    }
    
    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [selectedNode]);

  return (
    <div className="w-full h-full relative">
      <ForceGraph3D
        ref={fgRef}
        graphData={forceGraphData}
        nodeId="id"
        nodeLabel={null}
        nodeColor={node => {
          // Check if this node is selected for highlighting
          const isSelected = selectedNode?.id?.toString() === node.id?.toString();
          
          if (isSelected) {
            // Return bright highlight color for selected node
            return '#00973A'; // 360T green highlight for selection
          }

          // Use standard color function for non-selected nodes
          let color = node.color || getNodeColorForComponent(node);

          // Validate color and provide fallback (accept hex colors and rgba colors)
          if (!color || (!color.startsWith('#') && !color.startsWith('rgb'))) {
            color = defaultNodeColors[getNodeType(node)] || defaultNodeColors.Default;
          }

          if (import.meta.env.DEV && Math.random() < 0.1) { // Log 10% of calls to avoid spam
            console.log(`🎨 ForceGraph3D nodeColor for ${node.id}:`, {
              nodeId: node.id,
              color: color,
              isSelected: isSelected,
              selectedNodeId: selectedNode?.id,
              isUsingCustomNodes: customConfig.useCustomNodes !== false
            });
          }
          return color;
        }}
        linkWidth={0.1}
        nodeVal="val"
        nodeThreeObject={customConfig.useCustomNodes !== false ? nodeThreeObject : undefined}
        nodeRelSize={customConfig.useCustomNodes !== false ? 0.1 : 0.1} // Very small base size for all nodes
        linkColor="color"
        linkThreeObjectExtend={true}
        linkThreeObject={linkThreeObject}
        linkPositionUpdate={linkPositionUpdate}
        onNodeClick={handleNodeClick}
        onNodeRightClick={handleNodeRightClick}
        onNodeHover={handleNodeHover}
        onLinkClick={handleLinkClick}
        onLinkHover={handleLinkHover}
        onBackgroundClick={handleBackgroundClick}
        onEngineStop={handleEngineStop}
        // 3D-specific optimizations
        enableNavigationControls={true}
        controlType="trackball"
        showNavInfo={false}
        enablePointerInteraction={true}
        // Performance settings (moved nodeRelSize to conditional above)
        linkOpacity={0.6}
        backgroundColor="#f8fafc"
        // Force simulation settings - improved node spacing
        cooldownTicks={100}
        d3AlphaDecay={0.02}
        d3VelocityDecay={0.4}
        linkDistance={150}
        chargeStrength={-500}
      />
      
      {/* Debug controls */}
      <div className="absolute top-4 right-4 space-y-2">
        <div className="bg-black bg-opacity-50 text-white px-3 py-1 rounded text-sm">
          Press ESC to return to 2D view
        </div>
        <button
          onClick={() => {
            const newUseCustomNodes = customConfig.useCustomNodes !== false ? false : true;
            console.log(`🔧 Toggling custom nodes: ${customConfig.useCustomNodes !== false} -> ${newUseCustomNodes}`);
            // Force re-render by updating the config
            if (window.updateCustomConfig) {
              window.updateCustomConfig({ useCustomNodes: newUseCustomNodes });
            }
          }}
          className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm w-full"
        >
          {customConfig.useCustomNodes !== false ? 'Use Default Nodes' : 'Use Custom Nodes'}
        </button>
        
        <button
          onClick={() => {
            console.log('🎨 3D Color Test - Current Node Colors:');
            forceGraphData.nodes.slice(0, 5).forEach(node => {
              console.log(`Node ${node.id}: ${node.color} (${node.group})`);
            });
          }}
          className="bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded text-sm w-full"
        >
          Test Node Colors
        </button>

        {/* 3D controls are now managed by the Legend component */}

      </div>

      {/* Performance stats (only show in development) */}
      {import.meta.env.DEV && (
        <div className="absolute bottom-4 left-4 bg-black bg-opacity-70 text-white px-3 py-2 rounded text-xs font-mono">
          <div>3D Mode | Nodes: {renderStats.nodes} | Links: {renderStats.links}</div>
          {renderStats.fps > 0 && <div>FPS: {renderStats.fps} | Calls: {renderStats.calls}</div>}
          {renderStats.geometries > 0 && <div>Geometries: {renderStats.geometries} | Textures: {renderStats.textures}</div>}
        </div>
      )}

      {/* Right-click context menu */}
      {contextMenu.visible && (console.log('🎯 Rendering context menu at:', contextMenu.x, contextMenu.y) || true) && (
        <>
          {/* Invisible overlay to capture clicks outside menu */}
          <div
            className="fixed inset-0 z-40"
            onClick={handleCloseContextMenu}
          />

          {/* Context menu */}
          <div
            data-context-menu="true"
            className="fixed z-50 bg-white border border-gray-200 rounded-lg shadow-lg py-1 min-w-[150px]"
            style={{
              left: `${Math.min(contextMenu.x, window.innerWidth - 160)}px`,
              top: `${Math.min(contextMenu.y, window.innerHeight - 100)}px`
            }}
          >
            <button
              className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
              onClick={handleExpandNode}
              disabled={expandedNodes.has(contextMenu.node?.id)}
            >
              <span className="text-green-600">⊕</span>
              {expandedNodes.has(contextMenu.node?.id) ? 'Node Expanded' : 'Expand Node'}
            </button>

            <button
              className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
              onClick={handleCollapseNode}
            >
              <span className="text-red-600">⊖</span>
              Collapse Node
            </button>

            <hr className="my-1 border-gray-200" />

            <div className="px-4 py-1 text-xs text-gray-500">
              {contextMenu.node?.name || contextMenu.node?.id}
            </div>
          </div>
        </>
      )}

      {/* Custom 3D Tooltip */}
      {customTooltip.visible && (
        <div
          style={{
            position: 'absolute',
            left: `${customTooltip.x}px`,
            top: `${customTooltip.y}px`,
            maxWidth: '300px',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            padding: '8px 12px',
            borderRadius: '4px',
            fontSize: '12px',
            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
            pointerEvents: 'none',
            zIndex: 1000,
            wordWrap: 'break-word',
            whiteSpace: 'pre-wrap',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',
            lineHeight: '1.4'
          }}
        >
          {customTooltip.content}
        </div>
      )}
    </div>
  );
});

export default Unified3DGraphWrapper;
