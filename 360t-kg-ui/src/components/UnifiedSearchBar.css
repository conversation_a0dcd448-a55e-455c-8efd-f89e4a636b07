/* Unified Search Bar Styles */

/* CSS Custom Properties for Color Consistency */
:root {
  --primary-green: #00973A;
  --primary-green-light: rgba(0, 151, 58, 0.1);
  --primary-green-medium: rgba(0, 151, 58, 0.3);
  --primary-green-focus: rgba(0, 151, 58, 0.15);
  --glass-white: rgba(255, 255, 255, 0.88);
  --glass-white-hover: rgba(255, 255, 255, 0.9);
  --glass-white-focus: rgba(255, 255, 255, 0.95);
  --glass-border: rgba(255, 255, 255, 0.4);
  --glass-border-light: rgba(255, 255, 255, 0.3);
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --scrollbar-track: rgba(0, 0, 0, 0.05);
}
.unified-search-container {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  width: 700px;
  height: 60px;
  
  /* Glassmorphism effect */
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  
  /* Border and shadow */
  border: 1px solid var(--glass-border-light);
  border-radius: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  
  /* Smooth transitions */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.unified-search-container:hover {
  background: var(--glass-white-hover);
  border-color: var(--primary-green-light);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.unified-search-container:focus-within {
  background: var(--glass-white-focus);
  border-color: var(--glass-border);
  box-shadow: 0 16px 48px var(--primary-green-focus);
}

/* Category Filter Panel - Compact Chip-based Design */
.category-filter-panel {
  /* Positioned absolutely relative to search container */
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 480px; /* Increased from 320px to accommodate more chips horizontally */
  max-height: 400px; /* Increased back to 400px to allow vertical space if needed */
  
  /* Glassmorphism effect */
  background: rgba(255, 255, 255, 0.88);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  
  /* Border and shadow */
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.1);
  
  /* Layout - Tighter spacing for chip grid */
  padding: 10px; /* Reduced from 20px */
  overflow-y: auto;
  z-index: 1200;
}

/* Category Chip Grid Styling */
.category-filter-panel .MuiChip-root {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  border-width: 1.5px !important;
  cursor: pointer;
  user-select: none;
}

.category-filter-panel .MuiChip-root:hover {
  transform: scale(1.02);
  z-index: 1;
  filter: brightness(1.05);
}

.category-filter-panel .MuiChip-root:active {
  transform: scale(0.98);
}

/* Grid container optimizations */
.category-filter-panel .MuiBox-root[style*="grid"] {
  place-items: stretch;
  align-content: start;
}

/* Action Bar Buttons */
.category-filter-panel .MuiButton-root {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
}

/* Improved scrollbar styling for filter panel */
.category-filter-panel::-webkit-scrollbar {
  width: 6px;
}

.category-filter-panel::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: 3px;
}

.category-filter-panel::-webkit-scrollbar-thumb {
  background: var(--primary-green-medium);
  border-radius: 3px;
}

.category-filter-panel::-webkit-scrollbar-thumb:hover {
  background: var(--primary-green);
}

/* Focus and interaction states */
.unified-search-container:focus-within {
  outline: 2px solid var(--primary-green-medium);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .unified-search-container {
    background: #ffffff;
    border: 2px solid #000000;
    backdrop-filter: none;
  }
  
  .category-filter-panel {
    background: #ffffff;
    border: 2px solid #000000;
    backdrop-filter: none;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .unified-search-container,
  .category-filter-panel {
    transition: none;
    animation: none;
  }
}

/* Responsive Design */

/* Tablet styles */
@media (max-width: 768px) {
  .unified-search-container {
    width: calc(100vw - 40px);
    max-width: 500px;
    height: 50px;
    top: 15px;
    border-radius: 25px;
  }
  
  .category-filter-panel {
    width: 400px; /* Increased from 280px for better tablet display */
    max-height: 350px; /* Increased to accommodate more content */
  }
}

/* Mobile styles */
@media (max-width: 480px) {
  .unified-search-container {
    width: calc(100vw - 20px);
    height: 45px;
    top: 10px;
    border-radius: 22px;
  }
  
  .category-filter-panel {
    /* Improved mobile modal - compact chip design */
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: calc(100vw - 24px);
    max-width: 420px; /* Increased from 340px for mobile */
    max-height: 70vh; /* Increased back to 70vh for better mobile display */
    border-radius: 20px;
    padding: 12px; /* Reduced from 16px */
    background: var(--glass-white);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid var(--glass-border);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(0, 151, 58, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.6);
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .unified-search-container {
    /* Larger touch targets on touch devices */
    min-height: 48px;
  }
  
  /* Larger touch targets for filter panel chips and buttons */
  .category-filter-panel .MuiChip-root {
    height: 32px !important; /* Larger touch target for mobile */
    font-size: 0.8rem !important;
  }
  
  .category-filter-panel .MuiButton-root {
    min-height: 36px;
    padding: 8px 12px;
  }
}

/* Print styles - hide search bar when printing */
@media print {
  .unified-search-container {
    display: none;
  }
}

/* Dark mode support (if needed in future) */
@media (prefers-color-scheme: dark) {
  .unified-search-container {
    background: rgba(17, 24, 39, 0.85);
    border-color: rgba(75, 85, 99, 0.3);
  }
  
  .category-filter-panel {
    background: rgba(17, 24, 39, 0.95);
    border-color: rgba(75, 85, 99, 0.3);
  }
}

/* Loading states */
.unified-search-container.loading {
  cursor: progress;
}

.unified-search-container.disabled {
  background: rgba(255, 255, 255, 0.6);
  border-color: rgba(0, 0, 0, 0.1);
  opacity: 0.7;
  cursor: not-allowed;
}

/* Animation keyframes */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideOut {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
}

/* Apply animations to dropdowns */
.search-dropdown-enter {
  animation: slideIn 0.2s ease-out;
}

.search-dropdown-exit {
  animation: slideOut 0.2s ease-in;
}

/* Ensure proper z-index stacking */
.unified-search-container > .MuiAutocomplete-popper {
  z-index: 1100 !important;
}

/* Custom styling for Material-UI components within search */
.unified-search-container .MuiChip-root {
  transition: all 0.2s ease;
}

.unified-search-container .MuiChip-root:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Badge styling for filter button */
.unified-search-container .MuiBadge-badge {
  background-color: #dc2626;
  color: white;
  font-size: 0.6rem;
  height: 16px;
  min-width: 16px;
}

/* Ensure search input doesn't zoom on iOS */
.unified-search-container input {
  font-size: 16px;
}

@media screen and (max-width: 480px) {
  .unified-search-container input {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}