/* Add styles for properties section */
.node-properties {
  margin-top: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.properties-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 8px;
}

.property-item {
  display: flex;
  padding: 4px 0;
  border-bottom: 1px solid #eee;
}

.property-key {
  font-weight: 600;
  margin-right: 8px;
  color: #555;
  flex: 0 0 40%;
}

.property-value {
  flex: 1;
  word-break: break-word;
}



/* PDF Link Styles */
.property-pdf-link {
  margin: 8px 0;
  padding: 8px 0;
}

.pdf-document-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  color: #dc3545;
  font-weight: 500;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #dc3545;
  background-color: #fff;
  transition: all 0.2s ease;
}

.pdf-document-link:hover {
  background-color: #dc3545;
  color: white;
  text-decoration: none;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2);
}

.pdf-document-link .pdf-icon {
  height: 1.5em !important;
  width: auto !important;
  object-fit: contain;
  max-width: 21px;
  max-height: 21px;
}

.pdf-text {
  font-size: 14px;
  font-weight: 500;
}