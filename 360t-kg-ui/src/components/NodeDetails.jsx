import React, { useState, useEffect, useCallback } from 'react';
import { getNodeDetails } from '../services/api';
import { getNodeIcon as getNodeIconFromMap } from '../constants/iconMap.js';
import pdfIcon from '../assets/logos/pdf_1979245.png';
import { getCategoryColor, getContrastTextColor } from '../constants/categoryColors';

/**
 * NodeDetails component displays detailed information about a selected node
 * @param {Object} selectedNode - The selected node object
 * @param {Function} onClose - Callback when closing details
 * @param {Function} onAnalysisResults - Callback when analysis results are received
 * @param {Function} onNodeSelect - Callback when a related node is selected
 * @param {Function} onNodeExpand - Callback when expanding a node
 */
function NodeDetails({ selectedNode, onClose, onAnalysisResults, onNodeSelect, onNodeExpand }) {
  const [nodeData, setNodeData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Function to get appropriate icon for node type
  const getNodeIcon = useCallback((nodeType) => {
    return getNodeIconFromMap(nodeType);
  }, []);

  // Function to get node type from labels or fallback
  const getNodeType = useCallback((node) => {
    if (node.labels && Array.isArray(node.labels) && node.labels.length > 0) {
      return node.labels[0];
    }
    if (node.group) {
      return node.group;
    }
    return 'Unknown';
  }, []);

  // Function to get display name (remove "Show " prefix if present)
  const getDisplayName = useCallback((node) => {
    let name = node.name || node.label || node.title || node.id || 'Unknown Node';
    // Remove "Show " prefix if present
    name = name.replace(/^show\s+/i, '');
    return name;
  }, []);

  /**
   * Gets specific properties for nodes in the required order
   * Custom format: name(category), summary, description, source document with PDF link
   * @param {Object} properties - The raw properties object from the node
   * @returns {Object} Ordered properties object with only specific fields
   */
  const getSpecificProperties = useCallback((properties) => {
    if (!properties || typeof properties !== 'object') return {};

    const orderedProperties = {};

    // Name is now displayed in the header with category badge, so exclude from properties section
    // Define the specific properties we want to show in order (excluding name and category as they're now in header)
    const desiredProperties = ['summary', 'description', 'source_document', 'sourceDocument', 'document'];

    // Add properties in the specified order if they exist
    desiredProperties.forEach(key => {
      if (properties[key] && properties[key] !== null && properties[key] !== '') {
        orderedProperties[key] = properties[key];
      }
    });

    // Add URL as a special property for PDF link display
    if (properties.url && properties.url !== null && properties.url !== '') {
      orderedProperties.url = properties.url;
    }

    return orderedProperties;
  }, []);

  /**
   * Filters out technical/system properties that contain large data or are not user-friendly
   * This improves the UI by hiding fields like n2v embeddings, vector data, etc.
   * @param {Object} properties - The raw properties object from the node
   * @returns {Object} Filtered properties object with technical fields removed
   */
  const getFilteredProperties = useCallback((properties) => {
    if (!properties || typeof properties !== 'object') return {};

    // List of property keys to hide from the user interface
    const hiddenPropertyKeys = [
      'n2v',           // Node2Vec embeddings - large numerical vectors
      'embeddings',    // Any other embedding data
      'vector_data',   // Vector representations
      'embedding',     // Alternative embedding field names
      'vectors',       // Plural vector fields
      'features',      // Feature vectors
      'representation' // Data representations
    ];

    return Object.fromEntries(
      Object.entries(properties).filter(([key]) =>
        !hiddenPropertyKeys.some(hiddenKey =>
          key.toLowerCase().includes(hiddenKey.toLowerCase())
        )
      )
    );
  }, []);

  // Effect to load node details when selectedNode changes
  useEffect(() => {
    if (!selectedNode) {
      setNodeData(null);
      setError(null);
      return;
    }

    const isDataComplete = selectedNode.relationships && 
                          Array.isArray(selectedNode.relationships) && 
                          selectedNode.relationships.length > 0;

    // If data is complete, use it immediately without loading state
    if (isDataComplete) {
      setNodeData({
        id: selectedNode.id,
        name: getDisplayName(selectedNode),
        type: getNodeType(selectedNode),
        labels: selectedNode.labels || [],
        properties: selectedNode.properties || {},
        relationships: selectedNode.relationships
      });
    } else {
      // Handle case where relationships exist but are empty array
      if (Array.isArray(selectedNode.relationships) && selectedNode.relationships.length === 0) {
        setNodeData({
          id: selectedNode.id,
          name: getDisplayName(selectedNode),
          type: getNodeType(selectedNode),
          labels: selectedNode.labels || [],
          properties: selectedNode.properties || {},
          relationships: []
        });
        return;
      }

      // Need to fetch relationship data
      setIsLoading(true);
      setError(null);
      
      const fetchNodeDetails = async () => {
        try {
          const response = await getNodeDetails(selectedNode.id);
          
          setNodeData({
            id: response.id || selectedNode.id,
            name: getDisplayName(response) || getDisplayName(selectedNode),
            type: getNodeType(response) || getNodeType(selectedNode),
            labels: response.labels || selectedNode.labels || [],
            properties: response.properties || selectedNode.properties || {},
            relationships: response.relationships || []
          });
        } catch (err) {
          console.error('Error fetching node details:', err);
          setError('Failed to load node details');
          
          // Preserve basic node information even on error
          setNodeData({
            id: selectedNode.id,
            name: getDisplayName(selectedNode),
            type: getNodeType(selectedNode),
            labels: selectedNode.labels || [],
            properties: selectedNode.properties || {},
            relationships: []
          });
        } finally {
          setIsLoading(false);
        }
      };

      fetchNodeDetails();
    }
  }, [selectedNode]);

  // Handle ESC key to close
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [onClose]);

  // Handle relationship node clicks
  const handleRelationshipNodeClick = useCallback((relatedNode) => {
    if (onNodeSelect && relatedNode) {
      onNodeSelect(relatedNode);
    }
  }, [onNodeSelect]);



  // Render loading state
  if (isLoading) {
    return (
      <div className="details-panel">
        <div className="loading-overlay">
          <div className="loading-spinner"></div>
          <p>Loading node details...</p>
        </div>
      </div>
    );
  }

  // Render error state if fetching fails
  if (error && !nodeData) {
    return (
      <div className="details-panel">
        <div className="error-message" style={{ margin: '20px' }}>
          <p>⚠️ {error}</p>
          <button className="close-button" onClick={onClose} style={{ top: '10px', right: '10px' }}>×</button>
        </div>
      </div>
    );
  }

  // Render when no node is selected
  if (!nodeData) {
    return (
      <div className="details-panel empty-panel">
        <p>No node selected</p>
      </div>
    );
  }

  const nodeType = nodeData.type;
  const iconSrc = `/svg/${getNodeIcon(nodeType)}`;

  return (
    <div className="details-panel">
      <div className="panel-header">
        <div className="node-icon-container" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <img 
            src={iconSrc} 
            alt={`${nodeType} icon`} 
            className="relationship-icon"
            onError={(e) => {
              console.warn(`Failed to load icon: ${iconSrc}`);
              e.target.src = '/svg/system-svgrepo-com.svg';
            }}
          />
          <div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', flexWrap: 'wrap' }}>
              <h2 style={{ margin: '0', fontSize: '1.25rem' }}>
                {nodeData.properties?.name || nodeData.name || 'Unknown Node'}
              </h2>
              {nodeData.properties?.category && (
                <span style={{
                  color: getContrastTextColor(getCategoryColor(nodeData.properties.category)),
                  fontSize: '11px',
                  backgroundColor: getCategoryColor(nodeData.properties.category),
                  padding: '3px 6px',
                  borderRadius: '4px',
                  fontWeight: '600',
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px'
                }}>
                  {nodeData.properties.category}
                </span>
              )}
            </div>
            <span className="detail-type">{nodeType}</span>
          </div>
        </div>
        <button className="close-button" onClick={onClose}>×</button>
      </div>

      <div className="details-content">
        {/* Properties Section */}
        {nodeData.properties && Object.keys(nodeData.properties).length > 0 && (() => {
          const specificProperties = getSpecificProperties(nodeData.properties);
          const hasVisibleProperties = Object.keys(specificProperties).length > 0;

          return hasVisibleProperties ? (
            <div className="properties-list">
              <h4>Properties</h4>
              <div className="properties-vertical">
                {Object.entries(specificProperties).map(([key, value]) => (
                  <div key={key} className="property-item-vertical">
                    {key === 'url' ? (
                      // Special handling for URL - display as PDF link with icon
                      <div className="property-pdf-link">
                        <a 
                          href={value} 
                          target="_blank" 
                          rel="noopener noreferrer" 
                          className="pdf-document-link"
                          title="Open PDF document"
                        >
                          <img 
                            src={pdfIcon} 
                            alt="PDF" 
                            className="pdf-icon"
                            style={{
                              height: '1.5em',
                              width: 'auto',
                              maxHeight: '21px',
                              maxWidth: '21px'
                            }}
                          />
                          <span className="pdf-text"> View Documentation</span>
                        </a>
                      </div>
                    ) : (
                      // Regular property display
                      <>
                        <div className="property-name-green">{key.replace(/_/g, ' ')}</div>
                        <div className="property-value-vertical">{typeof value === 'object' ? JSON.stringify(value) : String(value)}</div>
                      </>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ) : null;
        })()}

        {/* Labels Section */}
        {nodeData.labels && nodeData.labels.length > 0 && (
          <div className="detail-group">
            <h4>Labels</h4>
            <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
              {nodeData.labels.map((label, index) => (
                <span key={index} className="legend-badge" style={{ fontSize: '0.75rem', padding: '2px 8px' }}>{label}</span>
              ))}
            </div>
          </div>
        )}

        {/* Relationships Section */}
        {nodeData.relationships && nodeData.relationships.length > 0 && (
          <div className="section">
            {(() => {
              // Separate MENTIONS relationships from others
              const mentionsRelationships = nodeData.relationships.filter(rel => 
                rel && typeof rel === 'object' && rel.type === 'MENTIONS'
              );
              const otherRelationships = nodeData.relationships.filter(rel => 
                rel && typeof rel === 'object' && rel.type !== 'MENTIONS'
              );

              // Further separate by direction
              const outgoingRelationships = otherRelationships.filter(rel => rel.direction === 'outgoing');
              const incomingRelationships = otherRelationships.filter(rel => rel.direction === 'incoming');

              return (
                <>
                  {/* Outgoing Relationships */}
                  {outgoingRelationships.length > 0 && (
                    <>
                      <h3 className="section-title outgoing-section">Outgoing Relationships ({outgoingRelationships.length})</h3>
                      <div className="relationships-list outgoing-list">
                        {outgoingRelationships.map((rel, index) => (
                          <div 
                            key={`${rel.type || 'unknown'}-${rel.direction || 'none'}-${index}`} 
                            className="relationship-item outgoing-item"
                            onClick={() => onNodeSelect && rel.node && onNodeSelect(rel.node)}
                          >
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                              <div style={{ display: 'flex', alignItems: 'center', gap: '6px', flex: 1, minWidth: 0, fontSize: '13px' }}>
                                <span className="relationship-type">{rel.properties?.name || rel.type || 'Unknown'}</span>
                                <span className="relationship-direction">
                                  {rel.direction === 'outgoing' ? '→' : rel.direction === 'incoming' ? '←' : '↔'}
                                </span>
                                {rel.node ? (
                                  <span className="node-name" style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                    {getDisplayName(rel.node) || 'Unnamed Node'}
                                  </span>
                                ) : (
                                  <span className="node-name">Unknown Node</span>
                                )}
                              </div>
                              {rel.node?.properties?.category && (
                                <span style={{
                                  color: getContrastTextColor(getCategoryColor(rel.node.properties.category)),
                                  fontSize: '9px',
                                  backgroundColor: getCategoryColor(rel.node.properties.category),
                                  padding: '2px 6px',
                                  borderRadius: '3px',
                                  fontWeight: '500',
                                  textTransform: 'uppercase',
                                  letterSpacing: '0.5px',
                                  flexShrink: 0
                                }}>
                                  {rel.node.properties.category}
                                </span>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </>
                  )}

                  {/* Incoming Relationships */}
                  {incomingRelationships.length > 0 && (
                    <>
                      <h3 className="section-title incoming-section">Incoming Relationships ({incomingRelationships.length})</h3>
                      <div className="relationships-list incoming-list">
                        {incomingRelationships.map((rel, index) => (
                          <div 
                            key={`${rel.type || 'unknown'}-${rel.direction || 'none'}-${index}`} 
                            className="relationship-item incoming-item"
                            onClick={() => onNodeSelect && rel.node && onNodeSelect(rel.node)}
                          >
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                              <div style={{ display: 'flex', alignItems: 'center', gap: '6px', flex: 1, minWidth: 0, fontSize: '13px' }}>
                                <span className="relationship-type">{rel.properties?.name || rel.type || 'Unknown'}</span>
                                <span className="relationship-direction">
                                  {rel.direction === 'outgoing' ? '→' : rel.direction === 'incoming' ? '←' : '↔'}
                                </span>
                                {rel.node ? (
                                  <span className="node-name" style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                    {getDisplayName(rel.node) || 'Unnamed Node'}
                                  </span>
                                ) : (
                                  <span className="node-name">Unknown Node</span>
                                )}
                              </div>
                              {rel.node?.properties?.category && (
                                <span style={{
                                  color: getContrastTextColor(getCategoryColor(rel.node.properties.category)),
                                  fontSize: '9px',
                                  backgroundColor: getCategoryColor(rel.node.properties.category),
                                  padding: '2px 6px',
                                  borderRadius: '3px',
                                  fontWeight: '500',
                                  textTransform: 'uppercase',
                                  letterSpacing: '0.5px',
                                  flexShrink: 0
                                }}>
                                  {rel.node.properties.category}
                                </span>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </>
                  )}

                  {/* Mentions Section - Separated */}
                  {mentionsRelationships.length > 0 && (
                    <>
                      <h3 className="section-title mentions-section">Document References ({mentionsRelationships.length})</h3>
                      <div className="relationships-list mentions-list">
                        {mentionsRelationships.map((rel, index) => (
                          <div 
                            key={`${rel.type || 'unknown'}-${rel.direction || 'none'}-${index}`} 
                            className="relationship-item mentions-item"
                            onClick={() => onNodeSelect && rel.node && onNodeSelect(rel.node)}
                          >
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                              <div style={{ display: 'flex', alignItems: 'center', gap: '6px', flex: 1, minWidth: 0, fontSize: '13px' }}>
                                <span className="relationship-type">{rel.properties?.name || rel.type || 'Unknown'}</span>
                                <span className="relationship-direction">
                                  {rel.direction === 'outgoing' ? '→' : rel.direction === 'incoming' ? '←' : '↔'}
                                </span>
                                {rel.node ? (
                                  <span className="node-name" style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                    {getDisplayName(rel.node) || 'Unnamed Node'}
                                  </span>
                                ) : (
                                  <span className="node-name">Unknown Node</span>
                                )}
                              </div>
                              {rel.node?.properties?.category && (
                                <span style={{
                                  color: getContrastTextColor(getCategoryColor(rel.node.properties.category)),
                                  fontSize: '9px',
                                  backgroundColor: getCategoryColor(rel.node.properties.category),
                                  padding: '2px 6px',
                                  borderRadius: '3px',
                                  fontWeight: '500',
                                  textTransform: 'uppercase',
                                  letterSpacing: '0.5px',
                                  flexShrink: 0
                                }}>
                                  {rel.node.properties.category}
                                </span>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </>
                  )}
                </>
              );
            })()}
          </div>
        )}

        {/* Error Section */}
        {error && (
          <div className="error-message">
            <p>⚠️ {error}</p>
          </div>
        )}


      </div>
      
      <div className="panel-footer">
        <p className="hint">Press ESC to return to graph view</p>
      </div>
    </div>
  );
}

export default NodeDetails; 