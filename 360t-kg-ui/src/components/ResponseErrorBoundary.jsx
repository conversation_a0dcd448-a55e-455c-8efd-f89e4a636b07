import React from 'react';

/**
 * Error Boundary specifically for handling malformed chat responses
 * Provides graceful fallback when StructuredResponse component fails
 */
class ResponseErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // Log error in development
    if (process.env.NODE_ENV === 'development') {
      console.error('🚨 ResponseErrorBoundary caught an error:', {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        messageContent: this.props.messageContent?.substring(0, 200),
        retryCount: this.state.retryCount
      });
    }

    // Report error to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      this.reportError(error, errorInfo);
    }
  }

  reportError = (error, errorInfo) => {
    // In a real application, you would send this to your error monitoring service
    console.error('Response rendering error:', {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      messageLength: this.props.messageContent?.length
    });
  };

  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: prevState.retryCount + 1
    }));
  };

  handleFallbackToPlainText = () => {
    if (this.props.onFallbackToPlainText) {
      this.props.onFallbackToPlainText(this.props.messageContent);
    }
  };

  render() {
    if (this.state.hasError) {
      // Fallback UI when response rendering fails
      return (
        <div className="response-error-boundary">
          <div className="error-message">
            <h4>⚠️ Response Display Error</h4>
            <p>There was an issue displaying this response in structured format.</p>
            
            <div className="error-actions">
              {this.state.retryCount < 2 && (
                <button 
                  onClick={this.handleRetry}
                  className="retry-button"
                  type="button"
                >
                  🔄 Retry ({this.state.retryCount + 1}/2)
                </button>
              )}
              
              <button 
                onClick={this.handleFallbackToPlainText}
                className="fallback-button"
                type="button"
              >
                📄 Show as Plain Text
              </button>
            </div>

            {process.env.NODE_ENV === 'development' && (
              <details className="error-details">
                <summary>Debug Information</summary>
                <div className="error-debug">
                  <div><strong>Error:</strong> {this.state.error?.message}</div>
                  <div><strong>Content Length:</strong> {this.props.messageContent?.length || 0}</div>
                  <div><strong>Retry Count:</strong> {this.state.retryCount}</div>
                  {this.state.error?.stack && (
                    <div>
                      <strong>Stack Trace:</strong>
                      <pre>{this.state.error.stack}</pre>
                    </div>
                  )}
                  {this.state.errorInfo?.componentStack && (
                    <div>
                      <strong>Component Stack:</strong>
                      <pre>{this.state.errorInfo.componentStack}</pre>
                    </div>
                  )}
                </div>
              </details>
            )}
          </div>

          <style jsx>{`
            .response-error-boundary {
              border: 2px solid #ff6b6b;
              border-radius: 8px;
              padding: 16px;
              margin: 8px 0;
              background-color: #fff5f5;
              color: #c53030;
            }
            
            .error-message h4 {
              margin: 0 0 8px 0;
              color: #c53030;
            }
            
            .error-message p {
              margin: 0 0 16px 0;
              color: #744545;
            }
            
            .error-actions {
              display: flex;
              gap: 8px;
              margin-bottom: 16px;
            }
            
            .retry-button, .fallback-button {
              padding: 8px 16px;
              border: 1px solid #c53030;
              background: white;
              color: #c53030;
              border-radius: 4px;
              cursor: pointer;
              font-size: 14px;
            }
            
            .retry-button:hover, .fallback-button:hover {
              background: #c53030;
              color: white;
            }
            
            .error-details {
              margin-top: 16px;
              font-size: 12px;
            }
            
            .error-debug {
              margin-top: 8px;
              padding: 8px;
              background: #f7fafc;
              border-radius: 4px;
              color: #2d3748;
            }
            
            .error-debug div {
              margin-bottom: 8px;
            }
            
            .error-debug pre {
              white-space: pre-wrap;
              word-break: break-word;
              font-size: 11px;
              max-height: 200px;
              overflow-y: auto;
              background: #edf2f7;
              padding: 8px;
              border-radius: 4px;
              margin: 4px 0;
            }
          `}</style>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ResponseErrorBoundary;