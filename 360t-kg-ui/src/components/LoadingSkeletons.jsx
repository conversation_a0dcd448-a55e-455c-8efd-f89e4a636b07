import React from 'react';
import './LoadingSkeletons.css';

/**
 * Skeleton loading component for graph visualization
 * Shows different loading states with appropriate visual feedback
 */

// Main graph skeleton for initial loading
export const GraphSkeleton = ({ is3DMode = false }) => {
  return (
    <div className="graph-skeleton-container">
      <div className="graph-skeleton-header">
        <div className="skeleton-pulse skeleton-title"></div>
        <div className="skeleton-pulse skeleton-controls"></div>
      </div>
      
      <div className={`graph-skeleton-canvas ${is3DMode ? 'skeleton-3d' : 'skeleton-2d'}`}>
        {/* Simulate nodes */}
        <div className="skeleton-nodes">
          {[...Array(8)].map((_, i) => (
            <div 
              key={i} 
              className="skeleton-node skeleton-pulse"
              style={{
                left: `${20 + (i % 3) * 30}%`,
                top: `${20 + Math.floor(i / 3) * 25}%`,
                animationDelay: `${i * 0.1}s`
              }}
            ></div>
          ))}
        </div>
        
        {/* Simulate connections */}
        <div className="skeleton-connections">
          {[...Array(6)].map((_, i) => (
            <div 
              key={i} 
              className="skeleton-connection skeleton-pulse"
              style={{
                animationDelay: `${0.5 + i * 0.1}s`
              }}
            ></div>
          ))}
        </div>
        
        {/* Loading indicator */}
        <div className="skeleton-loading-indicator">
          <div className="skeleton-spinner"></div>
          <div className="skeleton-loading-text">Loading graph data...</div>
        </div>
      </div>
    </div>
  );
};

// Data processing skeleton
export const DataProcessingSkeleton = () => {
  return (
    <div className="data-processing-skeleton">
      <div className="processing-steps">
        <div className="processing-step">
          <div className="step-icon skeleton-pulse"></div>
          <div className="step-text">
            <div className="skeleton-pulse skeleton-text-line"></div>
            <div className="skeleton-pulse skeleton-text-line-small"></div>
          </div>
          <div className="step-progress">
            <div className="progress-bar">
              <div className="progress-fill processing-animation"></div>
            </div>
          </div>
        </div>
        
        <div className="processing-step">
          <div className="step-icon skeleton-pulse"></div>
          <div className="step-text">
            <div className="skeleton-pulse skeleton-text-line"></div>
            <div className="skeleton-pulse skeleton-text-line-small"></div>
          </div>
          <div className="step-progress">
            <div className="progress-bar">
              <div className="progress-fill transforming-animation"></div>
            </div>
          </div>
        </div>
        
        <div className="processing-step">
          <div className="step-icon skeleton-pulse"></div>
          <div className="step-text">
            <div className="skeleton-pulse skeleton-text-line"></div>
            <div className="skeleton-pulse skeleton-text-line-small"></div>
          </div>
          <div className="step-progress">
            <div className="progress-bar">
              <div className="progress-fill rendering-animation"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Compact loading indicator for quick operations
export const CompactLoadingSkeleton = ({ message = "Loading..." }) => {
  return (
    <div className="compact-loading-skeleton">
      <div className="compact-spinner"></div>
      <div className="compact-message">{message}</div>
    </div>
  );
};

// Phase-based loading component
export const PhaseLoadingIndicator = ({ phase, progress = 0 }) => {
  const phases = {
    idle: { label: 'Ready', color: '#6b7280' },
    fetching: { label: 'Fetching data...', color: '#3b82f6' },
    transforming: { label: 'Processing data...', color: '#f59e0b' },
    rendering: { label: 'Rendering graph...', color: '#00973a' },
    complete: { label: 'Complete', color: '#059669' }
  };

  const currentPhase = phases[phase] || phases.idle;

  return (
    <div className="phase-loading-indicator">
      <div className="phase-header">
        <div 
          className="phase-dot"
          style={{ backgroundColor: currentPhase.color }}
        ></div>
        <span className="phase-label">{currentPhase.label}</span>
      </div>
      
      {progress > 0 && (
        <div className="phase-progress">
          <div className="phase-progress-bar">
            <div 
              className="phase-progress-fill"
              style={{ 
                width: `${progress}%`,
                backgroundColor: currentPhase.color 
              }}
            ></div>
          </div>
          <span className="phase-progress-text">{Math.round(progress)}%</span>
        </div>
      )}
    </div>
  );
};

// Network request skeleton for API calls
export const NetworkRequestSkeleton = ({ endpoint }) => {
  return (
    <div className="network-request-skeleton">
      <div className="request-header">
        <div className="request-method">GET</div>
        <div className="request-url skeleton-pulse">{endpoint}</div>
      </div>
      <div className="request-status">
        <div className="status-indicator pulsing"></div>
        <span>Requesting...</span>
      </div>
    </div>
  );
};
