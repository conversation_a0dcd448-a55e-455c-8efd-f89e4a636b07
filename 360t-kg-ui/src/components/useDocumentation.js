import { useState, useEffect } from 'react';
import { marked } from 'marked';
import DOMPurify from 'dompurify';

export function useDocumentation(expandedDoc) {
  const [docContent, setDocContent] = useState('');
  const [docLoading, setDocLoading] = useState(false);
  const [docError, setDocError] = useState(null);

  useEffect(() => {
    marked.setOptions({
      gfm: true,
      breaks: true,
      sanitize: false,
      headerIds: true,
      mangle: false
    });

    const loadDocContent = async () => {
      if (!expandedDoc) {
        setDocContent('');
        setDocError(null);
        return;
      }

      setDocLoading(true);
      setDocError(null);

      try {
        // Server automatically adds .md extension, so don't add it here
        const response = await fetch(`/api/docs/${expandedDoc}`);
        if (!response.ok) {
          throw new Error(`Failed to load documentation: ${response.status} ${response.statusText}`);
        }

        const contentType = response.headers.get('content-type');
        if (!contentType || (!contentType.includes('text/markdown') && !contentType.includes('text/plain'))) {
          throw new Error(`Invalid content type: ${contentType}`);
        }

        const content = await response.text();
        if (!content.trim()) {
          throw new Error('Received empty content from server');
        }

        const renderedContent = marked.parse(content);
        const sanitizedContent = DOMPurify.sanitize(renderedContent, {
          ADD_TAGS: ['table', 'thead', 'tbody', 'tr', 'th', 'td', 'svg', 'g', 'path', 'text', 'circle', 'rect', 'line', 'polygon', 'polyline', 'ellipse', 'defs', 'marker', 'foreignobject', 'div', 'details', 'summary'],
          ADD_ATTR: ['align', 'class', 'id', 'width', 'height', 'viewBox', 'x', 'y', 'x1', 'y1', 'x2', 'y2', 'cx', 'cy', 'r', 'rx', 'ry', 'fill', 'stroke', 'stroke-width', 'stroke-dasharray', 'transform', 'd', 'points', 'marker-start', 'marker-end', 'text-anchor', 'dominant-baseline', 'font-family', 'font-size', 'font-weight', 'style', 'open']
        });

        if (!sanitizedContent.trim()) {
          throw new Error('Content processing resulted in empty output');
        }

        setDocContent(sanitizedContent);
      } catch (error) {
        console.error('useDocumentation error:', error);
        setDocError(error instanceof Error ? error.message : String(error));
      } finally {
        setDocLoading(false);
      }
    };

    loadDocContent();
  }, [expandedDoc]);

  return { docContent, docLoading, docError };
}
