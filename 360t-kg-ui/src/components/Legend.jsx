import React, { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import ColorPickerModal from './ColorPickerModal';
import { useSettings } from '../hooks/useSettings';
import * as d3 from 'd3';
import debounce from 'lodash.debounce';

// Default node type to color mapping - current business categories only
const defaultNodeColors = {
  'MMC': '#e91e63',           // bright pink/magenta - MMC category
  'SEP': '#9c27b0',           // bright purple/violet - SEP category
  'BA-PWD': '#6b7280',        // dark gray - BA-PWD category
  'ECN': '#6b7280',           // dark gray - ECN category
  'FUT': '#6b7280',           // dark gray - FUT category
  'BA-DD': '#6b7280',         // dark gray - BA-DD category
  'PS-MAP': '#6b7280',        // dark gray - PS-MAP category
  'ISIN': '#6b7280',          // dark gray - ISIN category
  'BA-SP': '#6b7280',         // dark gray - BA-SP category
  'Default': '#6b7280',       // neutral gray - Unknown types
};

// Default relationship type to color mapping
const defaultRelationshipColors = {
  'USES': '#00973A',          // 360T green
  'CONTAINS': '#ec4899',      // pink
  'NAVIGATES_TO': '#8b5cf6',  // purple
  'VALIDATES': '#f59e0b',     // amber
  'REQUIRES': '#ef4444',      // red
  'CONFIGURES_IN': '#06b6d4', // cyan
  'DISPLAYS': '#f97316',      // orange
  'Default': '#64748b',       // slate
};

// Default node sizes - current business categories only
const defaultNodeSizes = {
  'MMC': 20,               // Medium - MMC category
  'SEP': 20,               // Medium - SEP category
  'BA-PWD': 18,            // Medium-small - BA-PWD category
  'ECN': 18,               // Medium-small - ECN category
  'FUT': 18,               // Medium-small - FUT category
  'BA-DD': 18,             // Medium-small - BA-DD category
  'PS-MAP': 18,            // Medium-small - PS-MAP category
  'ISIN': 18,              // Medium-small - ISIN category
  'BA-SP': 18,             // Medium-small - BA-SP category
  'Default': 16,           // Default size for unknown types
};

/**
 * Legend component for displaying node types and relationship types
 * @param {Object} props - Component props
 * @param {Object} props.data - Graph data with nodes and links
 * @param {Object} props.initialConfig - Initial configuration of colors and sizes
 * @param {function} props.onNodeConfigChange - Callback when node config changes
 * @param {function} props.onFilterChange - Callback when node filtering changes
 * @param {function} props.onClose - Callback to close the legend
 * @param {string} props.searchQuery - Current search query
 * @param {function} props.setSearchQuery - Function to update search query
 * @param {Array} props.searchResults - Array of search results
 * @param {boolean} props.showSearchResults - Whether to show search results dropdown
 * @param {function} props.onSearch - Function to handle search
 * @param {function} props.onCenterOnNode - Function to center on selected node
 * @param {number} props.nodeLimit - Current node limit
 * @param {function} props.setNodeLimit - Function to update node limit
 */
const Legend = ({
  data,
  initialConfig = {},
  onNodeConfigChange,
  onFilterChange,
  onClose,
  // Node limit props
  nodeLimit = 0, // 0 means no limit - user requested removal of artificial limits
  setNodeLimit = () => {},
  // 3D mode props
  is3DMode = false,
  nodeSize = 2,
  onNodeSizeChange = () => {},
  // 3D Force control props
  forceGraphRef = null,
  onForceConfigChange = () => {}
}) => {
  const { settings, set: setSetting, get: getSetting, isReady: settingsReady } = useSettings();
  const [selectedItem, setSelectedItem] = useState(null);
  const [isNodeType, setIsNodeType] = useState(true);

  // 3D Force control state
  const [currentPreset, setCurrentPreset] = useState('balanced');
  const [forceConfig, setForceConfig] = useState({
    linkDistance: 100,
    chargeStrength: -300,
    centerStrength: 0.1,
    collisionRadius: 1.0
  });

  // Collapsible section state - load from settings
  const [isForceLayoutExpanded, setIsForceLayoutExpanded] = useState(() => {
    return getSetting('forceLayoutExpanded') !== false; // Default to expanded
  });

  // Layout presets for 3D force configuration
  const layoutPresets = {
    tight: {
      linkDistance: 30,
      chargeStrength: -100,
      centerStrength: 0.3,
      collisionRadius: 0.8,
      description: "Compact layout for detailed inspection"
    },
    balanced: {
      linkDistance: 100,
      chargeStrength: -300,
      centerStrength: 0.1,
      collisionRadius: 1.0,
      description: "Balanced layout for general use"
    },
    loose: {
      linkDistance: 180,
      chargeStrength: -500,
      centerStrength: 0.05,
      collisionRadius: 1.2,
      description: "Spacious layout for large graphs"
    },
    clustered: {
      linkDistance: 60,
      chargeStrength: -200,
      centerStrength: 0.2,
      collisionRadius: 0.9,
      description: "Grouped layout with stronger clustering"
    }
  };

  // Apply force configuration to 3D graph
  const applyForceConfig = useCallback((config) => {
    if (!forceGraphRef?.current || !is3DMode) return;

    console.log('🔧 Applying force configuration:', config);

    const graph = forceGraphRef.current;

    try {
      // Set force updating state to prevent hover events during re-initialization
      if (graph.setForceUpdating) {
        graph.setForceUpdating(true);
      }

      // Apply link force (attraction between connected nodes)
      graph.d3Force('link', d3.forceLink()
        .distance(config.linkDistance)
        .strength(0.1)
      );

      // Apply charge force (repulsion between all nodes)
      graph.d3Force('charge', d3.forceManyBody()
        .strength(config.chargeStrength)
      );

      // Apply center force (gravity toward center)
      graph.d3Force('center', d3.forceCenter()
        .strength(config.centerStrength)
      );

      // Apply collision force (prevent node overlap)
      graph.d3Force('collision', d3.forceCollide()
        .radius(config.collisionRadius * nodeSize)
        .strength(0.7)
      );

      // Restart simulation with new parameters
      graph.d3ReheatSimulation();

      // Clear force updating state after a short delay to allow graph to stabilize
      setTimeout(() => {
        if (graph.setForceUpdating) {
          graph.setForceUpdating(false);
        }
      }, 100); // 100ms delay should be sufficient for graph re-initialization

      // Notify parent component
      if (onForceConfigChange) {
        onForceConfigChange(config);
      }

    } catch (error) {
      console.error('Error applying force configuration:', error);
      // Ensure force updating state is cleared even on error
      if (graph.setForceUpdating) {
        graph.setForceUpdating(false);
      }
    }
  }, [forceGraphRef, is3DMode, nodeSize, onForceConfigChange]);

  // Apply layout preset
  const applyLayoutPreset = useCallback((presetName) => {
    const preset = layoutPresets[presetName];
    if (!preset) return;

    console.log(`🎨 Applying layout preset: ${presetName}`);
    setCurrentPreset(presetName);
    setForceConfig(preset);
    applyForceConfig(preset);

    // Save to settings
    setSetting('forceLayoutPreset', presetName);
    setSetting('forceConfig', preset);
  }, [layoutPresets, applyForceConfig, setSetting]);

  // Debounced force configuration to prevent WebGL context overload
  const debouncedApplyForceConfig = useCallback(
    debounce((config) => {
      applyForceConfig(config);
      setSetting('forceConfig', config);
    }, 150), // 150ms debounce to prevent excessive WebGL context recreation
    [applyForceConfig, setSetting]
  );

  // Handle individual force parameter changes with debouncing
  const handleForceParameterChange = useCallback((parameter, value) => {
    const newConfig = { ...forceConfig, [parameter]: value };
    setForceConfig(newConfig); // Update UI immediately
    debouncedApplyForceConfig(newConfig); // Apply to WebGL with debounce
  }, [forceConfig, debouncedApplyForceConfig]);

  // Toggle force layout section and save state
  const toggleForceLayoutExpanded = useCallback(() => {
    const newExpanded = !isForceLayoutExpanded;
    setIsForceLayoutExpanded(newExpanded);
    setSetting('forceLayoutExpanded', newExpanded);
  }, [isForceLayoutExpanded, setSetting]);

  // Get current values from settings service
  const nodeColors = useMemo(() => ({ ...defaultNodeColors, ...(settings?.nodeColors || {}) }), [settings?.nodeColors]);
  const relationshipColors = useMemo(() => ({ ...defaultRelationshipColors, ...(settings?.relationshipColors || {}) }), [settings?.relationshipColors]);
  const relationshipLineStyles = useMemo(() => (settings?.relationshipLineStyles || {}), [settings?.relationshipLineStyles]);
  const nodeSizes = useMemo(() => ({ ...defaultNodeSizes, ...(settings?.nodeSizes || {}) }), [settings?.nodeSizes]);
  const nodeShapes = useMemo(() => ({
    'Module': 'square',
    'Product': 'triangle',
    'Workflow': 'diamond',
    'UI_Area': 'circle',
    'ConfigurationItem': 'star',
    'TestCase': 'wye',
    'Document': 'svg:finance-book-svgrepo-com.svg',
    'Default': 'circle',
    ...(settings?.nodeShapes || {})
  }), [settings?.nodeShapes]);



  // No longer need to sync with initialConfig since we use settings service directly

  if (!data || !data.nodes || !data.links) {
    return null;
  }

  // Memoize node type counts calculation
  const nodeTypeCounts = useMemo(() => {
    const counts = {};
    const legacyCategories = ['Workflow', 'Module', 'Product', 'Role', 'Parameter', 'trading', 'Configuration', 'Feature', 'Entity']; // Categories to exclude from legend
    
    if (data && data.nodes) {
      data.nodes.forEach(node => {
        // UPDATED: Use node.properties.category for business-relevant legend labels
        // with fallback to existing node type logic for backward compatibility
        // NOTE: This affects ONLY legend display. Filtering still uses original node types
        // from getNodeType() functions in graph components for consistency.
        let nodeType = 'Default';

        // Priority 1: Use component category from Neo4j properties (new business logic)
        if (node.properties && node.properties.category) {
          nodeType = node.properties.category;
        }
        // Priority 2: Fallback to existing node type logic for backward compatibility
        else if (node.labels && node.labels.length > 0) {
          nodeType = node.labels[0];
        } else if (node.group) {
          nodeType = node.group;
        }

        // Skip legacy categories - don't show them in legend
        if (!legacyCategories.includes(nodeType)) {
          counts[nodeType] = (counts[nodeType] || 0) + 1;
        }
      });
    }
    return counts;
  }, [data?.nodes]);

  // Get node type visibility settings - default all to true (visible)
  const nodeTypeVisibility = useMemo(() => {
    const savedVisibility = getSetting('nodeTypeVisibility') || {};
    const allNodeTypes = Object.keys(nodeTypeCounts);
    const visibility = {};

    // Initialize all node types as visible by default
    allNodeTypes.forEach(nodeType => {
      visibility[nodeType] = savedVisibility[nodeType] !== undefined ? savedVisibility[nodeType] : true;
    });

    return visibility;
  }, [settings?.nodeTypeVisibility, nodeTypeCounts, getSetting]);

  // Calculate filtering statistics
  const filteringStats = useMemo(() => {
    const legacyCategories = ['Workflow', 'Module', 'Product', 'Role', 'Parameter', 'trading', 'Configuration', 'Feature', 'Entity']; // Categories to exclude from stats
    let totalNodes = 0;
    let legacyNodesCount = 0;
    
    // Count total nodes and legacy nodes
    if (data && data.nodes) {
      data.nodes.forEach(node => {
        let nodeType = 'Default';
        if (node.properties && node.properties.category) {
          nodeType = node.properties.category;
        } else if (node.labels && node.labels.length > 0) {
          nodeType = node.labels[0];
        } else if (node.group) {
          nodeType = node.group;
        }
        
        if (legacyCategories.includes(nodeType)) {
          legacyNodesCount++;
        }
        totalNodes++;
      });
    }
    
    const visibleNodeTypes = Object.entries(nodeTypeVisibility).filter(([_, isVisible]) => isVisible);
    const hiddenNodeTypes = Object.entries(nodeTypeVisibility).filter(([_, isVisible]) => !isVisible);

    let visibleNodesCount = 0;
    visibleNodeTypes.forEach(([nodeType, _]) => {
      visibleNodesCount += nodeTypeCounts[nodeType] || 0;
    });

    const isFiltering = hiddenNodeTypes.length > 0;

    return {
      totalNodes: totalNodes - legacyNodesCount, // Exclude legacy nodes from total
      visibleNodes: visibleNodesCount,
      hiddenNodes: (totalNodes - legacyNodesCount) - visibleNodesCount,
      isFiltering,
      visibleNodeTypes: visibleNodeTypes.length,
      totalNodeTypes: Object.keys(nodeTypeCounts).length
    };
  }, [data?.nodes?.length, nodeTypeVisibility, nodeTypeCounts]);

  // Memoize relationship type counts calculation
  const relationshipTypeCounts = useMemo(() => {
    const counts = {};
    if (data && data.links) {
      data.links.forEach(link => {
        const type = link.type || 'Unknown';
        counts[type] = (counts[type] || 0) + 1;
      });
    }
    return counts;
  }, [data?.links]);



  // Handle badge click to open the color picker
  const handleBadgeClick = (type, isNode = true) => {
    setSelectedItem(type);
    setIsNodeType(isNode);
  };

  // Handle checkbox change for node type visibility
  const handleNodeTypeVisibilityChange = (nodeType, isVisible) => {
    if (!settingsReady) {
      console.warn('Settings service not ready, skipping visibility change');
      return;
    }

    const newVisibility = { ...nodeTypeVisibility, [nodeType]: isVisible };
    setSetting('nodeTypeVisibility', newVisibility);

    // Notify parent component about the filtering change
    if (onFilterChange) {
      onFilterChange(newVisibility);
    }
  };

  // New handler for when the modal applies changes
  const handleModalApply = (type, changes) => {
    console.log(`Legend: Applying changes from modal for ${type}:`, changes);
    
    if (!settingsReady) {
      console.warn('Settings service not ready, skipping changes');
      handleCloseModal();
      return;
    }
    
    let configUpdate = {};
    let needsCallback = false;

    if (isNodeType) {
      // Node update
      if (changes.color) {
        const newNodeColors = { ...nodeColors, [type]: changes.color };
        setSetting('nodeColors', newNodeColors);
        configUpdate.colors = newNodeColors;
        configUpdate.isColorChange = true;
        needsCallback = true;
      }
      if (changes.size !== undefined) {
        const newNodeSizes = { ...nodeSizes, [type]: changes.size };
        setSetting('nodeSizes', newNodeSizes);
        configUpdate.sizes = newNodeSizes;
        configUpdate.isSizeChange = true;
        needsCallback = true;
      }
      if (changes.shape) {
        const newNodeShapes = { ...nodeShapes, [type]: changes.shape };
        setSetting('nodeShapes', newNodeShapes);
        configUpdate.shapes = newNodeShapes;
        configUpdate.isShapeChange = true;
        needsCallback = true;
      }
    } else {
      // Relationship update (color and line style)
      if (changes.color) {
        const newRelationshipColors = { ...relationshipColors, [type]: changes.color };
        setSetting('relationshipColors', newRelationshipColors);
        configUpdate.relationshipColors = newRelationshipColors;
        configUpdate.isColorChange = true;
        needsCallback = true;
      }
      if (changes.shape) { // shape holds line style string for relationships
        const newLineStyles = { ...relationshipLineStyles, [type]: changes.shape };
        setSetting('relationshipLineStyles', newLineStyles);
        configUpdate.relationshipLineStyles = newLineStyles;
        needsCallback = true;
      }
    }

    // Notify parent component only with the specific changes made
    if (needsCallback && onNodeConfigChange) {
      // Always include latest relationshipLineStyles in the update
      configUpdate.relationshipLineStyles = { ...relationshipLineStyles };

      delete configUpdate.isColorChange; 
      delete configUpdate.isSizeChange;
      delete configUpdate.isShapeChange;

      if (Object.keys(configUpdate).length > 0) {
        console.log(`Legend: Notifying parent with specific changes for ${type}`, configUpdate);
        onNodeConfigChange(configUpdate);
      } else {
        console.log(`Legend: No actual changes detected for ${type}, not notifying parent.`);
      }
    }

    // Close the modal
    handleCloseModal();
  };




  // Close the color picker modal
  const handleCloseModal = () => {
    setSelectedItem(null);
  };

  // Calculate appropriate badge width based on text length
  const getBadgeWidth = (text, count, isNode = true, nodeType = null) => {
    // Base text width (approximate)
    const textLength = `${text} (${count})`.length;
    const baseWidth = Math.max(40, textLength * 7); // 7px per character is approximate
    
    // For node badges, factor in node size
    if (isNode && nodeType && nodeType !== 'Default') {
      const nodeSize = nodeSizes[nodeType] || 20;
      // Blend text-based width with node size
      return Math.max(baseWidth, nodeSize * 2);
    }
    
    return baseWidth;
  };

  // Add a function to generate SVG path for each shape type
  const getShapePath = (type, size = 20) => {
    const scale = size / 20; // Scale based on default size of 20
    
    switch (type) {
      case 'Module':
        // Square
        return `M ${-10 * scale},${-10 * scale} h${20 * scale} v${20 * scale} h${-20 * scale} z`;
      case 'Product':
        // Triangle 
        return `M0,${-12 * scale} L${10 * scale},${8 * scale} L${-10 * scale},${8 * scale} z`;
      case 'Workflow':
        // Diamond
        return `M0,${-12 * scale} L${12 * scale},0 L0,${12 * scale} L${-12 * scale},0 z`;
      case 'UI_Area':
        // Circle
        return d3.symbol().type(d3.symbolCircle).size(Math.PI * size * size)();
      case 'ConfigurationItem':
        // Star
        return d3.symbol().type(d3.symbolStar).size(Math.PI * size * size)();
      case 'TestCase':
        // Y shape
        return d3.symbol().type(d3.symbolWye).size(Math.PI * size * size)();
      default:
        // Default circle
        return d3.symbol().type(d3.symbolCircle).size(Math.PI * size * size)();
    }
  };

  // Handle shape click from shape options
  const handleShapeClick = (type, shape) => {
    console.log(`Legend: Selected shape ${shape} for type ${type}`);
    
    if (!settingsReady) {
      console.warn('Settings service not ready, skipping shape change');
      return;
    }
    
    // Update settings
    const newNodeShapes = { ...nodeShapes, [type]: shape };
    setSetting('nodeShapes', newNodeShapes);
    
    // Pass to parent via onNodeConfigChange
    if (onNodeConfigChange) {
      console.log(`Legend: Notifying parent about direct shape selection for ${type} to ${shape}`);
      onNodeConfigChange({
        colors: nodeColors,
        sizes: nodeSizes,
        shapes: newNodeShapes,
        isShapeChange: true
      });
    }
  };

  return (
    <div className="legend-container">
      <div className="legend-header">
        {filteringStats.isFiltering && (
          <div className="filtering-stats" style={{
            fontSize: '0.85rem',
            color: '#00973A',
            fontWeight: 'bold',
            marginTop: '4px'
          }}>
            Showing {filteringStats.visibleNodes} of {filteringStats.totalNodes} nodes
            ({filteringStats.visibleNodeTypes} of {filteringStats.totalNodeTypes} types)
          </div>
        )}
        {onClose && (
          <button
            className="legend-close-btn"
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '1.2rem',
              cursor: 'pointer',
              position: 'absolute',
              top: '10px',
              right: '10px'
            }}
          >
            ×
          </button>
        )}
      </div>

      {/* Node Limit Control */}
      <div style={{
        marginBottom: '16px',
        padding: '8px',
        background: '#f8f9fa',
        borderRadius: '8px',
        border: '1px solid #e5e7eb',
        width: '100%',
        boxSizing: 'border-box'
      }}>
        {/* Node Limit Control */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: is3DMode ? '12px' : '0' }}>
          <span style={{ fontSize: '12px', fontWeight: '500' }}>Max Nodes:</span>
          <input
            type="number"
            min="0"
            max="10000"
            step="100"
            value={nodeLimit}
            onChange={(e) => setNodeLimit(parseInt(e.target.value) || 0)}
            style={{
              width: '70px',
              padding: '4px 6px',
              border: '1px solid #d1d5db',
              borderRadius: '4px',
              fontSize: '12px'
            }}
          />
          <span style={{ color: '#6b7280', fontSize: '11px' }}>
            {(() => {
              const totalNodes = data?.nodes?.length || 0;
              const performanceThreshold = is3DMode ? 1500 : 2500;
              
              if (nodeLimit === 0) {
                if (totalNodes > performanceThreshold) {
                  return `(${totalNodes} nodes ⚠️ performance)`;
                }
                return `(${totalNodes} nodes)`;
              }
              if (totalNodes <= nodeLimit) {
                return `(${totalNodes} nodes)`;
              }
              return `(${totalNodes} of ${Math.max(totalNodes, nodeLimit)} shown)`;
            })()}
          </span>
        </div>

        {/* 3D Mode Control Sliders */}
        {is3DMode && (
          <div style={{ borderTop: '1px solid #e5e7eb', paddingTop: '12px' }}>
            <div style={{ marginBottom: '8px', fontSize: '12px', fontWeight: '500', color: '#374151' }}>
              3D Controls
            </div>

            {/* Node Size Slider */}
            <div style={{ marginBottom: '12px' }}>
              <label style={{
                display: 'block',
                fontSize: '11px',
                fontWeight: '500',
                marginBottom: '4px',
                color: '#059669'
              }}>
                Node Size: {nodeSize.toFixed(1)}
              </label>
              <input
                type="range"
                min="0"
                max="10"
                step="0.1"
                value={nodeSize}
                onChange={(e) => onNodeSizeChange(parseFloat(e.target.value))}
                style={{
                  width: '100%',
                  height: '4px',
                  background: '#e5e7eb',
                  borderRadius: '2px',
                  outline: 'none',
                  cursor: 'pointer'
                }}
              />
            </div>

            {/* Force Layout Controls */}
            <div style={{ borderTop: '1px solid #e5e7eb', paddingTop: '12px', marginTop: '12px' }}>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  marginBottom: '8px',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#374151',
                  cursor: 'pointer',
                  padding: '4px 0'
                }}
                onClick={toggleForceLayoutExpanded}
              >
                <span>Force Layout</span>
                <span style={{
                  fontSize: '10px',
                  color: '#6b7280',
                  transform: isForceLayoutExpanded ? 'rotate(90deg)' : 'rotate(0deg)',
                  transition: 'transform 0.2s ease'
                }}>
                  ▶
                </span>
              </div>

              {/* Collapsible Force Layout Content */}
              {isForceLayoutExpanded && (
                <div style={{
                  animation: 'fadeIn 0.2s ease-in-out',
                  overflow: 'hidden'
                }}>
                  {/* Layout Presets */}
              <div style={{ marginBottom: '12px' }}>
                <label style={{
                  display: 'block',
                  fontSize: '11px',
                  fontWeight: '500',
                  marginBottom: '6px',
                  color: '#059669'
                }}>
                  Layout Presets
                </label>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '4px' }}>
                  {Object.entries(layoutPresets).map(([key, preset]) => (
                    <button
                      key={key}
                      onClick={() => applyLayoutPreset(key)}
                      style={{
                        padding: '4px 8px',
                        fontSize: '10px',
                        border: '1px solid #d1d5db',
                        borderRadius: '4px',
                        background: currentPreset === key ? '#059669' : '#f9fafb',
                        color: currentPreset === key ? 'white' : '#374151',
                        cursor: 'pointer',
                        transition: 'all 0.2s'
                      }}
                      title={preset.description}
                    >
                      {key.charAt(0).toUpperCase() + key.slice(1)}
                    </button>
                  ))}
                </div>
              </div>

              {/* Fine-tuning Sliders */}
              <div style={{ fontSize: '11px', fontWeight: '500', marginBottom: '8px', color: '#374151' }}>
                Fine-tuning
              </div>

              {/* Link Distance Slider */}
              <div style={{ marginBottom: '10px' }}>
                <label style={{
                  display: 'block',
                  fontSize: '10px',
                  fontWeight: '500',
                  marginBottom: '3px',
                  color: '#6b7280'
                }}>
                  Link Distance: {forceConfig.linkDistance}
                </label>
                <input
                  type="range"
                  min="20"
                  max="200"
                  step="5"
                  value={forceConfig.linkDistance}
                  onChange={(e) => handleForceParameterChange('linkDistance', parseInt(e.target.value))}
                  style={{
                    width: '100%',
                    height: '3px',
                    background: '#e5e7eb',
                    borderRadius: '2px',
                    outline: 'none',
                    cursor: 'pointer'
                  }}
                />
              </div>

              {/* Charge Strength Slider */}
              <div style={{ marginBottom: '10px' }}>
                <label style={{
                  display: 'block',
                  fontSize: '10px',
                  fontWeight: '500',
                  marginBottom: '3px',
                  color: '#6b7280'
                }}>
                  Repulsion: {Math.abs(forceConfig.chargeStrength)}
                </label>
                <input
                  type="range"
                  min="50"
                  max="800"
                  step="10"
                  value={Math.abs(forceConfig.chargeStrength)}
                  onChange={(e) => handleForceParameterChange('chargeStrength', -parseInt(e.target.value))}
                  style={{
                    width: '100%',
                    height: '3px',
                    background: '#e5e7eb',
                    borderRadius: '2px',
                    outline: 'none',
                    cursor: 'pointer'
                  }}
                />
              </div>

              {/* Center Strength Slider */}
              <div style={{ marginBottom: '10px' }}>
                <label style={{
                  display: 'block',
                  fontSize: '10px',
                  fontWeight: '500',
                  marginBottom: '3px',
                  color: '#6b7280'
                }}>
                  Center Gravity: {forceConfig.centerStrength.toFixed(2)}
                </label>
                <input
                  type="range"
                  min="0"
                  max="0.5"
                  step="0.01"
                  value={forceConfig.centerStrength}
                  onChange={(e) => handleForceParameterChange('centerStrength', parseFloat(e.target.value))}
                  style={{
                    width: '100%',
                    height: '3px',
                    background: '#e5e7eb',
                    borderRadius: '2px',
                    outline: 'none',
                    cursor: 'pointer'
                  }}
                />
              </div>

              {/* Collision Radius Slider */}
              <div style={{ marginBottom: '10px' }}>
                <label style={{
                  display: 'block',
                  fontSize: '10px',
                  fontWeight: '500',
                  marginBottom: '3px',
                  color: '#6b7280'
                }}>
                  Collision Radius: {forceConfig.collisionRadius.toFixed(1)}
                </label>
                <input
                  type="range"
                  min="0.5"
                  max="2.0"
                  step="0.1"
                  value={forceConfig.collisionRadius}
                  onChange={(e) => handleForceParameterChange('collisionRadius', parseFloat(e.target.value))}
                  style={{
                    width: '100%',
                    height: '3px',
                    background: '#e5e7eb',
                    borderRadius: '2px',
                    outline: 'none',
                    cursor: 'pointer'
                  }}
                />
              </div>

              {/* Reset Button */}
              <button
                onClick={() => applyLayoutPreset(currentPreset)}
                style={{
                  width: '100%',
                  padding: '6px',
                  fontSize: '10px',
                  border: '1px solid #d1d5db',
                  borderRadius: '4px',
                  background: '#f9fafb',
                  color: '#374151',
                  cursor: 'pointer',
                  marginTop: '8px'
                }}
              >
                Reset to {currentPreset.charAt(0).toUpperCase() + currentPreset.slice(1)}
              </button>
                </div>
              )}
            </div>

          </div>
        )}
      </div>

      <h3 className="legend-section-title">Node Categories</h3>
      <div className="legend-badges">
        <span className="legend-badge all-nodes" style={{
          backgroundColor: '#4b5563',
          color: 'white',
          padding: '4px 12px',
          borderRadius: '4px',
          border: 'none',
          display: 'inline-block',
          textAlign: 'center'
        }}>
          * ({filteringStats.totalNodes})
        </span>
        {Object.entries(nodeTypeCounts)
          .sort(([, countA], [, countB]) => countB - countA) // Sort by count descending
          .map(([type, count]) => {
          const isVisible = nodeTypeVisibility[type] !== false;
          return (
            <div
              key={type}
              className="legend-node-item"
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                gap: '6px',
                margin: '2px',
                padding: '2px 4px',
                borderRadius: '4px',
                backgroundColor: isVisible ? 'transparent' : 'rgba(0,0,0,0.1)'
              }}
            >
              <input
                type="checkbox"
                id={`node-filter-${type}`}
                checked={isVisible}
                onChange={(e) => handleNodeTypeVisibilityChange(type, e.target.checked)}
                style={{
                  accentColor: '#00973A',
                  cursor: 'pointer'
                }}
                title={`${isVisible ? 'Hide' : 'Show'} ${type} nodes`}
              />
              <span
                className="legend-badge"
                style={{
                  backgroundColor: nodeColors[type] || nodeColors.Default,
                  color: 'white',
                  padding: '4px 12px',
                  borderRadius: '4px',
                  minWidth: `${getBadgeWidth(type, count, true, type)}px`,
                  height: 'auto',
                  border: 'none',
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: '4px',
                  textAlign: 'center',
                  opacity: isVisible ? 1 : 0.5,
                  cursor: 'pointer'
                }}
                onClick={() => handleBadgeClick(type, true)}
                title="Click to customize"
              >
                <svg width="14" height="14" viewBox="-7 -7 14 14">
                  {nodeShapes[type] && nodeShapes[type].startsWith('svg:') ? (
                    <image
                      href={`/svg/${nodeShapes[type].substring(4)}`}
                      x="-7" y="-7" width="14" height="14"
                      preserveAspectRatio="xMidYMid meet"
                    />
                  ) : (
                    <path
                      d={d3.symbol()
                        .type(
                          (nodeShapes[type] && d3[`symbol${nodeShapes[type][0].toUpperCase()}${nodeShapes[type].slice(1)}`])
                          || d3.symbolCircle
                        )
                        .size(100)()
                      }
                      fill="white"
                      stroke="white"
                      strokeWidth="0.5"
                    />
                  )}
                </svg>
                {type} ({count})
              </span>
            </div>
          );
        })}
      </div>
      
      <h3 className="legend-section-title">Relationship types</h3>
      <div className="legend-badges">
        <span className="legend-badge all-relationships" style={{
          backgroundColor: '#64748b',
          color: 'white',
          padding: '4px 12px',
          borderRadius: '4px',
          border: 'none',
          display: 'inline-block',
          textAlign: 'center'
        }}>
          * ({data.links.length})
        </span>
        {Object.entries(relationshipTypeCounts).map(([type, count]) => (
          <span 
            key={type} 
            className="legend-badge relationship-badge"
            style={{ 
              backgroundColor: relationshipColors[type] || relationshipColors.Default,
              borderColor: relationshipColors[type] || relationshipColors.Default,
              color: 'white',
              padding: '4px 12px',
              borderRadius: '4px',
              minWidth: `${getBadgeWidth(type, count, false)}px`,
              height: 'auto',
              borderWidth: '2px',
              borderStyle: (relationshipLineStyles[type] === 'dashed' || relationshipLineStyles[type] === 'dotted') ? relationshipLineStyles[type] : 'solid',
              display: 'inline-block',
              textAlign: 'center'
            }}
            onClick={() => handleBadgeClick(type, false)}
            title="Click to customize"
          >
            {type} ({count})
          </span>
        ))}
      </div>
      
      <div className="legend-hint">Click on a badge to customize colors and sizes</div>
      
      {selectedItem && (
        <ColorPickerModal
          type={selectedItem}
          isNodeType={isNodeType}
          initialColor={isNodeType 
            ? (nodeColors[selectedItem] || nodeColors.Default)
            : (relationshipColors[selectedItem] || relationshipColors.Default)
          }
          initialSize={isNodeType ? (nodeSizes[selectedItem] || 20) : null}
          initialShape={isNodeType ? (nodeShapes[selectedItem] || 'circle') : null} // Pass shape name string
          onApply={handleModalApply} // Use the new consolidated callback
          onClose={handleCloseModal}
        />
      )}
    </div>
  );
};

export default React.memo(Legend);
