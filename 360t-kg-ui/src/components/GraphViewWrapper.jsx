import React, { Suspense, lazy } from 'react';
import GraphView from './GraphView';
import UnifiedGraphWrapper from './UnifiedGraphWrapper';

// Lazy load the 3D component to reduce initial bundle size
const Unified3DGraphWrapper = lazy(() => import('./Unified3DGraphWrapper'));

// Simple Error Boundary for 3D component loading
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('3D Graph Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback;
      return <FallbackComponent error={this.state.error} resetError={() => this.setState({ hasError: false, error: null })} />;
    }

    return this.props.children;
  }
}

// Loading component for 3D graph lazy loading
const LoadingFallback = () => (
  <div className="w-full h-full flex items-center justify-center bg-gray-50">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
      <p className="text-gray-600">Loading 3D visualization...</p>
    </div>
  </div>
);

// Error boundary component for 3D loading failures
const ErrorFallback = ({ error, resetError }) => (
  <div className="w-full h-full flex items-center justify-center bg-red-50">
    <div className="text-center p-6">
      <div className="text-red-600 mb-4">
        <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <h3 className="text-lg font-semibold text-red-800 mb-2">Failed to load 3D visualization</h3>
      <p className="text-red-600 mb-4">Error: {error?.message || 'Unknown error'}</p>
      <button
        onClick={resetError}
        className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
      >
        Try Again
      </button>
    </div>
  </div>
);

/**
 * Graph View Wrapper component that switches between D3.js, React Force Graph 2D, and React Force Graph 3D implementations
 * Based on the VITE_USE_REACT_FORCE_GRAPH environment variable and is3DMode prop
 */
const GraphViewWrapper = React.forwardRef((props, ref) => {
  const { is3DMode = false, ...otherProps } = props;

  // Check environment variable for feature flag
  const useReactForceGraph = import.meta.env.VITE_USE_REACT_FORCE_GRAPH === 'true';
  const enable3DMode = import.meta.env.VITE_ENABLE_3D_MODE === 'true';

  console.log('GraphViewWrapper: Using React Force Graph:', useReactForceGraph, '| 3D Mode:', is3DMode, '| 3D Enabled:', enable3DMode);

  // 3D mode takes precedence if enabled and requested
  if (useReactForceGraph && enable3DMode && is3DMode) {
    return (
      <Suspense fallback={<LoadingFallback />}>
        <ErrorBoundary fallback={ErrorFallback}>
          <Unified3DGraphWrapper ref={ref} {...otherProps} />
        </ErrorBoundary>
      </Suspense>
    );
  }

  // 2D React Force Graph mode
  if (useReactForceGraph) {
    return <UnifiedGraphWrapper {...otherProps} />;
  }

  // Fallback to original D3.js GraphView
  // For the old GraphView, we don't pass React Force Graph specific props since it doesn't support them
  const { onCenterOnNodeReady, onNodeClick, ...graphViewProps } = otherProps;
  return <GraphView {...graphViewProps} />;
});

export default GraphViewWrapper;
