/* EntityBadge.css */

.entity-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  border: 1px solid;
  border-radius: 9999px;
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
  position: relative;
  user-select: none;
}

/* Size Variants */
.entity-badge-small {
  padding: 0.125rem 0.5rem;
  font-size: 0.75rem;
  line-height: 1.2;
}

.entity-badge-medium {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.3;
}

.entity-badge-large {
  padding: 0.375rem 1rem;
  font-size: 1rem;
  line-height: 1.4;
}

/* Clickable State */
.entity-badge.clickable {
  cursor: pointer;
}

.entity-badge.clickable:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.entity-badge.clickable:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.entity-badge.clickable:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Variant Styles */
.entity-badge.filled {
  /* Styles applied via inline styles based on color prop */
}

.entity-badge.outlined {
  background-color: transparent !important;
  border-width: 1.5px;
}

.entity-badge.outlined:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.entity-badge.subtle {
  border-width: 1px;
}

.entity-badge.subtle:hover {
  opacity: 0.8;
}

/* Badge Label */
.badge-label {
  font-weight: inherit;
  letter-spacing: 0.025em;
}

/* Badge Count */
.badge-count {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 1.25rem;
  height: 1.25rem;
  padding: 0 0.25rem;
  background-color: rgba(255, 255, 255, 0.9);
  color: #374151;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.entity-badge-small .badge-count {
  min-width: 1rem;
  height: 1rem;
  font-size: 0.625rem;
}

.entity-badge-large .badge-count {
  min-width: 1.5rem;
  height: 1.5rem;
  font-size: 0.875rem;
}

/* Tooltip */
.entity-badge-tooltip {
  position: fixed;
  z-index: 1000;
  pointer-events: none;
  max-width: 250px;
  animation: badgeTooltipFadeIn 0.2s ease-out;
}

@keyframes badgeTooltipFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-100%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(-100%) scale(1);
  }
}

.entity-badge-tooltip .tooltip-content {
  background-color: #1f2937;
  color: #ffffff;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
  font-size: 0.875rem;
  line-height: 1.4;
}

.entity-badge-tooltip .tooltip-text {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.entity-badge-tooltip .tooltip-count {
  color: #d1d5db;
  font-size: 0.75rem;
  font-weight: 400;
}

.entity-badge-tooltip .tooltip-arrow {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #1f2937;
}

/* Category-specific default colors (fallback) */
.entity-badge[data-category="EMS"] {
  background-color: #40C4FF;
  border-color: #29B6F6;
  color: #ffffff;
}

.entity-badge[data-category="SEP"] {
  background-color: #A5D6A7;
  border-color: #81C784;
  color: #1B5E20;
}

.entity-badge[data-category="MMC"] {
  background-color: #FFAB91;
  border-color: #FF8A65;
  color: #BF360C;
}

.entity-badge[data-category="TRADING"] {
  background-color: #E1BEE7;
  border-color: #CE93D8;
  color: #4A148C;
}

.entity-badge[data-category="RISK"] {
  background-color: #FFCDD2;
  border-color: #EF9A9A;
  color: #B71C1C;
}

.entity-badge[data-category="COMPLIANCE"] {
  background-color: #FFF9C4;
  border-color: #FFF176;
  color: #F57F17;
}

.entity-badge[data-category="WORKFLOW"] {
  background-color: #C8E6C9;
  border-color: #A5D6A7;
  color: #1B5E20;
}

.entity-badge[data-category="SYSTEM"] {
  background-color: #BBDEFB;
  border-color: #90CAF9;
  color: #0D47A1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .entity-badge-medium {
    padding: 0.1875rem 0.625rem;
    font-size: 0.8125rem;
  }

  .entity-badge-large {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
  }

  .entity-badge-tooltip {
    max-width: 200px;
  }

  .entity-badge-tooltip .tooltip-content {
    padding: 0.375rem 0.5rem;
    font-size: 0.8125rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .entity-badge {
    border-width: 2px;
  }

  .entity-badge.outlined {
    border-width: 2px;
  }

  .badge-count {
    background-color: #ffffff;
    color: #000000;
    border: 1px solid #000000;
  }

  .entity-badge-tooltip .tooltip-content {
    background-color: #000000;
    border: 1px solid #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .entity-badge {
    transition: none;
  }

  .entity-badge.clickable:hover {
    transform: none;
  }

  .entity-badge-tooltip {
    animation: none;
  }
}

/* Focus visible for better keyboard navigation */
.entity-badge:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .entity-badge {
    background-color: transparent !important;
    color: #000000 !important;
    border: 1px solid #000000 !important;
  }

  .badge-count {
    background-color: #ffffff !important;
    color: #000000 !important;
    border: 1px solid #000000 !important;
  }

  .entity-badge-tooltip {
    display: none;
  }
}
