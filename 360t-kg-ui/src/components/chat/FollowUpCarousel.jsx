import React, { useState, useEffect, useCallback, useRef } from 'react';
import './FollowUpCarousel.css';

/**
 * FollowUpCarousel - Modern carousel component for displaying follow-up questions
 * 
 * Features:
 * - Single question display with smooth slide transitions
 * - Auto-advance every 4 seconds with pause on hover
 * - Navigation arrows (previous/next)  
 * - Touch/swipe gesture support for mobile
 * - Keyboard navigation accessibility
 * - Click-to-select functionality
 * - Responsive design matching 360T theme
 * 
 * Why carousel pattern:
 * - Reduces visual clutter by showing one question at a time
 * - Maintains user focus on current suggestion
 * - Provides smooth auto-discovery of available questions
 * - Works well on mobile with touch interactions
 */
const FollowUpCarousel = ({
  questions = [],
  onQuestionSelect,
  autoAdvanceInterval = 4000,
  showNavigation = true,
  showPagination = true,
  disabled = false,
  className = ''
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);
  
  const carouselRef = useRef(null);
  const autoAdvanceRef = useRef(null);

  // Normalize question text (same logic as FollowUpCards)
  const normalizeQuestionText = useCallback((text) => {
    if (!text) return '';
    
    let normalized = text;
    if (text === text.toUpperCase() && text.length > 3) {
      normalized = text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
    }
    
    normalized = normalized
      .replace(/\s+/g, ' ')
      .replace(/\?\s*\?+/g, '?')
      .replace(/\.\s*\.+/g, '.')
      .trim();
    
    if (normalized && !normalized.match(/[.?!]$/)) {
      normalized += '?';
    }
    
    return normalized;
  }, []);

  // Auto-advance functionality - rotates through questions automatically
  useEffect(() => {
    // Don't auto-advance if only one question, paused by user hover, or disabled
    if (questions.length <= 1 || isPaused || disabled) {
      console.debug('[FollowUpCarousel] Auto-advance disabled:', { 
        questionsCount: questions.length, 
        isPaused, 
        disabled 
      });
      return;
    }

    console.debug('[FollowUpCarousel] Starting auto-advance timer:', {
      interval: autoAdvanceInterval,
      questionsCount: questions.length
    });

    autoAdvanceRef.current = setInterval(() => {
      setCurrentIndex((prev) => {
        const newIndex = (prev + 1) % questions.length;
        console.debug('[FollowUpCarousel] Auto-advancing:', { from: prev, to: newIndex });
        return newIndex;
      });
    }, autoAdvanceInterval);

    return () => {
      if (autoAdvanceRef.current) {
        console.debug('[FollowUpCarousel] Clearing auto-advance timer');
        clearInterval(autoAdvanceRef.current);
      }
    };
  }, [questions.length, isPaused, disabled, autoAdvanceInterval]);

  // Navigation handlers - manage manual carousel navigation
  const goToNext = useCallback(() => {
    // Prevent navigation during transitions or if only one question
    if (isTransitioning || questions.length <= 1) return;
    
    console.debug('[FollowUpCarousel] Manual next navigation triggered');
    setIsTransitioning(true);
    setCurrentIndex((prev) => {
      const newIndex = (prev + 1) % questions.length;
      console.debug('[FollowUpCarousel] Moving to next:', { from: prev, to: newIndex });
      return newIndex;
    });
    // Brief transition state prevents rapid clicking and provides smooth UX
    setTimeout(() => setIsTransitioning(false), 300);
  }, [questions.length, isTransitioning]);

  const goToPrevious = useCallback(() => {
    // Prevent navigation during transitions or if only one question
    if (isTransitioning || questions.length <= 1) return;
    
    console.debug('[FollowUpCarousel] Manual previous navigation triggered');
    setIsTransitioning(true);
    setCurrentIndex((prev) => {
      // Handle wrap-around for previous navigation (add length before modulo)
      const newIndex = (prev - 1 + questions.length) % questions.length;
      console.debug('[FollowUpCarousel] Moving to previous:', { from: prev, to: newIndex });
      return newIndex;
    });
    setTimeout(() => setIsTransitioning(false), 300);
  }, [questions.length, isTransitioning]);

  const goToIndex = useCallback((index) => {
    // Prevent navigation if already at target index or during transitions
    if (isTransitioning || index === currentIndex) return;
    
    console.debug('[FollowUpCarousel] Direct navigation to index:', { from: currentIndex, to: index });
    setIsTransitioning(true);
    setCurrentIndex(index);
    setTimeout(() => setIsTransitioning(false), 300);
  }, [currentIndex, isTransitioning]);

  // Touch handlers for mobile swipe support - enables natural gesture navigation
  const handleTouchStart = (e) => {
    console.debug('[FollowUpCarousel] Touch start registered');
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e) => {
    // Track current touch position for swipe calculation
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    // Calculate swipe distance and direction for gesture recognition
    if (!touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;  // Left swipe = next question
    const isRightSwipe = distance < -50; // Right swipe = previous question

    console.debug('[FollowUpCarousel] Touch gesture detected:', {
      distance,
      isLeftSwipe,
      isRightSwipe,
      touchStart,
      touchEnd
    });

    if (isLeftSwipe) {
      goToNext();
    } else if (isRightSwipe) {
      goToPrevious();
    }
  };

  // Keyboard navigation
  const handleKeyDown = (event) => {
    if (disabled) return;

    switch (event.key) {
      case 'ArrowLeft':
        event.preventDefault();
        goToPrevious();
        break;
      case 'ArrowRight':
        event.preventDefault();
        goToNext();
        break;
      case 'Enter':
      case ' ':
        event.preventDefault();
        handleQuestionClick();
        break;
      default:
        break;
    }
  };

  // Question selection handler - processes user click/keyboard selection
  const handleQuestionClick = useCallback(() => {
    // Guard against invalid states
    if (disabled || !onQuestionSelect || questions.length === 0) {
      console.debug('[FollowUpCarousel] Question click ignored:', { disabled, hasHandler: !!onQuestionSelect, questionsCount: questions.length });
      return;
    }

    const currentQuestion = questions[currentIndex];
    // Support both string questions and object questions with .question property
    const rawQuestionText = typeof currentQuestion === 'string' 
      ? currentQuestion 
      : currentQuestion.question;
    const normalizedQuestionText = normalizeQuestionText(rawQuestionText);
    
    console.debug('[FollowUpCarousel] Question selected:', {
      index: currentIndex,
      raw: rawQuestionText,
      normalized: normalizedQuestionText
    });
    
    onQuestionSelect(normalizedQuestionText);
  }, [currentIndex, questions, onQuestionSelect, normalizeQuestionText, disabled]);

  // Reset carousel to first question when questions array changes
  useEffect(() => {
    console.debug('[FollowUpCarousel] Questions changed, resetting to index 0:', {
      newCount: questions.length,
      previousIndex: currentIndex
    });
    setCurrentIndex(0);
  }, [questions]);

  // Early return for empty state - no questions to display
  if (questions.length === 0) {
    console.debug('[FollowUpCarousel] No questions provided, showing empty state');
    return (
      <div className="follow-up-carousel-empty">
        <div className="empty-icon">💭</div>
        <div className="empty-message">No follow-up questions available</div>
      </div>
    );
  }

  // Extract current question data for rendering
  const currentQuestion = questions[currentIndex];
  // Handle both simple string questions and complex object questions with metadata
  const rawQuestionText = typeof currentQuestion === 'string' 
    ? currentQuestion 
    : currentQuestion.question;
  const questionText = normalizeQuestionText(rawQuestionText);
  // Context hints provide additional information about the question's scope
  const contextHint = typeof currentQuestion === 'object' ? currentQuestion.context_hint : null;

  console.debug('[FollowUpCarousel] Rendering question:', {
    currentIndex,
    questionText,
    hasContextHint: !!contextHint,
    totalQuestions: questions.length
  });

  return (
    <div 
      className={`follow-up-carousel ${disabled ? 'disabled' : ''} ${className}`}
      onMouseEnter={() => setIsPaused(true)}
      onMouseLeave={() => setIsPaused(false)}
      onKeyDown={handleKeyDown}
      tabIndex={disabled ? -1 : 0}
      role="region"
      aria-label="Follow-up questions carousel"
      ref={carouselRef}
    >
      {/* Main carousel container */}
      <div 
        className="carousel-container"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Navigation arrows */}
        {showNavigation && questions.length > 1 && (
          <>
            <button
              className="nav-arrow nav-arrow-prev"
              onClick={goToPrevious}
              disabled={disabled || isTransitioning}
              aria-label="Previous question"
              type="button"
            >
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polyline points="15,18 9,12 15,6" />
              </svg>
            </button>

            <button
              className="nav-arrow nav-arrow-next"
              onClick={goToNext}
              disabled={disabled || isTransitioning}
              aria-label="Next question"
              type="button"
            >
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polyline points="9,18 15,12 9,6" />
              </svg>
            </button>
          </>
        )}

        {/* Question card */}
        <div 
          className={`question-card ${isTransitioning ? 'transitioning' : ''}`}
          onClick={handleQuestionClick}
          role="button"
          tabIndex={disabled ? -1 : 0}
          aria-label={`Ask: ${questionText}`}
          aria-disabled={disabled}
        >
          <div className="card-content">
            <div className="question-text">
              {questionText}
            </div>
            
            {contextHint && (
              <div className="context-hint">
                {contextHint}
              </div>
            )}
          </div>


          {/* Glass morphism overlay */}
          <div className="card-overlay" />
        </div>
      </div>


      {/* Auto-advance pause indicator */}
      {isPaused && questions.length > 1 && !disabled && (
        <div className="pause-indicator">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
          </svg>
        </div>
      )}
    </div>
  );
};

export default React.memo(FollowUpCarousel);