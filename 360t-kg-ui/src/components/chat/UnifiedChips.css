.unified-chips { margin-top: 2px; }

.unified-chip .MuiChip-label {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding-right: 8px;
  padding-left: 8px;
}

.chip-label-wrap { display: inline-flex; align-items: center; gap: 6px; }
.chip-label { max-width: 22ch; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
.chip-score { font-weight: 600; font-size: 0.72rem; color: var(--360t-primary, #00973A); }

/* Enhanced Graphiti chips with score colors */
.enhanced-chip {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-width: 2px;
}

.enhanced-chip.score-dark-green {
  border-color: #059669;
  background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%);
  box-shadow: 0 2px 8px rgba(5, 150, 105, 0.15);
  color: #064e3b;
}

.enhanced-chip.score-green {
  border-color: #10b981;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(16, 185, 129, 0.04) 100%);
  color: #047857;
}

.enhanced-chip.score-yellow {
  border-color: #f59e0b;
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.08) 0%, rgba(245, 158, 11, 0.04) 100%);
  color: #92400e;
}

.enhanced-chip.score-orange {
  border-color: #f97316;
  background: linear-gradient(135deg, rgba(249, 115, 22, 0.08) 0%, rgba(249, 115, 22, 0.04) 100%);
  color: #9a3412;
}

.enhanced-chip.score-red {
  border-color: #ef4444;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.08) 0%, rgba(239, 68, 68, 0.04) 100%);
  color: #7f1d1d;
}

.enhanced-chip.score-gray {
  border-color: #6b7280;
  background: rgba(107, 114, 128, 0.05);
  color: #1f2937;
}

.enhanced-chip .MuiChip-label {
  font-weight: 500;
  color: inherit;
}

.enhanced-chip .chip-fact-truncated {
  color: inherit;
  opacity: 0.85;
}

/* Interactive states for clickable chips */
.enhanced-chip[clickable="true"]:hover:not(.loading) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

/* Score badges */
.score-badge {
  font-weight: 700 !important;
}

.score-badge.score-dark-green,
.score-badge.score-green {
  background: #10b981 !important;
  color: white !important;
}

.score-badge.score-yellow {
  background: #f59e0b !important;
  color: white !important;
}

.score-badge.score-orange,
.score-badge.score-red {
  background: #ef4444 !important;
  color: white !important;
}

.score-badge.score-gray {
  background: #6b7280 !important;
  color: white !important;
}

/* Confidence level indicators */
.confidence-high {
  color: #10b981 !important;
}

.confidence-medium {
  color: #f59e0b !important;
}

.confidence-low {
  color: #ef4444 !important;
}

/* Business category groupings */
.category-group {
  margin-bottom: 16px;
}

.category-group-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 600 !important;
  font-size: 14px !important;
}

.category-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

/* Enhanced Graphiti container */
.enhanced-graphiti-related {
  padding: 4px 0;
}

/* Loading states for search resolution */
.enhanced-chip.loading {
  opacity: 0.7;
  pointer-events: none;
  cursor: default !important;
}

.enhanced-chip.loading:hover {
  transform: none !important;
  box-shadow: none !important;
}


/* Truncated relationship fact next to relationship chips */
.chip-fact-truncated {
  color: #374151; /* slate-700 */
  opacity: 0.9;
  line-height: 1;
}

.enhanced-chip-tooltip-wrapper {
  display: inline-flex;
}
