import React from 'react';
import StructuredResponse from './StructuredResponse';

const MockResponseTest = ({ onNodeSelect }) => {
  // Mock v2.0 response data to test entity display
  const mockResponse = {
    "version": "2.0",
    "answer": "A spot transaction is an immediate execution trade without settlement delay. Reference entities: [1] and [2].",
    "sections": [
      {
        "title": "Spot Trading Definition",
        "content": "Spot trading involves immediate execution and settlement of financial instruments.",
        "entity_refs": ["spot-trading-001", "settlement-002"]
      }
    ],
    "sources": [
      {
        "id": "src-001",
        "title": "360T Spot Trading Guide",
        "url": "https://docs.360t.com/spot-trading.pdf",
        "document_type": "PDF",
        "snippet": "Spot transactions are executed immediately...",
        "category": "TRADING"
      }
    ],
    "entities": [
      {
        "id": "spot-trading-001",
        "name": "Spot Trading",
        "description": "Immediate execution trading without settlement delay",
        "category": "TRADING",
        "relevance_score": 0.95,
        "properties": {
          "type": "trading_method",
          "real_time": true
        }
      },
      {
        "id": "settlement-002", 
        "name": "Settlement Process",
        "description": "Process of completing financial transactions",
        "category": "EMS",
        "relevance_score": 0.82,
        "properties": {
          "type": "process"
        }
      },
      {
        "id": "risk-mgmt-003",
        "name": "Risk Management",
        "description": "Controls and monitoring for trading activities", 
        "category": "RISK",
        "relevance_score": 0.75,
        "properties": {
          "automated": true
        }
      },
      {
        "id": "compliance-004",
        "name": "Trade Compliance",
        "description": "Regulatory compliance for spot trades",
        "category": "COMPLIANCE", 
        "relevance_score": 0.68,
        "properties": {
          "mandatory": true
        }
      },
      {
        "id": "market-data-005",
        "name": "Market Data Feed",
        "description": "Real-time market information for pricing",
        "category": "SYSTEM",
        "relevance_score": 0.60,
        "properties": {
          "real_time": true
        }
      }
    ],
    "badges": [
      {
        "label": "TRADING",
        "tooltip": "Trading Operations",
        "color": "#E1BEE7",
        "count": 2
      },
      {
        "label": "EMS",
        "tooltip": "Energy Management System", 
        "color": "#40C4FF",
        "count": 1
      },
      {
        "label": "RISK",
        "tooltip": "Risk Management",
        "color": "#FFCDD2",
        "count": 1
      }
    ],
    "follow_up": [
      {
        "question": "What are the risk controls for spot trading?",
        "context_hint": "Risk management"
      },
      {
        "question": "How does settlement work for spot trades?", 
        "context_hint": "Settlement process"
      }
    ],
    "metadata": {
      "confidence_score": 0.92,
      "token_usage": 450,
      "fallback_used": false,
      "processing_time_ms": 1250,
      "entity_count": 5,
      "source_count": 1
    }
  };

  return (
    <div style={{ padding: '20px', backgroundColor: '#f9f9f9', margin: '10px 0' }}>
      <h3 style={{ marginBottom: '15px', color: '#333' }}>Mock v2.0 Response Test</h3>
      <StructuredResponse
        response={mockResponse}
        onNodeSelect={onNodeSelect}
        onSendMessage={(msg) => console.log('Mock send message:', msg)}
        showDebugInfo={true}
      />
    </div>
  );
};

export default MockResponseTest;