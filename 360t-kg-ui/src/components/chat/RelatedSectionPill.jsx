import React, { useMemo, useState, useCallback, useEffect, useRef } from 'react';
import UnifiedChips from './UnifiedChips';
import '../../styles/RelatedSectionPill.css';

/**
 * RelatedSectionPill - Collapsible pill component for related entities, facts, and edges
 *
 * Replaces the accordion-style RELATED section in FormatCRenderer with a compact pill
 * positioned at the bottom left corner of chat answers. Follows the same pattern as
 * ChatCategoryFilter for consistent positioning and behavior.
 *
 * Features:
 * - Collapsed state: Shows count of related items (e.g., "📍 5 related ▼")
 * - Expanded state: Popover containing UnifiedChips with all related content
 * - Smart positioning: Auto-detects available space and positions above/below
 * - Responsive design: Adapts to mobile and desktop layouts
 * - Accessibility: Full keyboard navigation and ARIA labels
 *
 * @param {Array} entities - Array of related entities from Graphiti response
 * @param {Array} facts - Array of additional facts/results
 * @param {Array} edges - Array of relationship edges
 * @param {Function} onEntitySelect - Callback when entity is selected for node details
 * @param {boolean} showAllEntities - Whether to expand all chips initially
 * @param {Function} onShowAllToggle - Callback for show all/less toggle
 * @param {boolean} disabled - Whether the pill is disabled
 */
const RelatedSectionPill = ({
  entities = [],
  facts = [],
  edges = [],
  onEntitySelect,
  onRelationshipSelect,
  showAllEntities = false,
  onShowAllToggle,
  disabled = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [openAbove, setOpenAbove] = useState(true);
  const containerRef = useRef(null);

  // Calculate total count of related items
  const totalCount = useMemo(() => {
    return entities.length + facts.length + edges.length;
  }, [entities.length, facts.length, edges.length]);

  // Don't render if no related items
  if (totalCount === 0) {
    return null;
  }

  // Handle click outside to close popover
  useEffect(() => {
    if (!isOpen) return;

    const handleClickAway = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    const handleEscape = (event) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickAway);
    document.addEventListener('keydown', handleEscape);

    return () => {
      document.removeEventListener('mousedown', handleClickAway);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen]);

  // Toggle pill open/closed with smart positioning
  const toggleOpen = useCallback(() => {
    if (disabled) return;

    setIsOpen((prev) => {
      const next = !prev;

      // Calculate positioning when opening
      if (!prev) {
        requestAnimationFrame(() => {
          if (!containerRef.current) return;

          const rect = containerRef.current.getBoundingClientRect();
          const availableAbove = rect.top;
          const availableBelow = window.innerHeight - rect.bottom;

          // Position above if more space available above, otherwise below
          setOpenAbove(availableAbove >= availableBelow);
        });
      }

      return next;
    });
  }, [disabled]);

  // Handle keyboard navigation
  const handleKeyDown = useCallback((event) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      toggleOpen();
    }
  }, [toggleOpen]);

  // Generate descriptive text for pill
  const getPillText = () => {
    if (totalCount === 1) return '1 related';
    return `${totalCount} related`;
  };

  return (
    <div
      ref={containerRef}
      className={`related-pill-container ${isOpen ? 'open' : ''}`}
    >
      {/* Pill Button */}
      <button
        type="button"
        className={`related-pill ${disabled ? 'disabled' : ''}`}
        onClick={toggleOpen}
        onKeyDown={handleKeyDown}
        aria-haspopup="true"
        aria-expanded={isOpen}
        aria-label={`Toggle related information. ${getPillText()}`}
        disabled={disabled}
      >
        <span className="related-pill__icon" aria-hidden="true">📍</span>
        <span className="related-pill__text">{getPillText()}</span>
        <span
          className={`related-pill__chevron ${isOpen ? 'open' : ''}`}
          aria-hidden="true"
        >
          <svg
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2.4"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <polyline points="6 9 12 15 18 9" />
          </svg>
        </span>
      </button>

      {/* Popover with Related Content */}
      {isOpen && (
        <div
          className={`related-popover ${openAbove ? 'pop-above' : 'pop-below'}`}
          role="dialog"
          aria-label="Related information"
        >
          <div className="related-popover__header">
            <h3 className="related-popover__title">Related</h3>
            {(totalCount > 30) && typeof onShowAllToggle === 'function' && (
              <button
                type="button"
                className="related-popover__toggle"
                onClick={onShowAllToggle}
                aria-label={showAllEntities ? 'Show fewer items' : 'Show all items'}
              >
                {showAllEntities ? 'Show Less' : 'Show All'}
              </button>
            )}
          </div>

          <div className="related-popover__content">
            <UnifiedChips
              entities={entities}
              facts={facts}
              edges={edges}
              onEntitySelect={onEntitySelect}
              onRelationshipSelect={onRelationshipSelect}
              expand={showAllEntities}
              maxVisible={30}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default RelatedSectionPill;
