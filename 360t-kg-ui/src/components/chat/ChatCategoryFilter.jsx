import React, { useMemo, useState, useCallback, useEffect, useRef } from 'react';
import configStore from '../../stores/configStore';
import { getAllCategoryColors, getCategoryColor, getContrastTextColor } from '../../constants/categoryColors';
import '../../styles/ChatCategoryFilter.css';

const createCategorySet = (allCategories, filters) => {
  if (Array.isArray(filters)) {
    return new Set(filters.filter((category) => allCategories.includes(category)));
  }
  if (filters === null || typeof filters === 'undefined') {
    return new Set(allCategories);
  }
  return new Set();
};

const ChatCategoryFilter = ({ disabled = false }) => {
  const allCategories = useMemo(() => Object.keys(getAllCategoryColors()), []);
  const [mode, setMode] = useState(() => configStore.getConfig().mode);
  const [isOpen, setIsOpen] = useState(false);
  const [openAbove, setOpenAbove] = useState(true);
  const [selectedCategories, setSelectedCategories] = useState(() => {
    const config = configStore.getConfig();
    return createCategorySet(allCategories, config?.graphiti?.categoryFilters);
  });
  const containerRef = useRef(null);

  const persistCategoryFilters = useCallback((categorySet) => {
    const filters = (() => {
      if (categorySet.size === 0) return [];
      if (categorySet.size === allCategories.length) return null;
      return Array.from(categorySet)
        .filter((category) => allCategories.includes(category))
        .sort();
    })();

    configStore.updateGraphiti({ categoryFilters: filters });
  }, [allCategories]);

  useEffect(() => {
    const unsubscribe = configStore.subscribe(
      (state) => state.config,
      (nextConfig) => {
        setMode(nextConfig.mode);
        setIsOpen(false);
        const filters = nextConfig?.graphiti?.categoryFilters;
        setSelectedCategories((prev) => {
          const candidate = createCategorySet(allCategories, filters);
          if (candidate.size === prev.size) {
            for (const category of candidate) {
              if (!prev.has(category)) {
                return candidate;
              }
            }
            for (const category of prev) {
              if (!candidate.has(category)) {
                return candidate;
              }
            }
            return prev;
          }
          return candidate;
        });
      }
    );

    return () => {
      if (typeof unsubscribe === 'function') {
        unsubscribe();
      }
    };
  }, [allCategories]);

  useEffect(() => {
    if (!isOpen) return;

    const handleClickAway = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    const handleEsc = (event) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickAway);
    document.addEventListener('keydown', handleEsc);
    return () => {
      document.removeEventListener('mousedown', handleClickAway);
      document.removeEventListener('keydown', handleEsc);
    };
  }, [isOpen]);

  const toggleOpen = useCallback(() => {
    if (disabled) return;

    setIsOpen((prev) => {
      const next = !prev;
      if (!prev) {
        requestAnimationFrame(() => {
          if (!containerRef.current) return;
          const rect = containerRef.current.getBoundingClientRect();
          const availableAbove = rect.top;
          const availableBelow = window.innerHeight - rect.bottom;
          setOpenAbove(availableAbove >= availableBelow);
        });
      }
      return next;
    });
  }, [disabled]);

  const handleCategoryToggle = useCallback((category) => {
    if (disabled) {
      return;
    }

    setSelectedCategories((prev) => {
      const next = new Set(prev);
      if (next.has(category)) {
        next.delete(category);
      } else {
        next.add(category);
      }
      persistCategoryFilters(next);
      return next;
    });
  }, [disabled, persistCategoryFilters]);

  const handleSelectAll = useCallback(() => {
    if (disabled) {
      return;
    }

    setSelectedCategories(() => {
      const next = new Set(allCategories);
      persistCategoryFilters(next);
      return next;
    });
  }, [allCategories, disabled, persistCategoryFilters]);

  const handleClearAll = useCallback(() => {
    if (disabled) {
      return;
    }

    setSelectedCategories(() => {
      const next = new Set();
      persistCategoryFilters(next);
      return next;
    });
  }, [disabled, persistCategoryFilters]);

  const selectedCount = selectedCategories.size;
  const summaryText = (() => {
    if (selectedCount === allCategories.length) return 'All categories';
    if (selectedCount === 0) return 'None selected';
    return `${selectedCount} selected`;
  })();

  if (mode !== 'graphiti') {
    return null;
  }

  return (
    <div ref={containerRef} className={`chat-category-inline ${isOpen ? 'open' : ''}`}>
      <button
        type="button"
        className={`chat-category-pill ${disabled ? 'disabled' : ''}`}
        onClick={toggleOpen}
        aria-haspopup="true"
        aria-expanded={isOpen}
        aria-label="Toggle category filters"
      >
        <span className="chat-category-pill__label"></span>
        <span className="chat-category-pill__summary">{summaryText}</span>
        <span className={`chat-category-pill__chevron ${isOpen ? 'open' : ''}`} aria-hidden="true">
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.4" strokeLinecap="round" strokeLinejoin="round">
            <polyline points="6 9 12 15 18 9" />
          </svg>
        </span>
      </button>

      {isOpen && (
        <div
          className={`chat-category-popover ${openAbove ? 'pop-above' : 'pop-below'}`}
          role="listbox"
          aria-label="Category filters"
        >
          <div className="chat-category-popover__actions">
            <button
              type="button"
              className="chat-category-filter__action"
              onClick={handleSelectAll}
              disabled={disabled || selectedCategories.size === allCategories.length}
            >
              Select all
            </button>
            <button
              type="button"
              className="chat-category-filter__action"
              onClick={handleClearAll}
              disabled={disabled || selectedCategories.size === 0}
            >
              Clear
            </button>
          </div>

          <div className="chat-category-popover__chips">
            {allCategories.map((category) => {
              const selected = selectedCategories.has(category);
              const color = getCategoryColor(category);
              const textColor = getContrastTextColor(color);
              return (
                <button
                  key={category}
                  type="button"
                  className={`chat-category-chip ${selected ? 'selected' : 'unselected'}`}
                  style={selected ? {
                    backgroundColor: color,
                    color: textColor
                  } : {
                    borderColor: color,
                    color: color
                  }}
                  onClick={() => handleCategoryToggle(category)}
                  disabled={disabled}
                  title={selected ? `${category} selected` : `${category} hidden`}
                >
                  <span className="chat-category-chip__label">{category}</span>
                </button>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatCategoryFilter;
