/* EntityList.css */

.entity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Empty State */
.entity-list-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: #6b7280;
}

.empty-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  opacity: 0.6;
}

.empty-message {
  font-size: 0.875rem;
  font-weight: 500;
}

/* Controls */
.entity-list-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.search-control {
  flex: 1;
  min-width: 200px;
}

.search-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-control,
.sort-control {
  min-width: 150px;
}

.category-filter,
.sort-select {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background-color: #ffffff;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.category-filter:focus,
.sort-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Entity Grid */
.entity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 0.75rem;
}

/* Entity Groups */
.entity-groups {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.entity-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.group-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e5e7eb;
}

/* Entity Item - Compact Design */
.entity-item {
  display: flex;
  flex-direction: column;
  gap: 0.375rem; /* Reduced from 0.5rem */
  padding: 0.75rem; /* Reduced from 1rem */
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.entity-item:hover {
  border-color: #dfe3ea;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.entity-item:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.entity-item:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

/* Entity Header */
.entity-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 0.5rem;
}

.entity-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  flex: 1;
}

.entity-relevance {
  font-size: 0.75rem;
  font-weight: 700;
  padding: 0.125rem 0.375rem;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  border: 1px solid currentColor;
  flex-shrink: 0;
}

/* Entity Description */
.entity-description {
  font-size: 0.8125rem;
  color: #6b7280;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* Entity Footer */
.entity-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.25rem;
}

.entity-category {
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  color: #374151;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.entity-properties-indicator {
  width: 16px;
  height: 16px;
  color: #6b7280;
  flex-shrink: 0;
}

.entity-properties-indicator svg {
  width: 100%;
  height: 100%;
}

/* Relevance Bar */
.entity-relevance-bar {
  height: 2px;
  background-color: #f2f4f7;
  border-radius: 2px;
  overflow: hidden;
  margin-top: 0.5rem;
}

.relevance-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 2px;
}

/* Entity List Info */
.entity-list-info {
  padding: 0.5rem 0.75rem;
  background-color: #f9fafb;
  border-radius: 6px;
  font-size: 0.8125rem;
  color: #6b7280;
  text-align: center;
  border: 1px solid #e5e7eb;
}

/* Responsive Design */
@media (max-width: 768px) {
  .entity-grid {
    grid-template-columns: 1fr;
  }

  .entity-list-controls {
    flex-direction: column;
    gap: 0.5rem;
  }

  .search-control,
  .filter-control,
  .sort-control {
    min-width: auto;
  }

  .entity-item {
    padding: 0.75rem;
  }

  .entity-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .entity-relevance {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .entity-list-controls {
    padding: 0.5rem;
  }

  .entity-item {
    padding: 0.5rem;
  }

  .entity-name {
    font-size: 0.8125rem;
  }

  .entity-description {
    font-size: 0.75rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .entity-item {
    border-width: 2px;
  }

  .entity-item:hover {
    border-color: #000000;
  }

  .entity-relevance {
    background-color: #ffffff;
    border-width: 2px;
  }

  .entity-category {
    border-width: 2px;
    border-color: #000000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .entity-item {
    transition: none;
  }

  .entity-item:hover {
    transform: none;
  }

  .relevance-fill {
    transition: none;
  }
}

/* Print styles */
@media print {
  .entity-list-controls {
    display: none;
  }

  .entity-item {
    border: 1px solid #000000;
    box-shadow: none;
    break-inside: avoid;
  }

  .entity-item:hover {
    transform: none;
    box-shadow: none;
  }

  .entity-relevance-bar {
    display: none;
  }
}
