import React from 'react';
import FormatCRenderer from './FormatCRenderer';

/**
 * StructuredResponse component for displaying Format C structured chat responses
 * 
 * Simplified to use Format C exclusively as the system now only produces Format C responses.
 * All responses are rendered using FormatCRenderer for consistent, native Format C rendering.
 * 
 * Features:
 * - Primary answer display
 * - Collapsible sections with markdown content
 * - Source documents with PDF icons and tooltips
 * - Entity badges with category colors
 * - Ranked entity list with click-to-expand
 * - Follow-up question cards
 * - Metadata display for debugging
 */
const StructuredResponse = ({
  response,
  onNodeSelect,
  onRelationshipSelect,
  onSendMessage,
  showDebugInfo = true
}) => {
  // Format C only - all responses use FormatCRenderer
  return (
    <FormatCRenderer
      response={response}
      onNodeSelect={onNodeSelect}
      onRelationshipSelect={onRelationshipSelect}
      onSendMessage={onSendMessage}
      showDebugInfo={showDebugInfo}
    />
  );
};

export default StructuredResponse;
