import React from 'react';
import { render, screen, fireEvent, within } from '@testing-library/react';
import '@testing-library/jest-dom';
import FormatCRenderer from '../FormatCRenderer';

// Mock MUI components for testing
jest.mock('@mui/material', () => ({
  Accordion: ({ children, expanded, onChange }) => (
    <div data-testid="accordion" onClick={onChange}>
      {children}
    </div>
  ),
  AccordionSummary: ({ children, expandIcon }) => (
    <div data-testid="accordion-summary">
      {children}
      {expandIcon}
    </div>
  ),
  AccordionDetails: ({ children }) => (
    <div data-testid="accordion-details">{children}</div>
  ),
  Box: ({ children, ...props }) => (
    <div data-testid="box" {...props}>{children}</div>
  ),
  Typography: ({ children, variant, ...props }) => (
    <div data-testid={`typography-${variant || 'default'}`} {...props}>
      {children}
    </div>
  ),
  Card: ({ children, ...props }) => (
    <div data-testid="card" {...props}>{children}</div>
  ),
  CardContent: ({ children }) => (
    <div data-testid="card-content">{children}</div>
  ),
  Button: ({ children, onClick, ...props }) => (
    <button onClick={onClick} data-testid="button" {...props}>
      {children}
    </button>
  ),
  Chip: ({ label, ...props }) => (
    <span data-testid="chip" {...props}>{label}</span>
  ),
  Divider: () => <hr data-testid="divider" />
}));

jest.mock('@mui/icons-material', () => ({
  ExpandMore: () => <span data-testid="expand-more-icon">▼</span>,
  QuestionAnswer: () => <span data-testid="question-icon">❓</span>,
  Source: () => <span data-testid="source-icon">📄</span>,
  BugReport: () => <span data-testid="debug-icon">🐛</span>
}));

// Mock the child components
jest.mock('../RichTextRenderer', () => ({
  default: ({ content, className }) => (
    <div data-testid="rich-text-renderer" className={className}>
      {content}
    </div>
  )
}));

jest.mock('../FollowUpCards', () => ({
  default: ({ questions, onQuestionClick, disabled }) => (
    <div data-testid="follow-up-cards" data-disabled={disabled}>
      {questions.map((question, index) => (
        <button
          key={index}
          onClick={() => onQuestionClick(question)}
          data-testid={`follow-up-question-${index}`}
        >
          {question}
        </button>
      ))}
    </div>
  )
}));

jest.mock('../EntityList', () => ({
  default: ({ entities, onEntitySelect }) => (
    <div data-testid="entity-list">
      {entities.map((entity, index) => (
        <button
          key={index}
          onClick={() => onEntitySelect(entity)}
          data-testid={`entity-${index}`}
        >
          {entity.name}
        </button>
      ))}
    </div>
  )
}));

describe('FormatCRenderer', () => {
  const mockOnNodeSelect = jest.fn();
  const mockOnSendMessage = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const validFormatCResponse = {
    version: '3.0',
    format: 'C',
    core: {
      summary: "360T's EMS platform provides comprehensive execution management for multi-asset trading. The system integrates smart order routing algorithms with real-time risk controls and market data feeds across global venues.",
      results: [
        {
          content: "Smart Order Routing (SOR) algorithms are configurable per asset class to optimize execution quality.",
          source: {
            title: "SOR Configuration Guide",
            category: "documentation",
            type: "pdf"
          }
        }
      ],
      follow_up_questions: [
        "How do you configure SOR algorithms for different asset classes?",
        "What are the latency requirements for algorithmic trading?"
      ],
      confidence_score: 0.85,
      processing_time_ms: 1240
    },
    extensions: {
      atlas_rag: {
        entities: [
          {
            id: "ems_platform",
            name: "EMS Platform",
            category: "system",
            description: "Electronic Market System for trading",
            relevance_score: 0.9
          }
        ],
        pagerank_scores: [
          { entity: "EMS_PLATFORM", score: 0.92 }
        ]
      },
      graphiti: {
        nodes: [
          {
            id: "sor_algorithm",
            properties: {
              name: "Smart Order Routing",
              category: "algorithm",
              description: "Algorithm for optimal order execution"
            }
          }
        ],
        edges: []
      }
    }
  };

  const problematicFormatCResponse = {
    version: '3.0',
    format: 'C',
    core: {
      // This is the problematic case - raw markdown in summary
      summary: "## Answer\n\n360T's EMS platform provides comprehensive execution management.\n\n## Follow-up Questions\n\n- How do you configure SOR algorithms?\n- What are the latency requirements?",
      follow_up_questions: [
        "How do you configure SOR algorithms?", // DUPLICATE!
        "What are the latency requirements?" // DUPLICATE!
      ],
      confidence_score: 0.85
    },
    extensions: {
      atlas_rag: {
        raw_markdown: "## Answer\n\n360T's EMS platform provides comprehensive execution management.\n\n## Follow-up Questions\n\n- How do you configure SOR algorithms?\n- What are the latency requirements?"
      }
    }
  };

  describe('Valid Format C Rendering', () => {
    it('should render Format C response without content duplication', () => {
      const { container } = render(
        <FormatCRenderer
          response={validFormatCResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
          showDebugInfo={false}
        />
      );

      // Should have the format-c-renderer test id
      expect(screen.getByTestId('format-c-renderer')).toBeInTheDocument();

      // Should display the summary content exactly once
      const summaryContent = "360T's EMS platform provides comprehensive execution management for multi-asset trading";
      const summaryElements = container.querySelectorAll('*');
      let summaryCount = 0;
      
      summaryElements.forEach(element => {
        if (element.textContent && element.textContent.includes(summaryContent)) {
          summaryCount++;
        }
      });

      // CRITICAL: Content should appear exactly once
      expect(summaryCount).toBe(1);
    });

    it('should NOT show markdown headers as visible text', () => {
      const { container } = render(
        <FormatCRenderer
          response={validFormatCResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
          showDebugInfo={false}
        />
      );

      const visibleText = container.textContent;
      
      // Should not contain markdown syntax
      expect(visibleText).not.toContain("## Answer");
      expect(visibleText).not.toContain("## Follow-up Questions");
      expect(visibleText).not.toContain("##");

      // But should contain the actual content
      expect(visibleText).toContain("360T's EMS platform");
      expect(visibleText).toContain("smart order routing");
    });

    it('should render follow-up questions in dedicated section', () => {
      render(
        <FormatCRenderer
          response={validFormatCResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
          showDebugInfo={false}
        />
      );

      // Should have dedicated follow-up questions section
      const questionsSection = screen.getByTestId('follow-up-questions');
      expect(questionsSection).toBeInTheDocument();

      // Should have follow-up cards component
      const followUpCards = screen.getByTestId('follow-up-cards');
      expect(followUpCards).toBeInTheDocument();

      // Should render all questions as buttons
      expect(screen.getByTestId('follow-up-question-0')).toBeInTheDocument();
      expect(screen.getByTestId('follow-up-question-1')).toBeInTheDocument();
    });

    it('should handle follow-up question clicks', () => {
      render(
        <FormatCRenderer
          response={validFormatCResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
          showDebugInfo={false}
        />
      );

      const firstQuestionButton = screen.getByTestId('follow-up-question-0');
      fireEvent.click(firstQuestionButton);

      expect(mockOnSendMessage).toHaveBeenCalledWith(
        "How do you configure SOR algorithms for different asset classes?"
      );
    });

    it('should render entities from extensions', () => {
      render(
        <FormatCRenderer
          response={validFormatCResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
          showDebugInfo={false}
        />
      );

      // Should have entity list
      const entityList = screen.getByTestId('entity-list');
      expect(entityList).toBeInTheDocument();

      // Should have specific entities
      expect(screen.getByTestId('entity-0')).toBeInTheDocument();
      expect(screen.getByText('EMS Platform')).toBeInTheDocument();
    });

    it('should handle entity selection', () => {
      render(
        <FormatCRenderer
          response={validFormatCResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
          showDebugInfo={false}
        />
      );

      const entityButton = screen.getByTestId('entity-0');
      fireEvent.click(entityButton);

      expect(mockOnNodeSelect).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'ems_platform',
          name: 'EMS Platform',
          category: 'system',
          description: 'Electronic Market System for trading'
        })
      );
    });

    it('should display confidence score', () => {
      render(
        <FormatCRenderer
          response={validFormatCResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
          showDebugInfo={false}
        />
      );

      // Should show confidence as percentage
      expect(screen.getByText('85% confidence')).toBeInTheDocument();
    });
  });

  describe('Problematic Format C Handling', () => {
    it('should clean markdown from problematic summary', () => {
      const { container } = render(
        <FormatCRenderer
          response={problematicFormatCResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
          showDebugInfo={false}
        />
      );

      const visibleText = container.textContent;

      // Should not show markdown headers
      expect(visibleText).not.toContain("## Answer");
      expect(visibleText).not.toContain("## Follow-up Questions");

      // Should show clean content
      expect(visibleText).toContain("360T's EMS platform provides comprehensive execution management");
    });

    it('should prevent question duplication in problematic responses', () => {
      const { container } = render(
        <FormatCRenderer
          response={problematicFormatCResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
          showDebugInfo={false}
        />
      );

      const visibleText = container.textContent;
      const questionText = "How do you configure SOR algorithms";
      
      // Count occurrences of the question text
      const questionOccurrences = (visibleText.match(new RegExp(questionText, 'g')) || []).length;
      
      // CRITICAL: Should appear only once (in the follow-up questions section)
      expect(questionOccurrences).toBe(1);
    });
  });

  describe('Invalid Format C Responses', () => {
    it('should show error for invalid format', () => {
      const invalidResponse = {
        version: '2.0', // Wrong version
        format: 'C',
        core: { summary: 'Test' }
      };

      render(
        <FormatCRenderer
          response={invalidResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
          showDebugInfo={false}
        />
      );

      expect(screen.getByText('Invalid Format C Response')).toBeInTheDocument();
    });

    it('should show error for missing core', () => {
      const invalidResponse = {
        version: '3.0',
        format: 'C'
        // Missing core
      };

      render(
        <FormatCRenderer
          response={invalidResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
          showDebugInfo={false}
        />
      );

      expect(screen.getByText('Invalid Format C Response')).toBeInTheDocument();
    });
  });

  describe('Debug Information', () => {
    it('should show debug information when enabled', () => {
      render(
        <FormatCRenderer
          response={validFormatCResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
          showDebugInfo={true}
        />
      );

      expect(screen.getByText('Response Metadata')).toBeInTheDocument();
      expect(screen.getByText('Format: C')).toBeInTheDocument();
      expect(screen.getByText('Version: 3.0')).toBeInTheDocument();
    });

    it('should hide debug information when disabled', () => {
      render(
        <FormatCRenderer
          response={validFormatCResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
          showDebugInfo={false}
        />
      );

      expect(screen.queryByText('Response Metadata')).not.toBeInTheDocument();
    });
  });

  describe('Streaming State', () => {
    it('should disable follow-up questions when streaming', () => {
      render(
        <FormatCRenderer
          response={validFormatCResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
          showDebugInfo={false}
          isStreaming={true}
        />
      );

      const followUpCards = screen.getByTestId('follow-up-cards');
      expect(followUpCards).toHaveAttribute('data-disabled', 'true');
    });

    it('should enable follow-up questions when not streaming', () => {
      render(
        <FormatCRenderer
          response={validFormatCResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
          showDebugInfo={false}
          isStreaming={false}
        />
      );

      const followUpCards = screen.getByTestId('follow-up-cards');
      expect(followUpCards).toHaveAttribute('data-disabled', 'false');
    });
  });

  describe('CRITICAL Anti-Duplication Tests', () => {
    it('test_no_content_duplication: should pass critical duplication prevention', () => {
      const { container } = render(
        <FormatCRenderer
          response={problematicFormatCResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
          showDebugInfo={false}
        />
      );

      const fullText = container.textContent;

      // Test for the specific content from the test file
      const keyContent = "360T's EMS platform provides comprehensive execution management";
      const contentOccurrences = (fullText.match(new RegExp(keyContent, 'g')) || []).length;

      // CRITICAL: Content should appear exactly once
      expect(contentOccurrences).toBe(1);
    });

    it('test_no_markdown_headers_as_text: should pass markdown header filtering', () => {
      const { container } = render(
        <FormatCRenderer
          response={problematicFormatCResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
          showDebugInfo={false}
        />
      );

      const visibleText = container.textContent;

      // CRITICAL: Users should never see markdown syntax
      expect(visibleText).not.toContain("## Answer");
      expect(visibleText).not.toContain("## Follow-up Questions");
      expect(visibleText).not.toContain("##");

      // But should see the actual content
      expect(visibleText).toContain("360T's EMS platform");
    });

    it('test_native_format_c_rendering: should render natively without normalization', () => {
      const { container } = render(
        <FormatCRenderer
          response={validFormatCResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
          showDebugInfo={false}
        />
      );

      // Should have Format C specific test id
      expect(screen.getByTestId('format-c-renderer')).toBeInTheDocument();
      
      // Should have proper sections
      expect(screen.getByTestId('message-summary')).toBeInTheDocument();
      expect(screen.getByTestId('follow-up-questions')).toBeInTheDocument();

      // Should not have any v2.0 normalization artifacts
      expect(container.textContent).not.toContain('v2.0');
      expect(container.textContent).not.toContain('normalized');
    });
  });
});