import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import RelatedSectionPill from '../RelatedSectionPill';

/**
 * Test suite for RelatedSectionPill component
 *
 * Tests the collapsible pill that replaces the accordion-style RELATED section
 * in FormatCRenderer. Verifies positioning, interaction, and content display.
 */
describe('RelatedSectionPill', () => {
  const mockOnEntitySelect = jest.fn();
  const mockOnShowAllToggle = jest.fn();

  const mockEntities = [
    {
      id: 'entity-1',
      name: 'Test Entity 1',
      properties: { business_category: 'TRADING' },
      source: 'graphiti'
    },
    {
      id: 'entity-2',
      name: 'Test Entity 2',
      properties: { business_category: 'RISK' },
      source: 'graphiti'
    }
  ];

  const mockFacts = [
    {
      source: { title: 'Test Fact 1' },
      content: 'This is test fact content'
    }
  ];

  const mockEdges = [
    {
      id: 'edge-1',
      type: 'RELATES_TO',
      source: { name: 'Entity A' },
      target: { name: 'Entity B' }
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should not render when no related items are present', () => {
      render(
        <RelatedSectionPill
          entities={[]}
          facts={[]}
          edges={[]}
          onEntitySelect={mockOnEntitySelect}
        />
      );

      expect(screen.queryByRole('button')).not.toBeInTheDocument();
    });

    it('should render pill with correct count for single item', () => {
      render(
        <RelatedSectionPill
          entities={mockEntities.slice(0, 1)}
          facts={[]}
          edges={[]}
          onEntitySelect={mockOnEntitySelect}
        />
      );

      const pill = screen.getByRole('button');
      expect(pill).toBeInTheDocument();
      expect(pill).toHaveTextContent('1 related');
    });

    it('should render pill with correct count for multiple items', () => {
      render(
        <RelatedSectionPill
          entities={mockEntities}
          facts={mockFacts}
          edges={mockEdges}
          onEntitySelect={mockOnEntitySelect}
        />
      );

      const pill = screen.getByRole('button');
      expect(pill).toBeInTheDocument();
      expect(pill).toHaveTextContent('4 related'); // 2 entities + 1 fact + 1 edge
    });

    it('should have proper accessibility attributes', () => {
      render(
        <RelatedSectionPill
          entities={mockEntities}
          facts={[]}
          edges={[]}
          onEntitySelect={mockOnEntitySelect}
        />
      );

      const pill = screen.getByRole('button');
      expect(pill).toHaveAttribute('aria-haspopup', 'true');
      expect(pill).toHaveAttribute('aria-expanded', 'false');
      expect(pill).toHaveAttribute('aria-label', 'Toggle related information. 2 related');
    });
  });

  describe('Interaction', () => {
    it('should toggle popover when pill is clicked', async () => {
      render(
        <RelatedSectionPill
          entities={mockEntities}
          facts={[]}
          edges={[]}
          onEntitySelect={mockOnEntitySelect}
        />
      );

      const pill = screen.getByRole('button');

      // Initially closed
      expect(pill).toHaveAttribute('aria-expanded', 'false');
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();

      // Click to open
      fireEvent.click(pill);

      await waitFor(() => {
        expect(pill).toHaveAttribute('aria-expanded', 'true');
        expect(screen.getByRole('dialog')).toBeInTheDocument();
      });

      // Click to close
      fireEvent.click(pill);

      await waitFor(() => {
        expect(pill).toHaveAttribute('aria-expanded', 'false');
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      });
    });

    it('should handle keyboard navigation', async () => {
      render(
        <RelatedSectionPill
          entities={mockEntities}
          facts={[]}
          edges={[]}
          onEntitySelect={mockOnEntitySelect}
        />
      );

      const pill = screen.getByRole('button');

      // Test Enter key
      fireEvent.keyDown(pill, { key: 'Enter' });

      await waitFor(() => {
        expect(pill).toHaveAttribute('aria-expanded', 'true');
        expect(screen.getByRole('dialog')).toBeInTheDocument();
      });

      // Test Space key
      fireEvent.keyDown(pill, { key: ' ' });

      await waitFor(() => {
        expect(pill).toHaveAttribute('aria-expanded', 'false');
      });
    });

    it('should close popover on Escape key', async () => {
      render(
        <RelatedSectionPill
          entities={mockEntities}
          facts={[]}
          edges={[]}
          onEntitySelect={mockOnEntitySelect}
        />
      );

      const pill = screen.getByRole('button');

      // Open popover
      fireEvent.click(pill);

      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
      });

      // Press Escape
      fireEvent.keyDown(document, { key: 'Escape' });

      await waitFor(() => {
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      });
    });

    it('should be disabled when disabled prop is true', () => {
      render(
        <RelatedSectionPill
          entities={mockEntities}
          facts={[]}
          edges={[]}
          onEntitySelect={mockOnEntitySelect}
          disabled={true}
        />
      );

      const pill = screen.getByRole('button');
      expect(pill).toBeDisabled();
      expect(pill).toHaveClass('disabled');
    });
  });

  describe('Popover Content', () => {
    it('should display show all toggle when there are many items', async () => {
      const manyEntities = Array.from({ length: 35 }, (_, i) => ({
        id: `entity-${i}`,
        name: `Entity ${i}`,
        properties: { business_category: 'TRADING' },
        source: 'graphiti'
      }));

      render(
        <RelatedSectionPill
          entities={manyEntities}
          facts={[]}
          edges={[]}
          onEntitySelect={mockOnEntitySelect}
          onShowAllToggle={mockOnShowAllToggle}
          showAllEntities={false}
        />
      );

      const pill = screen.getByRole('button');
      fireEvent.click(pill);

      await waitFor(() => {
        const showAllButton = screen.getByText('Show All');
        expect(showAllButton).toBeInTheDocument();
      });
    });

    it('should call onShowAllToggle when toggle button is clicked', async () => {
      const manyEntities = Array.from({ length: 35 }, (_, i) => ({
        id: `entity-${i}`,
        name: `Entity ${i}`,
        properties: { business_category: 'TRADING' },
        source: 'graphiti'
      }));

      render(
        <RelatedSectionPill
          entities={manyEntities}
          facts={[]}
          edges={[]}
          onEntitySelect={mockOnEntitySelect}
          onShowAllToggle={mockOnShowAllToggle}
          showAllEntities={false}
        />
      );

      const pill = screen.getByRole('button');
      fireEvent.click(pill);

      await waitFor(() => {
        const showAllButton = screen.getByText('Show All');
        fireEvent.click(showAllButton);
        expect(mockOnShowAllToggle).toHaveBeenCalledTimes(1);
      });
    });

    it('should display "Related" header in popover', async () => {
      render(
        <RelatedSectionPill
          entities={mockEntities}
          facts={[]}
          edges={[]}
          onEntitySelect={mockOnEntitySelect}
        />
      );

      const pill = screen.getByRole('button');
      fireEvent.click(pill);

      await waitFor(() => {
        expect(screen.getByText('Related')).toBeInTheDocument();
      });
    });
  });

  describe('CSS Classes and Styling', () => {
    it('should have correct CSS classes', () => {
      const { container } = render(
        <RelatedSectionPill
          entities={mockEntities}
          facts={[]}
          edges={[]}
          onEntitySelect={mockOnEntitySelect}
        />
      );

      const pillContainer = container.querySelector('.related-pill-container');
      const pill = container.querySelector('.related-pill');

      expect(pillContainer).toBeInTheDocument();
      expect(pill).toBeInTheDocument();
      expect(pill).toHaveClass('related-pill');
    });

    it('should add open class when popover is open', async () => {
      const { container } = render(
        <RelatedSectionPill
          entities={mockEntities}
          facts={[]}
          edges={[]}
          onEntitySelect={mockOnEntitySelect}
        />
      );

      const pill = screen.getByRole('button');
      const pillContainer = container.querySelector('.related-pill-container');

      // Initially not open
      expect(pillContainer).not.toHaveClass('open');

      // Click to open
      fireEvent.click(pill);

      await waitFor(() => {
        expect(pillContainer).toHaveClass('open');
      });
    });
  });
});