/**
 * Tests for SourceTooltip component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import SourceTooltip from '../SourceTooltip';

describe('SourceTooltip', () => {
  const mockCompleteSource = {
    id: 'source-1',
    title: 'Test Document',
    url: 'https://example.com/doc.pdf',
    document_type: 'PDF',
    snippet: 'This is a test snippet from the document',
    category: 'EMS',
    score: 0.95
  };

  const mockMinimalSource = {
    id: 'source-2',
    content: 'This source has content instead of snippet',
    type: 'Document',
    score: 0.75
  };

  const mockEmptySource = {
    id: 'source-3'
  };

  beforeEach(() => {
    // Mock window.open
    global.window.open = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering with complete data', () => {
    test('should render with all fields present', () => {
      render(<SourceTooltip source={mockCompleteSource} index={0} />);
      
      expect(screen.getByText('Test Document')).toBeInTheDocument();
      expect(screen.getAllByText('PDF')).toHaveLength(2); // One in icon, one in type
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    test('should show tooltip on hover', async () => {
      render(<SourceTooltip source={mockCompleteSource} index={0} />);
      
      const container = screen.getByRole('button');
      fireEvent.mouseEnter(container);
      
      await waitFor(() => {
        expect(screen.getByText('This is a test snippet from the document')).toBeInTheDocument();
        expect(screen.getByText('https://example.com/doc.pdf')).toBeInTheDocument();
        expect(screen.getByText('Category:')).toBeInTheDocument();
        expect(screen.getAllByText('EMS')).toHaveLength(2); // One in badge, one in tooltip
        expect(screen.getByText((content, element) => {
          return element.className === 'tooltip-score' && content.includes('Relevance:') && content.includes('95%');
        })).toBeInTheDocument();
      });
    });

    test('should hide tooltip on mouse leave', async () => {
      render(<SourceTooltip source={mockCompleteSource} index={0} />);
      
      const container = screen.getByRole('button');
      fireEvent.mouseEnter(container);
      
      await waitFor(() => {
        expect(screen.getByText('This is a test snippet from the document')).toBeInTheDocument();
      });
      
      fireEvent.mouseLeave(container);
      
      await waitFor(() => {
        expect(screen.queryByText('This is a test snippet from the document')).not.toBeInTheDocument();
      });
    });

    test('should open URL when clicked', () => {
      render(<SourceTooltip source={mockCompleteSource} index={0} />);
      
      const container = screen.getByRole('button');
      fireEvent.click(container);
      
      expect(window.open).toHaveBeenCalledWith('https://example.com/doc.pdf', '_blank', 'noopener,noreferrer');
    });

    test('should open URL when Enter key is pressed', () => {
      render(<SourceTooltip source={mockCompleteSource} index={0} />);
      
      const container = screen.getByRole('button');
      fireEvent.keyDown(container, { key: 'Enter' });
      
      expect(window.open).toHaveBeenCalledWith('https://example.com/doc.pdf', '_blank', 'noopener,noreferrer');
    });

    test('should open URL when Space key is pressed', () => {
      render(<SourceTooltip source={mockCompleteSource} index={0} />);
      
      const container = screen.getByRole('button');
      fireEvent.keyDown(container, { key: ' ' });
      
      expect(window.open).toHaveBeenCalledWith('https://example.com/doc.pdf', '_blank', 'noopener,noreferrer');
    });
  });

  describe('Fallback data handling', () => {
    test('should handle source with content instead of snippet', async () => {
      render(<SourceTooltip source={mockMinimalSource} index={1} />);
      
      expect(screen.getByText('Source 2')).toBeInTheDocument(); // Fallback title
      expect(screen.getByText('Document')).toBeInTheDocument(); // Fallback type
      
      const container = screen.getByText('Source 2').closest('div');
      fireEvent.mouseEnter(container);
      
      await waitFor(() => {
        expect(screen.getByText('This source has content instead of snippet')).toBeInTheDocument();
      });
    });

    test('should handle source with missing fields gracefully', async () => {
      render(<SourceTooltip source={mockEmptySource} index={2} />);
      
      expect(screen.getByText('Source 3')).toBeInTheDocument(); // Fallback title
      expect(screen.getByText('Document')).toBeInTheDocument(); // Fallback type
      
      const container = screen.getByText('Source 3').closest('div');
      fireEvent.mouseEnter(container);
      
      await waitFor(() => {
        expect(screen.getByText('No preview available')).toBeInTheDocument();
      });
    });

    test('should not be clickable without URL', () => {
      render(<SourceTooltip source={mockEmptySource} index={2} />);
      
      const container = screen.getByText('Source 3').closest('.source-tooltip-container');
      expect(container).toHaveAttribute('role', 'presentation');
      expect(container).toHaveAttribute('tabIndex', '-1');
    });

    test('should handle source with alternative field names', () => {
      const sourceWithAlternativeFields = {
        id: 'alt-source',
        name: 'Alternative Name Field',
        link: 'https://example.com/alt',
        text: 'Text field instead of content or snippet'
      };
      
      render(<SourceTooltip source={sourceWithAlternativeFields} index={3} />);
      
      expect(screen.getByText('Alternative Name Field')).toBeInTheDocument();
      expect(screen.getByRole('button')).toBeInTheDocument(); // Should be clickable due to link field
    });
  });

  describe('Document type icons', () => {
    test('should show PDF icon for PDF documents', () => {
      render(<SourceTooltip source={{ ...mockCompleteSource, document_type: 'PDF' }} index={0} />);
      
      const icon = document.querySelector('.document-icon.pdf');
      expect(icon).toBeInTheDocument();
    });

    test('should show DOC icon for Word documents', () => {
      render(<SourceTooltip source={{ ...mockCompleteSource, document_type: 'DOC' }} index={0} />);
      
      const icon = document.querySelector('.document-icon.doc');
      expect(icon).toBeInTheDocument();
    });

    test('should show WEB icon for web documents', () => {
      render(<SourceTooltip source={{ ...mockCompleteSource, document_type: 'WEB' }} index={0} />);
      
      const icon = document.querySelector('.document-icon.web');
      expect(icon).toBeInTheDocument();
    });

    test('should show default icon for unknown document types', () => {
      render(<SourceTooltip source={{ ...mockCompleteSource, document_type: 'UNKNOWN' }} index={0} />);
      
      const icon = document.querySelector('.document-icon.other');
      expect(icon).toBeInTheDocument();
    });
  });

  describe('Category badges', () => {
    test('should show category badge when category is present', () => {
      render(<SourceTooltip source={mockCompleteSource} index={0} />);
      
      expect(screen.getByText('EMS')).toBeInTheDocument();
    });

    test('should not show category badge when category is missing', () => {
      render(<SourceTooltip source={mockMinimalSource} index={1} />);
      
      expect(screen.queryByText('EMS')).not.toBeInTheDocument();
    });
  });

  describe('Null/invalid sources', () => {
    test('should return null for null source', () => {
      const { container } = render(<SourceTooltip source={null} index={0} />);
      expect(container.firstChild).toBeNull();
    });

    test('should return null for undefined source', () => {
      const { container } = render(<SourceTooltip source={undefined} index={0} />);
      expect(container.firstChild).toBeNull();
    });
  });

  describe('Accessibility', () => {
    test('should have proper ARIA labels for clickable sources', () => {
      render(<SourceTooltip source={mockCompleteSource} index={0} />);
      
      const container = screen.getByRole('button');
      expect(container).toHaveAttribute('aria-label', 'Open Test Document');
    });

    test('should have proper ARIA labels for non-clickable sources', () => {
      render(<SourceTooltip source={mockEmptySource} index={2} />);
      
      const container = screen.getByText('Source 3').closest('.source-tooltip-container');
      expect(container).toHaveAttribute('aria-label', 'Source 3');
    });

    test('should be keyboard accessible', () => {
      render(<SourceTooltip source={mockCompleteSource} index={0} />);
      
      const container = screen.getByRole('button');
      expect(container).toHaveAttribute('tabIndex', '0');
    });
  });
});