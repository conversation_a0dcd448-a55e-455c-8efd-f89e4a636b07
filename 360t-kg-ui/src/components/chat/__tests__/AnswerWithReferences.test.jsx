/**
 * Tests for AnswerWithReferences component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import AnswerWithReferences from '../AnswerWithReferences';

// Mock MarkdownRenderer
jest.mock('../../MarkdownRenderer', () => {
  return function MarkdownRenderer({ content }) {
    // Simple HTML rendering for testing (simulate markdown parsing)
    return <div dangerouslySetInnerHTML={{ __html: content }} />;
  };
});

describe('AnswerWithReferences', () => {
  const mockSources = [
    {
      id: 'source-1',
      content: 'This is the first source document with detailed information about the topic.',
      title: 'First Source',
      url: 'https://example.com/source1',
      type: 'document',
      score: 0.95
    },
    {
      id: 'source-2', 
      content: 'This is the second source document with additional context and examples.',
      title: 'Second Source',
      score: 0.87
    },
    {
      id: 'source-3',
      text: 'This source uses "text" property instead of "content".',
      title: 'Third Source'
    }
  ];

  const mockOnSendMessage = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Content Rendering', () => {
    test('should render content without references', () => {
      const content = 'This is a simple answer without any references.';
      
      render(<AnswerWithReferences content={content} onSendMessage={mockOnSendMessage} />);
      
      expect(screen.getByText(/This is a simple answer/)).toBeInTheDocument();
      expect(screen.queryByText(/References/)).not.toBeInTheDocument();
    });

    test('should render null for empty content', () => {
      const { container } = render(<AnswerWithReferences content="" onSendMessage={mockOnSendMessage} />);
      expect(container.firstChild).toBeNull();
    });

    test('should render null for null content', () => {
      const { container } = render(<AnswerWithReferences content={null} onSendMessage={mockOnSendMessage} />);
      expect(container.firstChild).toBeNull();
    });
  });

  describe('Reference Detection and Enhancement', () => {
    test('should detect and enhance [number] references in content', () => {
      const content = 'This statement is supported by research [1] and additional studies [2].';
      
      render(
        <AnswerWithReferences 
          content={content} 
          sources={mockSources}
          onSendMessage={mockOnSendMessage} 
        />
      );
      
      // Should show references summary
      expect(screen.getByText(/References \(2\)/)).toBeInTheDocument();
      
      // Should have clickable reference links in the content
      const referenceLinks = document.querySelectorAll('.reference-link');
      expect(referenceLinks).toHaveLength(2);
      expect(referenceLinks[0]).toHaveAttribute('data-ref', '1');
      expect(referenceLinks[1]).toHaveAttribute('data-ref', '2');
    });

    test('should handle duplicate references correctly', () => {
      const content = 'First mention [1], second mention [1], and third [2].';
      
      render(
        <AnswerWithReferences 
          content={content} 
          sources={mockSources}
          onSendMessage={mockOnSendMessage} 
        />
      );
      
      // Should show 2 unique references, not 3
      expect(screen.getByText(/References \(2\)/)).toBeInTheDocument();
      
      // All [1] references should be enhanced
      const referenceLinks = document.querySelectorAll('.reference-link');
      expect(referenceLinks).toHaveLength(3); // 2 x [1] + 1 x [2]
    });

    test('should handle references without corresponding sources', () => {
      const content = 'This has a missing reference [5].';
      
      render(
        <AnswerWithReferences 
          content={content} 
          sources={mockSources}
          onSendMessage={mockOnSendMessage} 
        />
      );
      
      expect(screen.getByText(/References \(1\)/)).toBeInTheDocument();
      expect(screen.getByText(/Source not available/)).toBeInTheDocument();
    });
  });

  describe('References Summary', () => {
    test('should show expandable references summary', async () => {
      const content = 'Statement with references [1] and [2].';
      
      render(
        <AnswerWithReferences 
          content={content} 
          sources={mockSources}
          onSendMessage={mockOnSendMessage} 
        />
      );
      
      // Should have collapsible summary
      const summary = screen.getByText(/References \(2\)/);
      expect(summary).toBeInTheDocument();
      
      // Should show source previews when expanded
      fireEvent.click(summary);
      
      await waitFor(() => {
        expect(screen.getByText(/This is the first source document/)).toBeInTheDocument();
        expect(screen.getByText(/This is the second source document/)).toBeInTheDocument();
      });
    });

    test('should show source titles and content previews', async () => {
      const content = 'Reference to source [1].';
      
      render(
        <AnswerWithReferences 
          content={content} 
          sources={mockSources}
          onSendMessage={mockOnSendMessage} 
        />
      );
      
      // Expand references
      fireEvent.click(screen.getByText(/References \(1\)/));
      
      await waitFor(() => {
        expect(screen.getByText(/First Source/)).toBeInTheDocument();
        expect(screen.getByText(/This is the first source document with detailed information about the topic./)).toBeInTheDocument();
      });
    });
  });

  describe('Reference Click Interaction', () => {
    test('should open modal when reference link is clicked', async () => {
      const content = 'Click this reference [1] to see details.';
      
      render(
        <AnswerWithReferences 
          content={content} 
          sources={mockSources}
          onSendMessage={mockOnSendMessage} 
        />
      );
      
      // Click the reference link
      const referenceLink = document.querySelector('.reference-link[data-ref="1"]');
      expect(referenceLink).toBeInTheDocument();
      
      fireEvent.click(referenceLink);
      
      // Should open modal
      await waitFor(() => {
        expect(document.querySelector('.reference-modal')).toBeInTheDocument();
        expect(document.querySelector('.reference-modal h3')).toHaveTextContent('Source [1]');
      });
    });

    test('should open modal when reference number in summary is clicked', async () => {
      const content = 'Reference to source [1].';
      
      render(
        <AnswerWithReferences 
          content={content} 
          sources={mockSources}
          onSendMessage={mockOnSendMessage} 
        />
      );
      
      // Expand references summary
      fireEvent.click(screen.getByText(/References \(1\)/));
      
      await waitFor(() => {
        const referenceNumber = screen.getByRole('button', { name: /\[1\]/ });
        fireEvent.click(referenceNumber);
      });
      
      // Should open modal
      await waitFor(() => {
        expect(document.querySelector('.reference-modal')).toBeInTheDocument();
      });
    });

    test('should show source details in modal', async () => {
      const content = 'Reference [1] with full details.';
      
      render(
        <AnswerWithReferences 
          content={content} 
          sources={mockSources}
          onSendMessage={mockOnSendMessage} 
        />
      );
      
      // Click reference link
      const referenceLink = document.querySelector('.reference-link[data-ref="1"]');
      fireEvent.click(referenceLink);
      
      await waitFor(() => {
        const modal = document.querySelector('.reference-modal');
        expect(modal).toBeInTheDocument();
        
        // Check modal content
        expect(modal).toHaveTextContent('First Source');
        expect(modal).toHaveTextContent('This is the first source document');
        expect(modal).toHaveTextContent('https://example.com/source1');
        expect(modal).toHaveTextContent('document');
        expect(modal).toHaveTextContent('95.0%'); // score as percentage
      });
    });

    test('should close modal when close button is clicked', async () => {
      const content = 'Reference [1] to close.';
      
      render(
        <AnswerWithReferences 
          content={content} 
          sources={mockSources}
          onSendMessage={mockOnSendMessage} 
        />
      );
      
      // Open modal
      const referenceLink = document.querySelector('.reference-link[data-ref="1"]');
      fireEvent.click(referenceLink);
      
      await waitFor(() => {
        expect(document.querySelector('.reference-modal')).toBeInTheDocument();
      });
      
      // Close modal
      const closeButton = document.querySelector('.reference-modal-close');
      fireEvent.click(closeButton);
      
      await waitFor(() => {
        expect(document.querySelector('.reference-modal')).not.toBeInTheDocument();
      });
    });

    test('should close modal when overlay is clicked', async () => {
      const content = 'Reference [1] with overlay close.';
      
      render(
        <AnswerWithReferences 
          content={content} 
          sources={mockSources}
          onSendMessage={mockOnSendMessage} 
        />
      );
      
      // Open modal
      const referenceLink = document.querySelector('.reference-link[data-ref="1"]');
      fireEvent.click(referenceLink);
      
      await waitFor(() => {
        expect(document.querySelector('.reference-modal')).toBeInTheDocument();
      });
      
      // Click overlay
      const overlay = document.querySelector('.reference-modal-overlay');
      fireEvent.click(overlay);
      
      await waitFor(() => {
        expect(document.querySelector('.reference-modal')).not.toBeInTheDocument();
      });
    });
  });

  describe('Source Content Handling', () => {
    test('should handle sources with "text" property instead of "content"', async () => {
      const content = 'Reference to text source [3].';
      
      render(
        <AnswerWithReferences 
          content={content} 
          sources={mockSources}
          onSendMessage={mockOnSendMessage} 
        />
      );
      
      // Expand references
      fireEvent.click(screen.getByText(/References \(1\)/));
      
      await waitFor(() => {
        expect(screen.getByText(/This source uses "text" property/)).toBeInTheDocument();
      });
    });

    test('should handle sources with missing properties gracefully', async () => {
      const incompleteSource = { id: 'incomplete' };
      const content = 'Reference to incomplete source [1].';
      
      render(
        <AnswerWithReferences 
          content={content} 
          sources={[incompleteSource]}
          onSendMessage={mockOnSendMessage} 
        />
      );
      
      // Click reference link
      const referenceLink = document.querySelector('.reference-link[data-ref="1"]');
      fireEvent.click(referenceLink);
      
      await waitFor(() => {
        const modal = document.querySelector('.reference-modal');
        expect(modal).toHaveTextContent('No content available');
      });
    });
  });

  describe('Integration with MarkdownRenderer', () => {
    test('should pass onSendMessage to MarkdownRenderer', () => {
      const content = 'Content without references.';
      
      render(
        <AnswerWithReferences 
          content={content} 
          sources={[]}
          onSendMessage={mockOnSendMessage} 
        />
      );
      
      // MarkdownRenderer should receive the onSendMessage prop
      // This is tested through the component rendering without errors
      expect(screen.getByText(/Content without references/)).toBeInTheDocument();
    });
  });
});