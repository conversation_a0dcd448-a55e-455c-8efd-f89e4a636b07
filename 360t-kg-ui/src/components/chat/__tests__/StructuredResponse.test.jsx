import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import StructuredResponse from '../StructuredResponse';

// Mock FormatCRenderer since StructuredResponse now only uses it
jest.mock('../FormatCRenderer', () => {
  return function MockFormatCRenderer({ response, onNodeSelect, onSendMessage, showDebugInfo }) {
    return (
      <div data-testid="format-c-renderer">
        <div data-testid="response-version">{response?.version}</div>
        <div data-testid="response-format">{response?.format}</div>
        <div data-testid="show-debug-info">{String(showDebugInfo)}</div>
        {response?.core?.summary && (
          <div data-testid="format-c-summary">{response.core.summary}</div>
        )}
      </div>
    );
  };
});

describe('StructuredResponse', () => {
  const mockOnNodeSelect = jest.fn();
  const mockOnSendMessage = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('always renders FormatCRenderer for any response', () => {
    const formatCResponse = {
      version: '3.0',
      format: 'C',
      core: {
        summary: 'This is a Format C response',
        results: [],
        follow_up_questions: [],
        confidence_score: 0.8,
        processing_time_ms: 600
      }
    };

    render(
      <StructuredResponse
        response={formatCResponse}
        onNodeSelect={mockOnNodeSelect}
        onSendMessage={mockOnSendMessage}
        showDebugInfo={true}
      />
    );

    // Should always render FormatCRenderer
    expect(screen.getByTestId('format-c-renderer')).toBeInTheDocument();
    expect(screen.getByTestId('response-version')).toHaveTextContent('3.0');
    expect(screen.getByTestId('response-format')).toHaveTextContent('C');
    expect(screen.getByTestId('show-debug-info')).toHaveTextContent('true');
    expect(screen.getByTestId('format-c-summary')).toHaveTextContent('This is a Format C response');
  });

  test('passes props correctly to FormatCRenderer', () => {
    const testResponse = {
      version: '3.0',
      format: 'C',
      core: {
        summary: 'Test response',
      }
    };

    render(
      <StructuredResponse
        response={testResponse}
        onNodeSelect={mockOnNodeSelect}
        onSendMessage={mockOnSendMessage}
        showDebugInfo={false}
      />
    );

    expect(screen.getByTestId('format-c-renderer')).toBeInTheDocument();
    expect(screen.getByTestId('show-debug-info')).toHaveTextContent('false');
  });

  test('handles null response gracefully', () => {
    render(
      <StructuredResponse
        response={null}
        onNodeSelect={mockOnNodeSelect}
        onSendMessage={mockOnSendMessage}
        showDebugInfo={true}
      />
    );

    expect(screen.getByTestId('format-c-renderer')).toBeInTheDocument();
  });

  test('handles empty response gracefully', () => {
    render(
      <StructuredResponse
        response={{}}
        onNodeSelect={mockOnNodeSelect}
        onSendMessage={mockOnSendMessage}
        showDebugInfo={false}
      />
    );

    expect(screen.getByTestId('format-c-renderer')).toBeInTheDocument();
  });

  test('uses default showDebugInfo when not provided', () => {
    const testResponse = {
      version: '3.0',
      format: 'C',
      core: { summary: 'Test' }
    };

    render(
      <StructuredResponse
        response={testResponse}
        onNodeSelect={mockOnNodeSelect}
        onSendMessage={mockOnSendMessage}
        // showDebugInfo not provided - should default to true
      />
    );

    expect(screen.getByTestId('format-c-renderer')).toBeInTheDocument();
    expect(screen.getByTestId('show-debug-info')).toHaveTextContent('true');
  });
});