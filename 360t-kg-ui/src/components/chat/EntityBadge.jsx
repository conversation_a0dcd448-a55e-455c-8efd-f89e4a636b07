import React, { useState } from 'react';
import './EntityBadge.css';

/**
 * EntityBadge component for displaying category badges with tooltips and colors
 * Features:
 * - Category-specific colors
 * - Hover tooltips with descriptions
 * - Item count display
 * - Accessible design with keyboard support
 * - WCAG AA compliant colors
 */
const EntityBadge = ({ 
  label, 
  tooltip, 
  color, 
  count, 
  onClick,
  size = 'medium',
  variant = 'filled'
}) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });

  // Handle mouse enter for tooltip
  const handleMouseEnter = (event) => {
    if (tooltip) {
      const rect = event.currentTarget.getBoundingClientRect();
      setTooltipPosition({
        x: rect.left + rect.width / 2,
        y: rect.top - 10
      });
      setShowTooltip(true);
    }
  };

  // Handle mouse leave
  const handleMouseLeave = () => {
    setShowTooltip(false);
  };

  // Handle click
  const handleClick = () => {
    if (onClick) {
      onClick({ label, tooltip, color, count });
    }
  };

  // Handle keyboard interaction
  const handleKeyDown = (event) => {
    if (onClick && (event.key === 'Enter' || event.key === ' ')) {
      event.preventDefault();
      handleClick();
    }
  };

  // Get contrasting text color for accessibility
  const getTextColor = (backgroundColor) => {
    if (!backgroundColor) return '#374151';
    
    // Remove # if present
    const hex = backgroundColor.replace('#', '');
    
    // Convert to RGB
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    
    // Calculate luminance
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
    
    // Return black or white based on luminance
    return luminance > 0.5 ? '#000000' : '#ffffff';
  };

  // Get badge styles based on variant and color
  const getBadgeStyles = () => {
    const textColor = getTextColor(color);
    
    switch (variant) {
      case 'outlined':
        return {
          backgroundColor: 'transparent',
          borderColor: color || '#d1d5db',
          color: color || '#374151'
        };
      case 'subtle':
        return {
          backgroundColor: color ? `${color}20` : '#f3f4f6',
          borderColor: color ? `${color}40` : '#e5e7eb',
          color: color || '#374151'
        };
      default: // filled
        return {
          backgroundColor: color || '#f3f4f6',
          borderColor: color || '#d1d5db',
          color: textColor
        };
    }
  };

  // Get size classes
  const getSizeClass = () => {
    switch (size) {
      case 'small':
        return 'entity-badge-small';
      case 'large':
        return 'entity-badge-large';
      default:
        return 'entity-badge-medium';
    }
  };

  const badgeStyles = getBadgeStyles();
  const sizeClass = getSizeClass();
  const isClickable = typeof onClick === 'function';

  return (
    <>
      <div
        className={`entity-badge ${sizeClass} ${variant} ${isClickable ? 'clickable' : ''}`}
        style={badgeStyles}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={isClickable ? handleClick : undefined}
        onKeyDown={isClickable ? handleKeyDown : undefined}
        role={isClickable ? 'button' : 'presentation'}
        tabIndex={isClickable ? 0 : -1}
        aria-label={tooltip || label}
        title={tooltip || label}
      >
        <span className="badge-label">{label}</span>
        {count !== undefined && count > 0 && (
          <span className="badge-count">{count}</span>
        )}
      </div>

      {/* Tooltip */}
      {showTooltip && tooltip && (
        <div
          className="entity-badge-tooltip"
          style={{
            left: tooltipPosition.x,
            top: tooltipPosition.y,
            transform: 'translateX(-50%) translateY(-100%)'
          }}
        >
          <div className="tooltip-content">
            <div className="tooltip-text">{tooltip}</div>
            {count !== undefined && count > 0 && (
              <div className="tooltip-count">{count} item{count !== 1 ? 's' : ''}</div>
            )}
          </div>
          <div className="tooltip-arrow"></div>
        </div>
      )}
    </>
  );
};

export default EntityBadge;
