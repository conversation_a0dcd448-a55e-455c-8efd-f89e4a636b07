import React, { useState } from 'react';
import './FollowUpCards.css';

/**
 * FollowUpCards component for displaying follow-up questions as interactive cards
 * Features:
 * - Card-based question display
 * - Click-to-ask functionality
 * - Context hints on hover
 * - Responsive grid layout
 * - Keyboard accessibility
 * - Loading states
 */
const FollowUpCards = ({
  questions = [],
  onQuestionClick,
  maxVisible = 6,
  layout = 'grid', // 'grid' or 'list'
  showContextHints = true,
  disabled = false
}) => {
  const [clickedQuestion, setClickedQuestion] = useState(null);
  const [hoveredCard, setHoveredCard] = useState(null);

  // Normalize question text (remove ALL CAPS, improve formatting)
  const normalizeQuestionText = (text) => {
    if (!text) return '';
    
    // Convert ALL CAPS to proper case
    let normalized = text;
    if (text === text.toUpperCase() && text.length > 3) {
      normalized = text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
    }
    
    // Clean up common formatting issues
    normalized = normalized
      .replace(/\s+/g, ' ') // Multiple spaces to single space
      .replace(/\?\s*\?+/g, '?') // Multiple question marks
      .replace(/\.\s*\.+/g, '.') // Multiple periods
      .trim();
    
    // Ensure question ends with proper punctuation
    if (normalized && !normalized.match(/[.?!]$/)) {
      normalized += '?';
    }
    
    return normalized;
  };

  // Handle question click
  const handleQuestionClick = (question, index) => {
    if (disabled || !onQuestionClick) return;

    setClickedQuestion(index);
    
    // Call the callback with the normalized question text
    const rawQuestionText = typeof question === 'string' ? question : question.question;
    const normalizedQuestionText = normalizeQuestionText(rawQuestionText);
    onQuestionClick(normalizedQuestionText);

    // Reset clicked state after animation
    setTimeout(() => {
      setClickedQuestion(null);
    }, 300);
  };

  // Handle keyboard interaction
  const handleKeyDown = (event, question, index) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleQuestionClick(question, index);
    }
  };

  // Limit visible questions
  const visibleQuestions = questions.slice(0, maxVisible);

  if (questions.length === 0) {
    return (
      <div className="follow-up-cards-empty">
        <div className="empty-icon">💭</div>
        <div className="empty-message">No follow-up questions available</div>
      </div>
    );
  }

  return (
    <div className={`follow-up-cards ${layout} ${disabled ? 'disabled' : ''}`}>
      {visibleQuestions.map((question, index) => {
        const rawQuestionText = typeof question === 'string' ? question : question.question;
        const questionText = normalizeQuestionText(rawQuestionText);
        const contextHint = typeof question === 'object' ? question.context_hint : null;
        const isClicked = clickedQuestion === index;
        const isHovered = hoveredCard === index;

        return (
          <div
            key={`question-${index}`}
            className={`follow-up-card ${isClicked ? 'clicked' : ''} ${isHovered ? 'hovered' : ''}`}
            onClick={() => handleQuestionClick(question, index)}
            onKeyDown={(e) => handleKeyDown(e, question, index)}
            onMouseEnter={() => setHoveredCard(index)}
            onMouseLeave={() => setHoveredCard(null)}
            role="button"
            tabIndex={disabled ? -1 : 0}
            aria-label={`Ask: ${questionText}`}
            aria-disabled={disabled}
          >
            <div className="card-content">
              <div className="question-text">
                {questionText}
              </div>
              
              {showContextHints && contextHint && (
                <div className="context-hint">
                  {contextHint}
                </div>
              )}
            </div>

            <div className="card-action">
              <svg 
                className="action-icon" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                strokeWidth="2"
              >
                <path d="M8 12h8m-8 0l4 4m-4-4l4-4" />
              </svg>
            </div>

            {/* Click ripple effect */}
            {isClicked && (
              <div className="click-ripple" />
            )}
          </div>
        );
      })}

      {questions.length > maxVisible && (
        <div className="more-questions-indicator">
          <div className="more-text">
            +{questions.length - maxVisible} more questions
          </div>
          <div className="more-hint">
            Continue the conversation to see more suggestions
          </div>
        </div>
      )}
    </div>
  );
};

export default FollowUpCards;
