import React, { useState } from 'react';
import './SourceTooltip.css';

/**
 * SourceTooltip component for displaying source documents with PDF icons and hover tooltips
 * Features:
 * - Document type icons (PDF, DOC, WEB, OTHER)
 * - Hover tooltips showing title and snippet
 * - Click to open document URL
 * - Category badges
 * - Responsive design
 */
const SourceTooltip = ({ source, index }) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });

  // Get icon based on document type
  const getDocumentIcon = (documentType) => {
    switch (documentType?.toUpperCase()) {
      case 'PDF':
        return (
          <svg className="document-icon pdf" viewBox="0 0 24 24" fill="currentColor">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
            <text x="12" y="16" textAnchor="middle" fontSize="6" fill="currentColor">PDF</text>
          </svg>
        );
      case 'DOC':
      case 'DOCX':
        return (
          <svg className="document-icon doc" viewBox="0 0 24 24" fill="currentColor">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
            <text x="12" y="16" textAnchor="middle" fontSize="6" fill="currentColor">DOC</text>
          </svg>
        );
      case 'WEB':
      case 'HTML':
      case 'HTM':
        return (
          <svg className="document-icon web" viewBox="0 0 24 24" fill="currentColor">
            <path d="M16.36,14C16.44,13.34 16.5,12.68 16.5,12C16.5,11.32 16.44,10.66 16.36,10H19.74C19.9,10.64 20,11.31 20,12C20,12.69 19.9,13.36 19.74,14M14.59,19.56C15.19,18.45 15.65,17.25 15.97,16H18.92C17.96,17.65 16.43,18.93 14.59,19.56M14.34,14H9.66C9.56,13.34 9.5,12.68 9.5,12C9.5,11.32 9.56,10.65 9.66,10H14.34C14.43,10.65 14.5,11.32 14.5,12C14.5,12.68 14.43,13.34 14.34,14M12,19.96C11.17,18.76 10.5,17.43 10.09,16H13.91C13.5,17.43 12.83,18.76 12,19.96M8,8H5.08C6.03,6.34 7.57,5.06 9.4,4.44C8.8,5.55 8.35,6.75 8,8M5.08,16H8C8.35,17.25 8.8,18.45 9.4,19.56C7.57,18.93 6.03,17.65 5.08,16M4.26,14C4.1,13.36 4,12.69 4,12C4,11.31 4.1,10.64 4.26,10H7.64C7.56,10.66 7.5,11.32 7.5,12C7.5,12.68 7.56,13.34 7.64,14M12,4.03C12.83,5.23 13.5,6.57 13.91,8H10.09C10.5,6.57 11.17,5.23 12,4.03M18.92,8H15.97C15.65,6.75 15.19,5.55 14.59,4.44C16.43,5.07 17.96,6.34 18.92,8M12,2C6.47,2 2,6.5 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z" />
          </svg>
        );
      default:
        return (
          <svg className="document-icon other" viewBox="0 0 24 24" fill="currentColor">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
          </svg>
        );
    }
  };

  // Get category color
  const getCategoryColor = (category) => {
    const colors = {
      'EMS': '#40C4FF',
      'SEP': '#A5D6A7',
      'MMC': '#FFAB91',
      'TRADING': '#E1BEE7',
      'RISK': '#FFCDD2',
      'COMPLIANCE': '#FFF9C4',
      'WORKFLOW': '#C8E6C9',
      'SYSTEM': '#BBDEFB'
    };
    return colors[category?.toUpperCase()] || '#F5F5F5';
  };

  // Handle mouse enter for tooltip
  const handleMouseEnter = (event) => {
    const rect = event.currentTarget.getBoundingClientRect();
    setTooltipPosition({
      x: rect.left + rect.width / 2,
      y: rect.top - 10
    });
    setShowTooltip(true);
  };

  // Handle mouse leave
  const handleMouseLeave = () => {
    setShowTooltip(false);
  };

  // Handle click to open document
  const handleClick = () => {
    const documentUrl = source.url || source.link;
    if (documentUrl) {
      window.open(documentUrl, '_blank', 'noopener,noreferrer');
    }
  };

  // Validate source object and normalize data
  if (!source) {
    return null;
  }

  // Extract and normalize source fields with fallbacks
  const title = source.title || source.name || `Source ${index + 1}`;
  const url = source.url || source.link;
  const document_type = source.document_type || source.type || 'Document';
  const snippet = source.snippet || source.content || source.text || '';
  const category = source.category;

  // Debug logging for development
  if (process.env.NODE_ENV === 'development' && (!source.title || !snippet)) {
    console.log(`SourceTooltip [${index}]: Missing data in source:`, {
      hasTitle: !!source.title,
      hasSnippet: !!snippet,
      hasUrl: !!url,
      sourceKeys: Object.keys(source),
      source: source
    });
  }

  return (
    <>
      <div
        className={`source-tooltip-container ${url ? 'clickable' : ''}`}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        role={url ? 'button' : 'presentation'}
        tabIndex={url ? 0 : -1}
        onKeyDown={(e) => {
          if (url && (e.key === 'Enter' || e.key === ' ')) {
            e.preventDefault();
            handleClick();
          }
        }}
        aria-label={url ? `Open ${title}` : title}
      >
        <div className="source-icon-wrapper">
          {getDocumentIcon(document_type)}
          {category && (
            <div 
              className="source-category-badge"
              style={{ backgroundColor: getCategoryColor(category) }}
            >
              {category}
            </div>
          )}
        </div>
        
        <div className="source-info">
          <div className="source-title" title={title}>
            {title}
          </div>
          <div className="source-type">
            {document_type || 'Document'}
          </div>
        </div>

        {url && (
          <div className="source-link-indicator">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z" />
            </svg>
          </div>
        )}
      </div>

      {/* Tooltip */}
      {showTooltip && (
        <div
          className="source-tooltip"
          style={{
            left: tooltipPosition.x,
            top: tooltipPosition.y,
            transform: 'translateX(-50%) translateY(-100%)'
          }}
        >
          <div className="tooltip-content">
            <div className="tooltip-title">{title}</div>
            {snippet ? (
              <div className="tooltip-snippet">{snippet}</div>
            ) : (
              <div className="tooltip-snippet tooltip-no-content">No preview available</div>
            )}
            {url && (
              <div className="tooltip-url">{url}</div>
            )}
            {category && (
              <div className="tooltip-category">
                Category: <span style={{ color: getCategoryColor(category) }}>{category}</span>
              </div>
            )}
            {source.score !== undefined && (
              <div className="tooltip-score">
                Relevance: {(source.score * 100).toFixed(0)}%
              </div>
            )}
          </div>
          <div className="tooltip-arrow"></div>
        </div>
      )}
    </>
  );
};

export default SourceTooltip;
