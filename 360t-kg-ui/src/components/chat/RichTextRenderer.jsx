import React from 'react';
import { marked } from 'marked';
import DOMPurify from 'dompurify';
import './RichTextRenderer.css';

/**
 * RichTextRenderer component for enhanced formatting of Answer and Explanation sections
 * Features:
 * - Enhanced markdown parsing with rich typography
 * - Automatic formatting improvements for plain text
 * - Better paragraph separation and visual hierarchy
 * - Bold/italic emphasis for key terms
 * - Structured lists and bullet points
 * - Improved readability with proper spacing
 */
function RichTextRenderer({ content, className = '', sectionType = 'answer' }) {
  if (!content) {
    return null;
  }

  // Enhanced content processing for better formatting
  const enhanceContent = (text) => {
    if (!text || typeof text !== 'string') {
      return text;
    }

    let enhanced = text;

    // Add proper paragraph breaks for double newlines
    enhanced = enhanced.replace(/\n\n/g, '\n\n');

    // Convert plain text patterns to markdown for better formatting
    
    // 1. Enhance key terms and important concepts (words in quotes or after colons)
    enhanced = enhanced.replace(/:\s*([A-Z][^.!?]*[.!?])/g, ': **$1**');
    
    // 2. Enhanced quote handling that preserves HTML attributes
    // Skip quotes that are part of HTML attributes (inside < > tags)
    enhanced = enhanced.replace(/"([^"]+)"/g, (match, p1, offset, string) => {
      // Check if this quote is inside an HTML tag
      const beforeQuote = string.substring(0, offset);
      const afterQuote = string.substring(offset + match.length);
      const lastOpenTag = beforeQuote.lastIndexOf('<');
      const lastCloseTag = beforeQuote.lastIndexOf('>');
      const nextCloseTag = afterQuote.indexOf('>');
      
      // If we're inside an HTML tag (< came after >) and there's a closing > ahead, preserve the quote
      if (lastOpenTag > lastCloseTag && nextCloseTag !== -1) {
        return match; // Preserve HTML attribute quotes
      }
      
      return `**"${p1}"**`; // Format regular quotes
    });
    
    // 3. Convert numbered sequences to proper lists
    enhanced = enhanced.replace(/(\d+\.\s+[^\n]+)/g, '\n$1');
    
    // 4. Convert bullet-like patterns to proper markdown lists
    enhanced = enhanced.replace(/^[-•*]\s+/gm, '- ');
    
    // 5. Emphasize technical terms and system names (capitalized words)
    enhanced = enhanced.replace(/\b([A-Z][A-Z_]+[A-Z])\b/g, '*$1*');
    
    // 6. Bold important action words and concepts
    const importantWords = [
      'important', 'critical', 'essential', 'key', 'main', 'primary', 'core',
      'note', 'warning', 'caution', 'remember', 'ensure', 'must', 'required',
      'trading', 'risk', 'limit', 'order', 'execution', 'settlement', 'compliance'
    ];
    
    importantWords.forEach(word => {
      const regex = new RegExp(`\\b(${word})\\b`, 'gi');
      enhanced = enhanced.replace(regex, '**$1**');
    });

    // 7. Add proper spacing around sections
    enhanced = enhanced.replace(/([.!?])\s*([A-Z])/g, '$1\n\n$2');
    
    // 8. Clean up excessive spacing
    enhanced = enhanced.replace(/\n{3,}/g, '\n\n');
    enhanced = enhanced.trim();

    return enhanced;
  };

  // Configure marked for rich formatting
  marked.setOptions({
    breaks: true, // Enable line breaks
    gfm: true, // GitHub Flavored Markdown
    sanitize: false, // We'll use DOMPurify
    smartLists: true,
    smartypants: true, // Smart quotes and dashes
    headerIds: false,
    mangle: false,
  });

  // Process content based on section type
  let processedContent = content;
  
  if (sectionType === 'answer' || sectionType === 'explanation') {
    processedContent = enhanceContent(content);
  }

  // Convert to HTML
  const rawMarkup = marked.parse(processedContent);
  
  // Sanitize HTML
  const sanitizedMarkup = DOMPurify.sanitize(rawMarkup, {
    ALLOWED_TAGS: [
      'p', 'br', 'strong', 'em', 'u', 'strike', 'del', 'ins',
      'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      'ul', 'ol', 'li',
      'blockquote',
      'code', 'pre',
      'a', 'hr',
      'div', 'span'
    ],
    ALLOWED_ATTR: [
      'href', 'title', 'target', 'rel',
      'class', 'id',
      'data-ref', 'data-type', 'data-index' // Allow data attributes for reference links
    ],
    ALLOW_DATA_ATTR: true, // Enable data attributes for reference functionality
    FORBID_SCRIPT: true,
    FORBID_TAGS: ['script', 'object', 'embed', 'form', 'input'],
    FORBID_ATTR: ['style', 'onload', 'onerror', 'onclick']
  });

  return (
    <div 
      className={`rich-text-renderer ${sectionType} ${className}`}
      dangerouslySetInnerHTML={{ __html: sanitizedMarkup }}
    />
  );
}

export default RichTextRenderer;
