import React, { useMemo, useCallback, useState } from 'react';
import <PERSON>down<PERSON>enderer from '../MarkdownRenderer';
import RichTextRenderer from './RichTextRenderer';
import SourceTooltip from './SourceTooltip';
import './AnswerWithReferences.css';

/**
 * AnswerWithReferences component
 * Renders markdown content with clickable [1], [2] reference links that show source details or trigger NodeDetails
 * Features:
 * - Parses [number] references in the text
 * - Makes references clickable with visual highlighting
 * - Shows source details in modals or triggers NodeDetails panel for entities
 * - Integrates with existing MarkdownRenderer for other content
 * - Enhanced to support both sources and entities/nodes
 */
function AnswerWithReferences({ content, sources = [], entities = [], onSendMessage, onNodeSelect }) {
  const [selectedReference, setSelectedReference] = useState(null);

  // Parse content to identify and enhance references
  const { enhancedContent, references } = useMemo(() => {
    if (!content || typeof content !== 'string') {
      return { enhancedContent: content, references: [] };
    }

    // Debug logging for reference mapping
    if (process.env.NODE_ENV === 'development') {
      console.log('AnswerWithReferences Debug:', {
        content: content.substring(0, 200) + '...',
        sourcesCount: sources.length,
        entitiesCount: entities.length,
        sourcesData: sources.slice(0, 3),
        entitiesData: entities.slice(0, 3)
      });
    }

    const refs = [];
    let processedContent = content;

    // Find all [number] patterns in the content, including those in parentheses like ([1]) or ([1], [2])
    const referencePattern = /\[(\d+)\]/g;
    let match;
    const foundRefs = new Set();

    while ((match = referencePattern.exec(content)) !== null) {
      const refNumber = parseInt(match[1], 10);
      if (!foundRefs.has(refNumber)) {
        foundRefs.add(refNumber);

        // Try to find corresponding source or entity
        // First check if sources/entities arrays are properly indexed
        const source = sources.find(s => s.reference_number === refNumber) || 
                      sources[refNumber - 1] || 
                      null;
        const entity = entities.find(e => e.reference_number === refNumber) || 
                      entities[refNumber - 1] || 
                      null;

        refs.push({
          number: refNumber,
          index: refNumber - 1, // Convert to 0-based index
          originalText: match[0],
          source: source,
          entity: entity,
          type: entity ? 'entity' : (source ? 'source' : 'unknown')
        });
      }
    }

    // Replace [number] with clickable spans, adding type information
    foundRefs.forEach(refNumber => {
      const ref = refs.find(r => r.number === refNumber);
      const refPattern = new RegExp(`\\[${refNumber}\\]`, 'g');
      processedContent = processedContent.replace(refPattern,
        `<span class="reference-link ${ref?.type || 'unknown'}" data-ref="${refNumber}" data-type="${ref?.type || 'unknown'}" data-index="${refNumber - 1}">[${refNumber}]</span>`
      );
    });

    return {
      enhancedContent: processedContent,
      references: refs.sort((a, b) => a.number - b.number)
    };
  }, [content, sources, entities]);

  // Handle reference clicks
  const handleReferenceClick = useCallback((refNumber, index, type) => {
    console.log('handleReferenceClick called:', { refNumber, index, type, onNodeSelect: !!onNodeSelect });
    console.log('Available references:', references);
    
    const ref = references.find(r => r.number === refNumber);

    if (!ref) {
      console.warn(`No reference found for [${refNumber}] in references:`, references);
      return;
    }

    console.log('Found reference:', ref);

    if (type === 'entity' && ref.entity && onNodeSelect) {
      // Trigger NodeDetails panel for entities
      // Enhanced entity-to-node conversion with comprehensive field mapping
      const nodeData = {
        id: ref.entity.id,
        // Ensure name field is populated with fallbacks
        name: ref.entity.name || ref.entity.label || ref.entity.title || `Entity ${refNumber}`,
        label: ref.entity.label || ref.entity.name || `Entity ${refNumber}`,
        title: ref.entity.title || ref.entity.name || ref.entity.label,
        description: ref.entity.description || ref.entity.summary || ref.entity.content || '',
        category: ref.entity.category || ref.entity.type || 'Unknown',
        properties: ref.entity.properties || {},
        relevance_score: ref.entity.relevance_score || 0.5,
        // Add additional fields that might be expected by NodeDetails
        type: ref.entity.type || ref.entity.category || 'entity',
        summary: ref.entity.summary || ref.entity.description || '',
        content: ref.entity.content || ref.entity.description || ref.entity.summary || '',
        // Reference information for debugging
        reference_number: refNumber,
        source: 'chat_reference'
      };

      console.log(`Opening NodeDetails for entity [${refNumber}]:`, nodeData);
      console.log('Original entity data:', ref.entity);
      onNodeSelect(nodeData);

    } else if (type === 'source' && ref.source) {
      // Show source modal for documents
      setSelectedReference({
        number: refNumber,
        source: ref.source
      });

      console.log(`Showing source [${refNumber}]:`, ref.source);
    } else {
      console.warn(`No valid ${type} found for reference [${refNumber}]. ref.entity:`, ref.entity, 'ref.source:', ref.source, 'onNodeSelect:', !!onNodeSelect);
    }
  }, [references, onNodeSelect]);

  // Close modal
  const closeModal = useCallback(() => {
    setSelectedReference(null);
  }, []);

  // Handle escape key
  React.useEffect(() => {
    if (!selectedReference) return;

    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        closeModal();
      }
    };

    document.addEventListener('keydown', handleEscape);
    document.body.style.overflow = 'hidden';

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = '';
    };
  }, [selectedReference, closeModal]);

  // Enhanced MarkdownRenderer that handles reference clicks
  const EnhancedMarkdownRenderer = useMemo(() => {
    return React.forwardRef((props, ref) => {
      const containerRef = React.useRef(null);

      React.useEffect(() => {
        if (!containerRef.current) return;

        // Add click handlers to reference links
        const referenceLinks = containerRef.current.querySelectorAll('.reference-link');
        
        const handleClick = (e) => {
          e.preventDefault();
          e.stopPropagation();

          const refNumber = parseInt(e.target.dataset.ref, 10);
          const index = parseInt(e.target.dataset.index, 10);
          const type = e.target.dataset.type;

          console.log('Reference click debug:', { refNumber, index, type, target: e.target });

          if (!isNaN(refNumber) && !isNaN(index) && type) {
            handleReferenceClick(refNumber, index, type);
          } else {
            console.warn('Invalid reference click data:', { refNumber, index, type });
          }
        };

        referenceLinks.forEach(link => {
          link.addEventListener('click', handleClick);
          
          // Add hover effects
          link.addEventListener('mouseenter', () => {
            link.style.backgroundColor = '#e3f2fd';
            link.style.transform = 'translateY(-1px)';
          });
          
          link.addEventListener('mouseleave', () => {
            link.style.backgroundColor = '';
            link.style.transform = '';
          });
        });

        // Cleanup
        return () => {
          referenceLinks.forEach(link => {
            link.removeEventListener('click', handleClick);
          });
        };
      }, [handleReferenceClick]); // Add handleReferenceClick to dependencies

      return (
        <div ref={containerRef}>
          <RichTextRenderer {...props} sectionType="answer" />
        </div>
      );
    });
  }, [handleReferenceClick]);

  if (!content) {
    return null;
  }

  return (
    <div className="answer-with-references">
      <EnhancedMarkdownRenderer 
        content={enhancedContent}
        onSendMessage={onSendMessage}
        className="answer-content"
      />
      
      {/* Reference Summary */}
      {references.length > 0 && (
        <div className="references-summary">
          <details className="references-details">
            <summary className="references-summary-header">
              References ({references.length})
            </summary>
            <div className="references-list">
              {references.map((ref) => (
                <div key={ref.number} className="reference-item">
                  <button
                    className={`reference-number ${ref.type}`}
                    onClick={() => handleReferenceClick(ref.number, ref.index, ref.type)}
                    title={ref.type === 'entity' ? `View entity [${ref.number}]` : `View source [${ref.number}]`}
                  >
                    [{ref.number}]
                  </button>
                  <div className="reference-preview">
                    {ref.type === 'entity' && ref.entity ? (
                      <>
                        <span className="reference-content entity-ref">
                          <strong>{ref.entity.name || `Entity ${ref.number}`}</strong>
                          {ref.entity.description && ` - ${ref.entity.description.substring(0, 100)}`}
                          {ref.entity.description && ref.entity.description.length > 100 ? '...' : ''}
                          {!ref.entity.description && ref.entity.summary && ` - ${ref.entity.summary.substring(0, 100)}`}
                          {!ref.entity.description && ref.entity.summary && ref.entity.summary.length > 100 ? '...' : ''}
                        </span>
                        {ref.entity.category && (
                          <span className="reference-category"> ({ref.entity.category})</span>
                        )}
                      </>
                    ) : ref.type === 'source' && ref.source ? (
                      <>
                        <span className="reference-content">
                          {(ref.source.content || ref.source.text || ref.source.title || 'Source available').substring(0, 100)}
                          {(ref.source.content || ref.source.text || ref.source.title || '').length > 100 ? '...' : ''}
                        </span>
                        {ref.source.title && (
                          <span className="reference-title"> - {ref.source.title}</span>
                        )}
                      </>
                    ) : ref.source || ref.entity ? (
                      <span className="reference-content">
                        Reference [{ref.number}] - {ref.type} available (click to view)
                      </span>
                    ) : (
                      <span className="reference-missing">Reference [{ref.number}] not available</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </details>
        </div>
      )}

      {/* Reference Modal */}
      {selectedReference && (
        <div className="reference-modal-overlay" onClick={closeModal}>
          <div className="reference-modal" onClick={(e) => e.stopPropagation()}>
            <div className="reference-modal-header">
              <h3>Source [{selectedReference.number}]</h3>
              <button className="reference-modal-close" onClick={closeModal}>
                &times;
              </button>
            </div>
            <div className="reference-modal-content">
              <div className="source-details">
                <p><strong>Content:</strong></p>
                <div className="source-content">
                  {selectedReference.source.content || selectedReference.source.text || 'No content available'}
                </div>
                {selectedReference.source.title && (
                  <p><strong>Title:</strong> {selectedReference.source.title}</p>
                )}
                {selectedReference.source.url && (
                  <p>
                    <strong>URL:</strong>{' '}
                    <a 
                      href={selectedReference.source.url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                    >
                      {selectedReference.source.url}
                    </a>
                  </p>
                )}
                {selectedReference.source.type && (
                  <p><strong>Type:</strong> {selectedReference.source.type}</p>
                )}
                {selectedReference.source.score !== undefined && (
                  <p><strong>Relevance Score:</strong> {(selectedReference.source.score * 100).toFixed(1)}%</p>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default AnswerWithReferences;