/* Structured Response Component - Consolidated using utilities */

.structured-response {
  display: flex;
  flex-direction: column;
  gap: var(--360t-space-5);
  padding: var(--360t-space-6);
  font-family: var(--360t-font-family-primary);
  font-size: var(--360t-text-base);
  line-height: var(--360t-leading-normal);
  max-width: 100%;
  width: 100%;
  background: var(--360t-white);
  border-radius: var(--360t-radius-xl);
  border: 1px solid var(--360t-mid-gray);
  color: var(--360t-text);
  overflow-x: hidden;
  box-sizing: border-box;
}

/* Error State */
.structured-response.error {
  padding: var(--360t-space-4);
  border: 1px solid #ef4444;
  border-radius: var(--360t-radius-lg);
  background-color: #fef2f2;
}

.error-message h4 {
  color: #dc2626;
  margin: 0 0 var(--360t-space-2) 0;
  font-size: var(--360t-text-base);
}

.error-message p {
  color: #7f1d1d;
  margin: 0 0 var(--360t-space-4) 0;
  font-size: var(--360t-text-sm);
}

.error-message details {
  margin-top: var(--360t-space-4);
}

.error-message summary {
  cursor: pointer;
  color: #dc2626;
  font-weight: var(--360t-font-medium);
}

.error-message pre {
  background: var(--360t-light-gray);
  padding: var(--360t-space-3);
  border-radius: var(--360t-radius-sm);
  overflow-x: auto;
  font-size: var(--360t-text-xs);
  margin-top: var(--360t-space-2);
}

/* Primary Answer - Clean White Design */
.response-answer {
  font-size: var(--360t-text-lg);
  line-height: var(--360t-leading-relaxed);
  color: var(--360t-text);
  font-weight: var(--360t-font-medium);
  padding: var(--360t-space-8);
  background: var(--360t-white);
  border-radius: var(--360t-radius-xl);
  border: 1px solid var(--360t-mid-gray);
  word-wrap: break-word;
  overflow-wrap: break-word;
  overflow-x: auto;
  margin-bottom: 0;
  max-width: 100%;
  min-width: 0;
  box-sizing: border-box;
}

/* Category Badges */
.response-badges {
  display: flex;
  flex-wrap: wrap;
  gap: var(--360t-space-2);
  margin: var(--360t-space-1) 0;
}

/* Explanation Section - Clean Professional Design */
.response-explanation {
  padding: var(--360t-space-6);
  background: var(--360t-light-gray);
  border-radius: var(--360t-radius-xl);
  border: 1px solid var(--360t-mid-gray);
  font-size: var(--360t-text-base);
  line-height: var(--360t-leading-normal);
  color: var(--360t-text);
}

.explanation-title {
  margin: 0 0 var(--360t-space-4) 0;
  font-size: var(--360t-text-base);
  font-weight: var(--360t-font-semibold);
  color: var(--360t-primary);
  display: flex;
  align-items: center;
  padding-bottom: var(--360t-space-3);
  border-bottom: 1px solid var(--360t-mid-gray);
  cursor: pointer;
}

.explanation-title:hover {
  color: var(--360t-primary-dark);
}

.explanation-section {
  margin-bottom: var(--360t-space-6);
}

.explanation-section:last-child {
  margin-bottom: 0;
}

.explanation-section-title {
  margin: 0 0 var(--360t-space-2) 0;
  font-size: var(--360t-text-base);
  font-weight: var(--360t-font-semibold);
  color: var(--360t-dark-gray);
  border-bottom: 2px solid var(--360t-mid-gray);
  padding-bottom: var(--360t-space-1);
}

.explanation-content {
  margin-bottom: var(--360t-space-4);
}

.explanation-entities {
  margin-top: var(--360t-space-3);
  padding-top: var(--360t-space-3);
  border-top: 1px solid var(--360t-mid-gray);
}

.explanation-entities .entity-refs-label {
  font-size: var(--360t-text-sm);
  color: var(--360t-dark-gray);
  font-weight: var(--360t-font-medium);
  margin-right: var(--360t-space-2);
}

.explanation-entities .entity-ref-button {
  display: inline-flex;
  align-items: center;
  gap: var(--360t-space-1);
  margin: var(--360t-space-1) var(--360t-space-2) var(--360t-space-1) 0;
  padding: var(--360t-space-2) var(--360t-space-3);
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  color: #065f46;
  border: 1px solid #a7f3d0;
  border-radius: var(--360t-radius-md);
  font-size: var(--360t-text-sm);
  font-weight: var(--360t-font-medium);
  font-family: var(--360t-font-family-primary);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  letter-spacing: 0.025em;
  position: relative;
  overflow: hidden;
}

.explanation-entities .entity-ref-button:hover {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border-color: #6ee7b7;
  transform: translateY(-1px);
  box-shadow: var(--360t-shadow-md);
  color: #047857;
}

/* Enhanced Button Interactions with Accessibility */
.entity-ref-button:focus,
.entity-ref-button:focus-visible {
  outline: 2px solid var(--360t-primary);
  outline-offset: 2px;
  border-color: var(--360t-primary);
  box-shadow: 0 0 0 3px rgba(0, 151, 58, 0.15);
}

.entity-ref-button:active {
  transform: translateY(0);
  box-shadow: var(--360t-shadow-sm);
}

/* Summary elements accessibility */
.explanation-title:focus,
.sources-title:focus,
.entities-title:focus {
  outline: 2px solid var(--360t-primary);
  outline-offset: 2px;
  border-radius: var(--360t-radius-sm);
}

/* Show more button accessibility */
.show-more-entities:focus,
.show-more-entities:focus-visible {
  outline: 2px solid #0ea5e9;
  outline-offset: 2px;
}

/* Ripple Effect */
.entity-ref-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.entity-ref-button:active::before {
  width: 100px;
  height: 100px;
}

/* Enhanced Sections */
.response-sections {
  border: 1px solid var(--360t-mid-gray);
  border-radius: var(--360t-radius-lg);
  overflow: hidden;
  background: linear-gradient(135deg, var(--360t-white) 0%, #fefefe 100%);
  box-shadow: var(--360t-shadow-sm);
  transition: all 0.2s ease;
  margin: var(--360t-space-3) 0;
}

.response-sections:hover {
  border-color: var(--360t-primary);
  box-shadow: var(--360t-shadow-md);
  transform: translateY(-1px);
}

.sections-title {
  margin: 0;
  font-size: var(--360t-text-sm);
  font-weight: var(--360t-font-semibold);
  color: var(--360t-text);
  padding: var(--360t-space-3);
  background: linear-gradient(135deg, var(--360t-light-gray) 0%, #f1f5f9 100%);
  border-bottom: 1px solid var(--360t-mid-gray);
  letter-spacing: -0.01em;
}

.response-section {
  border-bottom: 1px solid var(--360t-mid-gray);
}

.response-section:last-child {
  border-bottom: none;
}

.section-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--360t-space-2) var(--360t-space-3);
  background: var(--360t-white);
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-align: left;
}

.section-header:hover {
  background-color: var(--360t-light-gray);
}

.section-header.expanded {
  background-color: var(--360t-light-gray);
}

.section-title {
  font-weight: var(--360t-font-medium);
  color: var(--360t-text);
  font-size: var(--360t-text-sm);
}

.section-toggle {
  font-size: 1.2rem;
  color: var(--360t-dark-gray);
  font-weight: bold;
  min-width: 20px;
  text-align: center;
}

.section-content {
  padding: var(--360t-space-3);
  background-color: #fefefe;
  border-top: 1px solid var(--360t-light-gray);
}

.section-entities {
  margin-top: var(--360t-space-3);
  padding-top: var(--360t-space-3);
  border-top: 1px solid var(--360t-mid-gray);
}

.entity-refs-label {
  font-size: var(--360t-text-sm);
  color: var(--360t-dark-gray);
  font-weight: var(--360t-font-medium);
  margin-right: var(--360t-space-2);
}

.entity-ref-button {
  display: inline-flex;
  align-items: center;
  gap: var(--360t-space-1);
  margin: var(--360t-space-1) var(--360t-space-2) var(--360t-space-1) 0;
  padding: var(--360t-space-1) var(--360t-space-3);
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1e40af;
  border: 1px solid #93c5fd;
  border-radius: var(--360t-radius-md);
  font-size: var(--360t-text-xs);
  font-weight: var(--360t-font-medium);
  font-family: var(--360t-font-family-primary);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  letter-spacing: 0.025em;
  position: relative;
  overflow: hidden;
}

.entity-ref-button:hover {
  background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 100%);
  border-color: #60a5fa;
  transform: translateY(-1px);
  box-shadow: var(--360t-shadow-md);
  color: #1d4ed8;
}

/* Sources Section - Clean Professional Design */
.response-sources {
  padding: var(--360t-space-6);
  background: var(--360t-light-gray);
  border-radius: var(--360t-radius-xl);
  border: 1px solid var(--360t-mid-gray);
  font-size: var(--360t-text-base);
  line-height: var(--360t-leading-normal);
  color: var(--360t-text);
}

.sources-title {
  margin: 0 0 var(--360t-space-4) 0;
  font-size: var(--360t-text-base);
  font-weight: var(--360t-font-semibold);
  color: var(--360t-primary);
  display: flex;
  align-items: center;
  cursor: pointer;
  padding-bottom: var(--360t-space-3);
  border-bottom: 1px solid var(--360t-mid-gray);
}

.sources-title:hover {
  color: var(--360t-primary-dark);
}

.sources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--360t-space-2);
}

/* Entities Section - Clean Professional Design */
.response-entities {
  padding: var(--360t-space-6);
  background: var(--360t-light-gray);
  border-radius: var(--360t-radius-xl);
  border: 1px solid var(--360t-mid-gray);
  font-size: var(--360t-text-base);
  line-height: var(--360t-leading-normal);
  color: var(--360t-text);
}

.entities-title {
  margin: 0 0 var(--360t-space-4) 0;
  font-size: var(--360t-text-base);
  font-weight: var(--360t-font-semibold);
  color: var(--360t-primary);
  display: flex;
  align-items: center;
  padding-bottom: var(--360t-space-3);
  border-bottom: 1px solid var(--360t-mid-gray);
  cursor: pointer;
}

.entities-title:hover {
  color: var(--360t-primary-dark);
}

/* Ensure Related Entities and References have consistent collapsed header height */
.sources-title,
.entities-title {
  min-height: 40px;
}

/* Normalize spacing inside details wrappers for visual parity */
.response-sources details,
.response-entities details {
  margin: 0;
}

.show-more-entities {
  margin-top: var(--360t-space-2);
  padding: var(--360t-space-2) var(--360t-space-3);
  background-color: #0ea5e9;
  color: white;
  border: none;
  border-radius: var(--360t-radius-sm);
  font-size: var(--360t-text-sm);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.show-more-entities:hover {
  background-color: #0284c7;
}

/* Follow-up Questions - Clean Professional Design */
.response-followup {
  padding: var(--360t-space-6);
  background: var(--360t-light-gray);
  border-radius: var(--360t-radius-xl);
  border: 1px solid var(--360t-mid-gray);
}

.followup-title {
  margin: 0 0 var(--360t-space-4) 0;
  font-size: var(--360t-text-base);
  font-weight: var(--360t-font-semibold);
  color: var(--360t-primary);
  display: flex;
  align-items: center;
  padding-bottom: var(--360t-space-3);
  border-bottom: 1px solid var(--360t-mid-gray);
}

/* Follow-up Questions List */
.followup-questions-list {
  display: flex;
  flex-direction: column;
  gap: var(--360t-space-2);
}

.followup-question-link {
  display: block;
  width: 100%;
  text-align: left;
  padding: var(--360t-space-3) var(--360t-space-4);
  background: var(--360t-white);
  border: 1px solid var(--360t-border);
  border-radius: var(--360t-radius-lg);
  color: var(--360t-text);
  font-size: var(--360t-text-sm);
  font-weight: var(--360t-font-medium);
  line-height: var(--360t-leading-normal);
  cursor: pointer;
  text-decoration: none;
  font-family: inherit;
}

.followup-question-link:hover {
  background: var(--360t-primary);
  border-color: var(--360t-primary-dark);
  color: var(--360t-white);
}

.followup-question-link:focus,
.followup-question-link:focus-visible {
  outline: 2px solid var(--360t-primary);
  outline-offset: 2px;
}

/* Subtle Metadata Section */
.response-metadata {
  margin-top: var(--360t-space-3);
  padding: var(--360t-space-2);
  background-color: #fafbfc;
  border-radius: var(--360t-radius-lg);
  border: 1px solid #e2e8f0;
  font-size: var(--360t-text-xs);
  color: var(--360t-dark-gray);
  opacity: 0.85;
}

.response-metadata summary {
  cursor: pointer;
  font-weight: var(--360t-font-medium);
  color: var(--360t-dark-gray);
  font-size: var(--360t-text-xs);
  letter-spacing: 0.02em;
  padding: var(--360t-space-2) 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.response-metadata summary::after {
  content: "▼";
  font-size: 10px;
  transition: transform 0.2s ease;
}

.response-metadata[open] summary::after {
  transform: rotate(180deg);
}

.metadata-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--360t-space-3);
  margin-top: var(--360t-space-3);
  align-items: start;
}

.metadata-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--360t-space-2);
  background-color: var(--360t-white);
  border-radius: var(--360t-radius-md);
  border: 1px solid var(--360t-mid-gray);
  gap: var(--360t-space-2);
  min-height: 2rem;
  box-shadow: none;
}

.metadata-item .metadata-label {
  flex-shrink: 0;
  min-width: 120px;
  font-weight: var(--360t-font-semibold);
}

.metadata-item .metadata-value {
  flex-grow: 1;
  text-align: right;
  min-width: 0;
  font-weight: var(--360t-font-medium);
}

.metadata-label {
  font-size: var(--360t-text-xs);
  color: var(--360t-dark-gray);
  font-weight: var(--360t-font-medium);
}

.metadata-value {
  font-size: var(--360t-text-xs);
  color: var(--360t-text);
  font-weight: var(--360t-font-semibold);
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}

.metadata-value.algorithm-name {
  text-transform: capitalize;
  font-weight: var(--360t-font-bold);
  color: #1e40af;
  cursor: help;
  position: relative;
  padding: var(--360t-space-1) var(--360t-space-2);
  background-color: #dbeafe;
  border-radius: var(--360t-radius-sm);
  border: 1px solid #93c5fd;
  transition: all 0.2s ease;
}

.metadata-value.algorithm-name:hover {
  background-color: #bfdbfe;
  border-color: #60a5fa;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.15);
}

/* Enhanced Metadata Sections */
.metadata-section {
  margin-top: var(--360t-space-4);
  padding: var(--360t-space-3);
  background-color: #f8fafc;
  border-radius: var(--360t-radius-md);
  border: 1px solid #e2e8f0;
}

.metadata-section-title {
  font-size: var(--360t-text-sm);
  font-weight: var(--360t-font-semibold);
  color: #2d3748;
  margin: 0 0 var(--360t-space-2) 0;
  display: flex;
  align-items: center;
  gap: var(--360t-space-2);
}

.metadata-subsection {
  margin-top: var(--360t-space-3);
  padding: var(--360t-space-2);
  background-color: var(--360t-white);
  border-radius: var(--360t-radius-sm);
  border: 1px solid var(--360t-mid-gray);
}

.metadata-subsection-title {
  font-size: var(--360t-text-sm);
  font-weight: var(--360t-font-semibold);
  color: #4a5568;
  margin: 0 0 var(--360t-space-2) 0;
}

.metadata-subsection .metadata-item {
  margin-bottom: var(--360t-space-1);
  padding: var(--360t-space-1) 0;
  background: transparent;
  border: none;
}

.metadata-subsection .metadata-item:last-child {
  margin-bottom: 0;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .structured-response {
    gap: var(--360t-space-2);
    padding: var(--360t-space-4);
  }

  .response-answer {
    font-size: var(--360t-text-base);
    padding: var(--360t-space-6) var(--360t-space-4);
    margin-bottom: var(--360t-space-4);
  }

  .response-explanation,
  .response-sources,
  .response-entities {
    padding: var(--360t-space-4);
    margin: var(--360t-space-3) 0 var(--360t-space-4) 0;
  }

  .explanation-title,
  .sources-title,
  .entities-title {
    font-size: var(--360t-text-base);
    margin-bottom: var(--360t-space-3);
    padding-bottom: var(--360t-space-2);
  }

  .sources-grid {
    grid-template-columns: 1fr;
    gap: var(--360t-space-2);
  }

  .metadata-grid {
    grid-template-columns: 1fr;
    gap: var(--360t-space-2);
  }

  .section-header {
    padding: var(--360t-space-2) var(--360t-space-2);
  }

  .section-content {
    padding: var(--360t-space-2);
  }

  .response-followup {
    padding: var(--360t-space-4);
    margin: var(--360t-space-3) 0;
  }

  .response-metadata {
    padding: var(--360t-space-3);
    margin-top: var(--360t-space-4);
    font-size: var(--360t-text-xs);
  }

  /* Make entity reference buttons more touch-friendly */
  .entity-ref-button {
    padding: var(--360t-space-2) var(--360t-space-3);
    margin: var(--360t-space-1) var(--360t-space-2) var(--360t-space-1) 0;
    min-height: 36px;
  }

  /* Adjust follow-up cards for mobile */
  .show-more-entities {
    padding: var(--360t-space-2) var(--360t-space-4);
    margin-top: var(--360t-space-2);
    min-height: 40px;
  }
}

/* Extra small screens and high zoom levels */
@media (max-width: 480px) {
  .structured-response {
    padding: var(--360t-space-3);
    gap: var(--360t-space-3);
    margin: 0;
  }

  .response-answer {
    font-size: var(--360t-text-base);
    padding: var(--360t-space-4) var(--360t-space-3);
    line-height: var(--360t-leading-normal);
  }

  .response-explanation,
  .response-sources,
  .response-entities,
  .response-followup {
    padding: var(--360t-space-3);
    margin: var(--360t-space-2) 0;
  }

  .explanation-title,
  .sources-title,
  .entities-title,
  .followup-title {
    font-size: var(--360t-text-sm);
    margin-bottom: var(--360t-space-2);
    padding-bottom: var(--360t-space-2);
  }

  .sources-grid {
    grid-template-columns: 1fr;
  }

  .followup-question-link {
    padding: var(--360t-space-2) var(--360t-space-3);
    font-size: var(--360t-text-xs);
  }

  .metadata-grid {
    grid-template-columns: 1fr;
  }

  .response-metadata {
    padding: var(--360t-space-2);
    margin-top: var(--360t-space-3);
    font-size: var(--360t-text-xs);
  }
}

/* Extreme zoom or very constrained viewports */
@media (max-width: 320px) {
  .structured-response {
    padding: var(--360t-space-2);
    gap: var(--360t-space-2);
    border-radius: var(--360t-radius-lg);
  }

  .response-answer {
    font-size: var(--360t-text-sm);
    padding: var(--360t-space-3) var(--360t-space-2);
  }

  .response-explanation,
  .response-sources,
  .response-entities,
  .response-followup {
    padding: var(--360t-space-2);
    margin: var(--360t-space-1) 0;
    border-radius: var(--360t-radius-lg);
  }

  .explanation-title,
  .sources-title,
  .entities-title,
  .followup-title {
    font-size: var(--360t-text-xs);
    margin-bottom: var(--360t-space-2);
    padding-bottom: var(--360t-space-1);
  }

  .followup-question-link {
    padding: var(--360t-space-2);
    font-size: var(--360t-text-xs);
  }

  .entity-ref-button {
    padding: var(--360t-space-1) var(--360t-space-2);
    font-size: var(--360t-text-xs);
    min-height: 32px;
  }

  .show-more-entities {
    padding: var(--360t-space-2) var(--360t-space-3);
    font-size: var(--360t-text-xs);
    min-height: 36px;
  }

  .response-metadata {
    padding: var(--360t-space-2);
    font-size: var(--360t-text-xs);
  }

  .metadata-item {
    padding: var(--360t-space-1);
    min-height: 1.5rem;
  }

  .metadata-label,
  .metadata-value {
    font-size: var(--360t-text-xs);
  }
}

/* Accessibility: Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .structured-response,
  .response-answer,
  .response-explanation,
  .response-sources,
  .response-entities,
  .response-followup,
  .entity-ref-button,
  .show-more-entities {
    transition: none;
    transform: none;
  }

  .entity-ref-button::before {
    transition: none;
  }

  .response-answer:hover,
  .response-explanation:hover,
  .response-sources:hover,
  .response-entities:hover,
  .response-followup:hover {
    transform: none;
  }

  .entity-ref-button:hover {
    transform: none;
  }
}

/* Accessibility: High Contrast Support */
@media (prefers-contrast: high) {
  .structured-response {
    border: 2px solid var(--360t-text);
    background: var(--360t-white);
  }

  .response-answer,
  .response-explanation,
  .response-sources,
  .response-entities,
  .response-followup {
    border: 2px solid var(--360t-text);
    background: var(--360t-white);
  }

  .explanation-title,
  .sources-title,
  .entities-title {
    border-bottom: 2px solid var(--360t-text);
  }

  .entity-ref-button {
    border: 2px solid var(--360t-text);
    background: var(--360t-white);
    color: var(--360t-text);
  }

  .show-more-entities {
    border: 2px solid var(--360t-text);
    background: var(--360t-white);
    color: var(--360t-text);
  }
}