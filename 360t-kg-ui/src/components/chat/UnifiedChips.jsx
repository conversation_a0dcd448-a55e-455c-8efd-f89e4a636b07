import React, { useMemo, useState } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  Chip, 
  Stack, 
  Box, 
  Typography, 
  Dialog,
  DialogTitle,
  DialogContent,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  CircularProgress
} from '@mui/material';
import { 
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import neo4jService from '../../services/neo4jService';
import { getCategoryColor, getContrastTextColor } from '../../constants/categoryColors';
import './UnifiedChips.css';

const sanitizeFactText = (text) => {
  if (!text) return '';
  return text
    .toString()
    .replace(/^relationship\s*/i, '')
    .replace(/^rel\s*/i, '')
    .trim();
};

// Enhanced Graphiti Chip Component for enriched data structure
const EnhancedGraphitiChip = ({ 
  entity, 
  onEntitySelect, 
  loadingEntity, 
  setLoadingEntity,
  setDisambiguationOptions,
  setDisambiguationOpen,
  relationship = null,
  onRelationshipSelect = null,
}) => {
  const { properties, ui_metadata } = entity;
  const {
    display_score,
    score_color,
    confidence_badge,
    category_color,
    is_clickable
  } = ui_metadata || {};

  const handleClick = async () => {
    if (relationship && onRelationshipSelect) {
      onRelationshipSelect(relationship);
      return;
    }

    if (!is_clickable || !onEntitySelect) return;
    
    try {
      setLoadingEntity(entity.id);
      
      // Use search-based resolution to find matching graph nodes
      const searchResults = await neo4jService.searchNodes(entity.name);
      
      if (searchResults?.nodes?.length === 1) {
        // Single match - open directly
        onEntitySelect({
          id: searchResults.nodes[0].id,
          name: entity.name,
          label: entity.name,
          properties: {
            ...entity.properties,
            ...searchResults.nodes[0].properties
          },
          source: 'graphiti-enhanced'
        });
      } else if (searchResults?.nodes?.length > 1) {
        // Multiple matches - show disambiguation
        setDisambiguationOptions(searchResults.nodes);
        setDisambiguationOpen(true);
      } else {
        // No matches - informative message via console (no toast system available)
        console.info(`"${entity.name}" was found in AI search but doesn't have a direct graph node to explore.`);
      }
    } catch (error) {
      console.error('Search resolution failed:', error);
      console.info(`Could not resolve "${entity.name}" in the knowledge graph.`);
    } finally {
      setLoadingEntity(null);
    }
  };

  // Get confidence icon based on level
  const getConfidenceIcon = () => {
    if (!confidence_badge) return null;
    
    switch (confidence_badge.level) {
      case 'HIGH':
        return <CheckCircleIcon className="confidence-high" sx={{ fontSize: 14 }} />;
      case 'MEDIUM':
        return <WarningIcon className="confidence-medium" sx={{ fontSize: 14 }} />;
      case 'LOW':
        return <ErrorIcon className="confidence-low" sx={{ fontSize: 14 }} />;
      default:
        return null;
    }
  };

  const isLoading = loadingEntity === entity.id;
  const isRelationshipChip = !!relationship;

  // Prepare relationship fact truncation and tooltip
  const sanitizeFactText = (text) => {
    if (!text) return '';
    return text.toString().replace(/^relationship\s*/i, '').replace(/^rel\s*/i, '').trim();
  };

  const rawFact = Array.isArray(properties?.fact)
    ? properties.fact.filter(Boolean).map(sanitizeFactText).join(' ')
    : sanitizeFactText(typeof properties?.fact === 'string' ? properties.fact : '');
  const hasFact = rawFact.length > 0;
  const truncatedFact = hasFact ? (rawFact.length > 30 ? `${rawFact.slice(0, 30)}...` : rawFact) : '';

  const shouldShowFactSuffix = hasFact && !isRelationshipChip;
  const labelText = isRelationshipChip && hasFact
    ? rawFact
    : (entity.name || 'Unknown');

  const factContent = shouldShowFactSuffix ? (
    <Tooltip title={rawFact} arrow>
      <Typography
        variant="caption"
        className="chip-fact-truncated"
        data-testid="graphiti-fact-truncated"
        data-full-fact={rawFact}
        sx={{ ml: 0.5 }}
      >
        {truncatedFact}
      </Typography>
    </Tooltip>
  ) : null;

  const chipElement = (
    <Chip
      variant="outlined"
      className={`unified-chip enhanced-chip ${score_color || 'score-gray'} ${isLoading ? 'loading' : ''} ${isRelationshipChip ? 'relationship-chip' : ''}`}
      onClick={handleClick}
      clickable={isRelationshipChip || is_clickable}
      disabled={isLoading}
      label={
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          {/* Loading indicator */}
          {isLoading && (
            <CircularProgress size={12} sx={{ mr: 0.5 }} />
          )}

          {/* Node/relationship type name */}
          <Typography variant="body2" noWrap sx={{ maxWidth: '26ch' }}>
            {labelText}
          </Typography>

          {/* Truncated relationship fact (Graphiti edges) */}
          {factContent}

          {/* Score badge removed per requirement */}
          {/* intentionally not rendering display_score */}

          {/* Confidence badge */}
          {getConfidenceIcon()}

          {/* Business category dot */}
          {properties?.business_category && (
            <Box
              sx={{
                width: 6,
                height: 6,
                borderRadius: '50%',
                backgroundColor: category_color || getCategoryColor(properties.business_category)
              }}
            />
          )}
        </Box>
      }
      title={hasFact ? undefined : `${entity.name}: ${properties?.summary?.substring(0, 100)}...${properties?.summary?.length > 100 ? '...' : ''} (${display_score || 'N/A'}% relevance, ${properties?.confidence_level || 'Unknown'} confidence)`}
    />
  );

  const relationshipTooltip = isRelationshipChip && hasFact ? rawFact : undefined;

  if (relationshipTooltip) {
    return (
      <Tooltip title={relationshipTooltip} arrow enterDelay={150} leaveDelay={100}>
        <span className="enhanced-chip-tooltip-wrapper">
          {chipElement}
        </span>
      </Tooltip>
    );
  }

  return chipElement;
};

/**
 * UnifiedChips
 * - Enhanced to support enriched Graphiti JSON data with scores, confidence levels, and visual metadata
 * - Merges entities + facts (additional details) + edges into compact chips
 * - Hover: shows summary/fact with enhanced tooltips
 * - Click: opens Node Details via onEntitySelect when resolvable with search-based resolution
 */
export default function UnifiedChips({
  entities = [],
  facts = [],
  edges = [],
  onEntitySelect,
  onRelationshipSelect,
  maxVisible = 30,
  expand = false,
}) {
  // State for enhanced functionality
  const [loadingEntity, setLoadingEntity] = useState(null);
  const [disambiguationOpen, setDisambiguationOpen] = useState(false);
  const [disambiguationOptions, setDisambiguationOptions] = useState([]);

  // Check if entities have enhanced structure
  const hasEnhancedData = useMemo(() => {
    return entities.some(entity => entity.ui_metadata);
  }, [entities]);
  // Build lookups for resolving facts/edges to entities
  const { byId, byName } = useMemo(() => {
    const idMap = new Map();
    const nameMap = new Map();
    for (const e of entities) {
      if (!e) continue;
      const id = String(e.id || e.node_id || e.uid || '') || undefined;
      const name = String(e.name || e.title || e.label || '').trim();
      const entity = {
        id: id || name || `entity_${idMap.size}`,
        name: name || id || 'Entity',
        description: e.description || e.summary || '',
        properties: e.properties || {},
        source: e.source || 'unified',
        raw: e,
      };
      if (entity.id) idMap.set(entity.id, entity);
      if (entity.name) nameMap.set(entity.name.toLowerCase(), entity);
    }
    return { byId: idMap, byName: nameMap };
  }, [entities]);

  const entityChips = useMemo(() => {
    return entities.map((e, idx) => {
      const id = e?.id || e?.node_id || `entity_${idx}`;
      const name = e?.name || e?.title || id;
      const score = typeof e?.relevance_score === 'number' ? Math.round(e.relevance_score * 100) : null;
      return {
        type: 'entity',
        id,
        label: name,
        score,
        preview: e?.description || e?.summary || '',
        linkedEntity: { id, name, description: e?.description, properties: e?.properties || {} },
      };
    });
  }, [entities]);

  const factChips = useMemo(() => {
    const chips = [];
    for (const f of facts || []) {
      const title = f?.source?.title || f?.title || 'Detail';
      const text = f?.content || f?.summary || f?.snippet || '';
      const entityId = f?.source?.id || f?.entity_id;
      let linked = null;
      if (entityId && byId.has(String(entityId))) linked = byId.get(String(entityId));
      else if (title && byName.has(String(title).toLowerCase())) linked = byName.get(String(title).toLowerCase());
      chips.push({ type: 'fact', id: `fact_${chips.length}`, label: title, preview: text, linkedEntity: linked });
    }
    return chips;
  }, [facts, byId, byName]);

  const edgeChips = useMemo(() => {
    const out = [];
    for (let i = 0; i < (edges?.length || 0); i++) {
      const e = edges[i];
      const subj = e?.source || e?.from || e?.subject || {};
      const obj = e?.target || e?.to || e?.object || {};
      const rel = e?.type || e?.relation || e?.label || 'REL';
      const preview = `${subj?.name || subj?.id || 'A'} — ${rel} → ${obj?.name || obj?.id || 'B'}`;
      const linkOf = (node) => {
        if (!node) return null;
        const id = node.id != null ? String(node.id) : undefined;
        const nm = node.name ? node.name.toLowerCase() : undefined;
        if (id && byId.has(id)) return byId.get(id);
        if (nm && byName.has(nm)) return byName.get(nm);
        return null;
      };
      const normalizeNodeRef = (nodeRef) => {
        if (!nodeRef) return null;

        const seededMatch = linkOf(nodeRef);

        const rawId = nodeRef.id ?? nodeRef.identity ?? nodeRef.node_id ?? nodeRef.uid ?? nodeRef.properties?.id;
        const normalizedId = rawId != null ? String(rawId) : seededMatch?.id;

        const name =
          nodeRef?.properties?.name ||
          nodeRef?.name ||
          nodeRef?.label ||
          seededMatch?.name ||
          normalizedId ||
          'Unknown';

        const mergedProps = {
          ...(seededMatch?.properties || {}),
          ...(nodeRef?.properties || {}),
        };

        if (!mergedProps.name) mergedProps.name = name;

        const businessCategory =
          mergedProps.business_category ||
          mergedProps.category ||
          seededMatch?.properties?.business_category;
        if (businessCategory && !mergedProps.category) {
          mergedProps.category = businessCategory;
        }

        // TODO(karen): Re-validate that normalized relationship node badges match Explorer after chat multi-match selection.
        return {
          id: normalizedId || name,
          name,
          label: name,
          properties: mergedProps,
          raw: nodeRef,
        };
      };

      const sourceNode = normalizeNodeRef(subj);
      const targetNode = normalizeNodeRef(obj);
      out.push({
        type: 'edge',
        id: e?.id || `edge_${i}`,
        label: rel,
        preview,
        linkedEntity: sourceNode || targetNode,
        relationship: {
          ...e,
          sourceNode,
          targetNode,
        },
      });
    }
    return out;
  }, [edges, byId, byName]);

  const chips = useMemo(() => {
    const merged = [...entityChips, ...edgeChips, ...factChips];
    return expand ? merged : merged.slice(0, maxVisible);
  }, [entityChips, edgeChips, factChips, expand, maxVisible]);

  const handleClick = (chip) => {
    if (chip.type === 'edge' && typeof onRelationshipSelect === 'function' && chip.relationship) {
      const relationshipData = chip.relationship;
      const sourceNode = relationshipData.sourceNode || null;
      const targetNode = relationshipData.targetNode || null;
      const normalizedRelationship = {
        id: relationshipData.id || chip.id,
        type: relationshipData.type || relationshipData.label,
        label: relationshipData.label || relationshipData.type,
        properties: {
          ...(relationshipData.properties || relationshipData.data?.properties || {}),
        },
        fact: relationshipData.fact || relationshipData.properties?.fact || null,
        source: sourceNode?.id || relationshipData.source?.id || relationshipData.from?.id || relationshipData.source,
        target: targetNode?.id || relationshipData.target?.id || relationshipData.to?.id || relationshipData.target,
        sourceNode,
        targetNode,
        direction: relationshipData.direction || relationshipData.data?.direction || relationshipData.properties?.direction || null,
        raw: relationshipData,
      };
      onRelationshipSelect(normalizedRelationship);
      return;
    }

    if (chip.linkedEntity && typeof onEntitySelect === 'function') {
      onEntitySelect({
        id: chip.linkedEntity.id,
        name: chip.linkedEntity.name,
        label: chip.linkedEntity.name,
        title: chip.linkedEntity.name,
        description: chip.linkedEntity.description || chip.preview || '',
        properties: chip.linkedEntity.properties || {},
        source: 'unified-chips',
      });
    }
  };

  // Handler for disambiguation dialog selection
  const normalizeDisambiguationNode = (option) => {
    if (!option) return null;

    const rawId = option.id ?? option.identity ?? option.node_id ?? option.uid ?? option.properties?.id;
    const normalizedId = rawId != null ? String(rawId) : undefined;

    const name =
      option?.properties?.name ||
      option?.name ||
      option?.label ||
      option?.properties?.title ||
      (normalizedId ? `Node ${normalizedId}` : 'Unknown node');

    const description =
      option?.properties?.description ||
      option?.description ||
      option?.properties?.summary ||
      '';

    const category =
      option?.properties?.business_category ||
      option?.properties?.category ||
      option?.category ||
      (Array.isArray(option?.labels) ? option.labels[0] : undefined);

    const properties = {
      ...(option?.properties || {}),
    };

    if (category && !properties.category) {
      properties.category = category;
    }

    return {
      id: normalizedId || name,
      name,
      label: name,
      title: name,
      description,
      category,
      properties,
      source: 'graphiti-disambiguation',
      raw: option
    };
  };

const handleDisambiguationSelect = (selectedNode) => {
  const normalized = normalizeDisambiguationNode(selectedNode);
  if (!normalized) return;

  if (onEntitySelect) {
    onEntitySelect(normalized);
  }

  setDisambiguationOpen(false);
  setDisambiguationOptions([]);
  };

  // Enhanced rendering for enriched Graphiti data
  if (hasEnhancedData) {
    // Sort entities by relevance score (highest first)
    const sortedEntities = [...entities].sort((a, b) => {
      const scoreA = a.ui_metadata?.display_score || 0;
      const scoreB = b.ui_metadata?.display_score || 0;
      return scoreB - scoreA;
    });

    // Group entities by business category
    const entitiesByCategory = sortedEntities.reduce((acc, entity) => {
      const category = entity.properties?.business_category || 'Nodes';
      if (!acc[category]) acc[category] = [];
      acc[category].push(entity);
      return acc;
    }, {});

    // Sort edges by relevance score if available
    const sortedEdges = [...edges].sort((a, b) => {
      const scoreA = a.ui_metadata?.display_score || 0;
      const scoreB = b.ui_metadata?.display_score || 0;
      return scoreB - scoreA;
    });

    // Limit visible items if not expanded
    const displayCategories = expand 
      ? Object.entries(entitiesByCategory)
      : Object.entries(entitiesByCategory).slice(0, 3); // Show top 3 categories
    
    const displayEdges = expand
      ? sortedEdges
      : sortedEdges.slice(0, Math.max(5, maxVisible - sortedEntities.length));

    const cleanEdgeLabel = (label) => {
      if (!label) return '';
      return label.replace(/^RELATIONSHIP\s*/i, '').replace(/^REL\s*/i, '').trim();
    };

    return (
      <Box className="enhanced-graphiti-related">
        {/* Render entities by category */}
        {displayCategories.map(([category, categoryEntities]) => {
          const displayCategory = category === 'Nodes' ? 'Nodes' : category;
          return (
          <Box key={category} className="category-group" sx={{ mb: 2 }}>
            <Typography variant="subtitle2" className="category-group-header" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box
                className="category-indicator"
                sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  backgroundColor: getCategoryColor(category)
                }}
              />
              {displayCategory}
            </Typography>
            <Stack direction="row" spacing={1} flexWrap="wrap">
              {(expand ? categoryEntities : categoryEntities.slice(0, 5)).map((entity, index) => (
                <EnhancedGraphitiChip
                  key={`${category}-entity-${index}`}
                  entity={entity}
                  onEntitySelect={onEntitySelect}
                  loadingEntity={loadingEntity}
                  setLoadingEntity={setLoadingEntity}
                  setDisambiguationOptions={setDisambiguationOptions}
                  setDisambiguationOpen={setDisambiguationOpen}
                />
              ))}
            </Stack>
          </Box>
        );
        })}
        
        {/* Render edges separately if available */}
        {displayEdges.length > 0 && (
          <Box className="category-group" sx={{ mb: 2 }}>
            <Typography variant="subtitle2" className="category-group-header" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box
                className="category-indicator"
                sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  backgroundColor: '#8b7355'
                }}
              />
              Relationships
            </Typography>
            <Stack direction="row" spacing={1} flexWrap="wrap">
              {displayEdges.map((edge, index) => {
                // Merge possible property locations and normalize fact
                const mergedProps = {
                  ...(edge.data?.properties || {}),
                  ...(edge.properties || {}),
                };
                const rawFactValue = mergedProps?.fact || edge.data?.fact || edge.fact || null;
                const normalizedFact = sanitizeFactText(rawFactValue);
                const edgeLabelRaw = edge.type || edge.relation || edge.label || '';
                const edgeLabel = cleanEdgeLabel(edgeLabelRaw);
                const displayName = typeof normalizedFact === 'string' && normalizedFact.trim().length > 0
                  ? normalizedFact.trim()
                  : (edgeLabel || `${edge.source?.name || 'A'} → ${edge.target?.name || 'B'}`);
                const properties = {
                  summary: `${edge.source?.name || 'A'} → ${edge.target?.name || 'B'}`,
                  ...mergedProps,
                  ...(normalizedFact ? { fact: normalizedFact } : {}),
                };
                const relationshipPayload = {
                  id: edge.id || `edge_${index}`,
                  type: edgeLabel || edgeLabelRaw || 'RELATIONSHIP',
                  label: edgeLabel,
                  source: edge.source?.id || edge.from?.id || edge.source?.name,
                  target: edge.target?.id || edge.to?.id || edge.target?.name,
                  sourceNode: edge.source || edge.from || null,
                  targetNode: edge.target || edge.to || null,
                  properties,
                  fact: normalizedFact || rawFactValue || null,
                  direction: edge.direction || edge.data?.direction || edge.properties?.direction || null,
                  raw: edge,
                };
                return (
                  <EnhancedGraphitiChip
                    key={`edge-${index}`}
                    entity={{
                      id: edge.id || `edge_${index}`,
                      name: displayName,
                      properties,
                      ui_metadata: {
                        ...edge.ui_metadata,
                        is_clickable: true
                      }
                    }}
                    onEntitySelect={null}
                    loadingEntity={loadingEntity}
                    setLoadingEntity={setLoadingEntity}
                    setDisambiguationOptions={setDisambiguationOptions}
                    setDisambiguationOpen={setDisambiguationOpen}
                    relationship={relationshipPayload}
                    onRelationshipSelect={onRelationshipSelect}
                  />
                );
              })}
            </Stack>
          </Box>
        )}

        {/* Disambiguation Dialog */}
        <Dialog
          open={disambiguationOpen}
          onClose={() => setDisambiguationOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Multiple matches found</DialogTitle>
          <DialogContent>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Found multiple graph nodes with this name. Please select the one you want to explore:
            </Typography>
            <List>
              {disambiguationOptions.map((option, index) => {
                const optionName = option?.properties?.name || option?.name || option?.label || `Node ${index + 1}`;
                const optionDescription = option?.properties?.description || option?.description || option?.properties?.summary || '';
                const optionCategory = option?.properties?.business_category || option?.properties?.category || option?.category || (Array.isArray(option?.labels) ? option.labels[0] : null);
                
                return (
                  <ListItem key={index} disablePadding divider>
                    <ListItemButton
                      onClick={() => handleDisambiguationSelect(option)}
                      sx={{ alignItems: 'flex-start', py: 1.5 }}
                    >
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.75, width: '100%' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', gap: 1 }}>
                          <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                            {optionName}
                          </Typography>
                          {optionCategory && (
                            <Chip
                              size="small"
                              label={optionCategory}
                              sx={{
                                ml: 1,
                                fontWeight: 600,
                                letterSpacing: 0.3,
                                textTransform: 'uppercase',
                                backgroundColor: getCategoryColor(optionCategory),
                                color: getContrastTextColor(getCategoryColor(optionCategory))
                              }}
                            />
                          )}
                        </Box>

                        {optionDescription && (
                          <Typography variant="body2" color="text.secondary">
                            {optionDescription}
                          </Typography>
                        )}

                        <Typography variant="caption" color="text.secondary">
                          ID: {option.id}
                        </Typography>
                      </Box>
                    </ListItemButton>
                  </ListItem>
                );
              })}
            </List>
          </DialogContent>
        </Dialog>
      </Box>
    );
  }

  // Fallback to existing minimal display for non-enhanced data
  return (
    <Stack direction="row" spacing={0.75} flexWrap="wrap" className="unified-chips" role="list" aria-label="Related information">
      {chips.map((chip) => {
        const chipNode = (
          <Chip
            key={`${chip.type}-${chip.id}`}
            size="small"
            variant="outlined"
            className={`unified-chip ${chip.type}`}
            onClick={() => handleClick(chip)}
            label={(
              <span className="chip-label-wrap">
                <span className="chip-label">{chip.label}</span>
              </span>
            )}
          />
        );

        return chip.preview ? (
          <Tooltip key={`${chip.type}-${chip.id}`} title={<span style={{ whiteSpace: 'pre-line' }}>{chip.preview}</span>} arrow>
            {chipNode}
          </Tooltip>
        ) : chipNode;
      })}
    </Stack>
  );
}
