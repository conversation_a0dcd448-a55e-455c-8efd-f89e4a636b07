import React from 'react';
import '../../styles/ChatDisclaimer.css';

/**
 * ChatDisclaimer
 * Accessible banner to communicate AI limitations.
 * Variants: "info" (default) and "warning" (for validation/fallback resurfacing)
 */
export default function ChatDisclaimer({ variant = 'info', onDismiss, onNeverShow }) {
  const isWarning = variant === 'warning';
  return (
    <div
      className={`chat-disclaimer ${isWarning ? 'warning' : 'info'}`}
      role="region"
      aria-label="AI assistance disclaimer"
      aria-live={isWarning ? 'polite' : undefined}
    >
      <div className="chat-disclaimer-icon" aria-hidden="true">
        {isWarning ? (
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M10.29 3.86L1.82 18a2 2 0 001.71 3h16.94a2 2 0 001.71-3L13.71 3.86a2 2 0 00-3.42 0z" />
            <line x1="12" y1="9" x2="12" y2="13" />
            <line x1="12" y1="17" x2="12.01" y2="17" />
          </svg>
        ) : (
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10" />
            <line x1="12" y1="16" x2="12" y2="12" />
            <line x1="12" y1="8" x2="12.01" y2="8" />
          </svg>
        )}
      </div>
      <div className="chat-disclaimer-content">
        <p className="chat-disclaimer-text">
          AI Assist: Responses are generated from 360T user guides and related documentation. While we strive for accuracy, AI may be incomplete or incorrect. Do not rely on this output for trading, operational, or safety‑critical decisions—always consult the official 360T user guides.
        </p>
      </div>
      <div className="chat-disclaimer-actions">
        <button
          type="button"
          className="chat-disclaimer-btn"
          onClick={onDismiss}
          aria-label="Dismiss disclaimer"
        >
          Dismiss
        </button>
        <button
          type="button"
          className="chat-disclaimer-btn strong"
          onClick={onNeverShow}
          aria-label="Do not show this disclaimer again"
        >
          Don’t show again
        </button>
      </div>
    </div>
  );
}

