.entity-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.entity-chip {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  max-width: 100%;
  padding: 6px 10px;
  font-size: 0.8125rem;
  line-height: 1;
  color: #1f2937;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 999px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.entity-chip:hover {
  border-color: #d1d5db;
  box-shadow: 0 1px 3px rgba(0,0,0,0.08);
}

.chip-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chip-score {
  color: var(--360t-primary, #00973A);
  font-weight: 600;
  font-size: 0.75rem;
}

@media (max-width: 480px) {
  .entity-chip { padding: 5px 8px; font-size: 0.75rem; }
}

