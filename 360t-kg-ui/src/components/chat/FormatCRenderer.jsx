import React, { useState, useMemo } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Chip,
  Collapse
} from '@mui/material';
import {
  Source as SourceIcon
} from '@mui/icons-material';
import RichTextRenderer from './RichTextRenderer';
// import EntityList from './EntityList';
import UnifiedChips from './UnifiedChips';
import RelatedSectionPill from './RelatedSectionPill';
import './FormatCRenderer.css';
import AnswerCard from './AnswerCard';

/**
 * FormatCRenderer - Native renderer for Format C responses
 * 
 * Renders Format C responses directly without normalization to v2.0 structure.
 * This eliminates content duplication issues caused by the normalization process.
 * 
 * Format C Structure:
 * - version: "3.0"
 * - format: "C"
 * - core: { summary, results, follow_up_questions, confidence_score, ... }
 * - extensions: { atlas_rag, graphiti, ... }
 * 
 * @param {Object} response - Format C response object
 * @param {Function} onNodeSelect - Callback for node selection
 * @param {Function} onSendMessage - Callback for sending follow-up messages
 * @param {boolean} showDebugInfo - Show debug/metadata sections
 * @param {boolean} isStreaming - Indicates if response is still streaming
 */
const FormatCRenderer = ({
  response,
  onNodeSelect,
  onRelationshipSelect,
  onSendMessage,
  showDebugInfo = false,
  isStreaming = false
}) => {
  const [expandedSections, setExpandedSections] = useState({
    results: false,      // Collapsed by default
    metadata: false      // Collapsed by default (secondary)
  });
  const [showAllEntities, setShowAllEntities] = useState(false);
  const [showMeta, setShowMeta] = useState(false);

  // Validate Format C structure
  const isValidFormatC = useMemo(() => {
    return (
      response &&
      response.version === '3.0' &&
      response.format === 'C' &&
      response.core &&
      typeof response.core.summary === 'string'
    );
  }, [response]);

  // Extract core data
  const {
    summary = '',
    results = [],
    confidence_score,
    processing_time_ms
  } = response?.core || {};


  // Extract extensions data
  const { atlas_rag, graphiti } = response?.extensions || {};
  const isGraphiti = !!graphiti;
  const isAtlasOnly = !!atlas_rag && !graphiti;

  // Extract entities from extensions
  const entities = useMemo(() => {
    const allEntities = [];
    
    // Get entities from Atlas RAG
    if (atlas_rag?.entities) {
      atlas_rag.entities.forEach((entity, index) => {
        allEntities.push({
          id: entity.id || `atlas_entity_${index}`,
          name: entity.name || entity.id || `Entity ${index + 1}`,
          category: entity.category || 'Unknown',
          description: entity.description || '',
          relevance_score: entity.relevance_score || 0.5,
          source: 'atlas_rag'
        });
      });
    }
    
    // Get entities from Graphiti (handle both arrays and counts)
    if (graphiti?.nodes) {
      // Check if nodes is an array (expected) or a number (actual from backend)
      if (Array.isArray(graphiti.nodes)) {
        graphiti.nodes.forEach((node, index) => {
          // Preserve enhanced structure if available, otherwise use legacy format
          if (node.ui_metadata) {
            // Enhanced structure - preserve as-is for UnifiedChips to handle
            allEntities.push({
              ...node, // Preserve entire enhanced structure
              source: 'graphiti'
            });
          } else {
            // Legacy structure - convert to old format
            allEntities.push({
              id: node.id || `graphiti_node_${index}`,
              name: node.properties?.name || node.id || `Node ${index + 1}`,
              category: node.properties?.category || 'Unknown',
              description: node.properties?.description || '',
              relevance_score: 0.5, // Default for graphiti nodes
              properties: node.properties || {},
              source: 'graphiti'
            });
          }
        });
      }
      // If nodes is a number (count), we can't iterate but we know there are entities
      // This is a defensive measure until backend is fixed to always send arrays
    }
    
    return allEntities;
  }, [atlas_rag, graphiti]);

  // Handle accordion expansion
  const handleSectionToggle = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Handle entity selection
  const handleEntitySelect = (entity) => {
    if (!onNodeSelect || !entity) return;

    const nodeData = {
      id: entity.id,
      name: entity.name,
      label: entity.name,
      title: entity.name,
      description: entity.description || 'No description available',
      category: entity.category,
      properties: entity.properties || {},
      relevance_score: entity.relevance_score,
      type: entity.category?.toLowerCase() || 'entity',
      source: 'format_c_renderer'
    };

    onNodeSelect(nodeData);
  };

  // Render error state for invalid Format C
  if (!isValidFormatC) {
    return (
      <Card variant="outlined" sx={{ mt: 2, borderColor: 'error.main' }}>
        <CardContent>
          <Typography variant="h6" color="error.main" gutterBottom>
            Invalid Format C Response
          </Typography>
          <Typography variant="body2" color="text.secondary">
            The response does not match the expected Format C structure (version: 3.0, format: C).
          </Typography>
          {showDebugInfo && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="caption" display="block">
                Debug Info:
              </Typography>
              <pre style={{ fontSize: '0.75rem', overflow: 'auto' }}>
                {JSON.stringify(response, null, 2)}
              </pre>
            </Box>
          )}
        </CardContent>
      </Card>
    );
  }

  // Use summary directly - no markdown cleaning needed since AI systems generate JSON directly

  // Limit visible entities initially
  const visibleEntities = showAllEntities ? entities : entities.slice(0, 5);
  const graphitiEntities = useMemo(() => entities.filter(e => e.source === 'graphiti'), [entities]);
  const graphitiEdges = useMemo(() => Array.isArray(graphiti?.edges) ? graphiti.edges : [], [graphiti]);

  return (
    <Box className="format-c-response" data-testid="format-c-renderer">
      {/* Main Answer */}
      <Box className="format-c-summary" data-testid="message-summary" sx={{ position: 'relative' }}>
        <AnswerCard
          confidence={confidence_score}
          timeMs={processing_time_ms}
          onToggleMeta={showDebugInfo ? () => setShowMeta(v => !v) : undefined}
          metaOpen={showMeta}
        >
          <RichTextRenderer
            content={summary}
            sectionType="summary"
            className="format-c-summary-content"
          />
        </AnswerCard>

        {/* Related Section Pill - positioned at bottom left of answer card */}
        {isGraphiti && (graphitiEntities.length > 0 || results.length > 0 || graphitiEdges.length > 0) && (
          <RelatedSectionPill
            entities={graphitiEntities}
            facts={results}
            edges={graphitiEdges}
            onEntitySelect={handleEntitySelect}
            onRelationshipSelect={onRelationshipSelect}
            showAllEntities={showAllEntities}
            onShowAllToggle={() => setShowAllEntities(v => !v)}
            disabled={false}
          />
        )}
      </Box>

      {/* Inline metadata panel toggled from the answer's bottom-right icon */}
      {showDebugInfo && (
        <Collapse in={showMeta} timeout="auto">
          <Box className="metadata-panel" sx={{ mt: 1 }}>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 1 }}>
              <Chip size="small" label={`Format: ${response.format}`} />
              <Chip size="small" label={`Version: ${response.version}`} />
              {confidence_score != null && (
                <Chip size="small" label={`Confidence: ${Math.round(confidence_score * 100)}%`} />
              )}
              {processing_time_ms != null && (
                <Chip size="small" label={`Processing: ${processing_time_ms}ms`} />
              )}
            </Box>

            {atlas_rag && (
              <Box sx={{ mb: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.secondary' }}>Atlas RAG</Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {atlas_rag.pagerank_scores?.length > 0 && (
                    <Chip size="small" label={`PageRank Entities: ${atlas_rag.pagerank_scores.length}`} />
                  )}
                  {atlas_rag.entities?.length > 0 && (
                    <Chip size="small" label={`RAG Entities: ${atlas_rag.entities.length}`} />
                  )}
                </Box>
              </Box>
            )}

            {graphiti && (
              <Box sx={{ mb: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.secondary' }}>Graphiti</Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {graphiti.nodes != null && (
                    <Chip size="small" label={`Nodes: ${Array.isArray(graphiti.nodes) ? graphiti.nodes.length : graphiti.nodes}`} />
                  )}
                  {graphiti.edges != null && (
                    <Chip size="small" label={`Edges: ${Array.isArray(graphiti.edges) ? graphiti.edges.length : graphiti.edges}`} />
                  )}
                  {graphiti.search_algorithm && (
                    <Chip size="small" label={graphiti.search_algorithm} variant="outlined" />
                  )}
                  {graphiti.search_time_ms && (
                    <Chip size="small" label={`${graphiti.search_time_ms}ms`} variant="outlined" />
                  )}
                </Box>
              </Box>
            )}

            <Box sx={{ mt: 1, fontSize: '0.75rem', maxHeight: 220, overflow: 'auto' }}>
              <pre style={{ margin: 0 }}>{JSON.stringify(response, null, 2)}</pre>
            </Box>
          </Box>
        </Collapse>
      )}

      {/* Follow-up Questions moved to composer suggestions in ChatView */}

      {/* Collapsible Results Section: hide for Atlas-only; for Graphiti we present chips instead */}
      {!isGraphiti && !isAtlasOnly && results.length > 0 && (
        <Box sx={{ mt: 2 }}>
          <Accordion 
            expanded={expandedSections.results}
            onChange={() => handleSectionToggle('results')}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <SourceIcon fontSize="small" />
                <Typography variant="subtitle1">
                  Additional Details ({results.length} sections)
                </Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {results.map((result, index) => (
                  <Card key={`result-${index}`} variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        {result.source?.title || `Section ${index + 1}`}
                      </Typography>
                      <RichTextRenderer
                        content={result.content || ''}
                        sectionType="result"
                        className="result-content"
                      />
                    </CardContent>
                  </Card>
                ))}
              </Box>
            </AccordionDetails>
          </Accordion>
        </Box>
      )}


      {/* AtlasRAG only: keep it minimal, no entities/facts lists */}

    </Box>
  );
};

export default FormatCRenderer;
