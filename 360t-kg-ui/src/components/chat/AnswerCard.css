.answer-card {
  position: relative;
  background: var(--360t-white, #ffffff);
  border: 1px solid var(--360t-mid-gray, #e5e7eb);
  border-left: 4px solid var(--360t-primary, #00973A);
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  padding: clamp(0.75rem, 0.6rem + 0.4vw, 1.25rem);
}

.answer-content {
  font-size: var(--360t-text-base, 1rem);
  line-height: var(--360t-leading-relaxed, 1.625);
  color: var(--360t-text, #1f2937);
}

.answer-content p { margin: 0 0 12px 0; }
.answer-content p:last-child { margin-bottom: 0; }

.answer-meta {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  margin-top: 0.5rem;
  font-size: var(--360t-text-xs, 0.75rem);
  color: var(--360t-dark-gray, #6b7280);
}

.meta-chip {
  border: 1px solid var(--360t-mid-gray, #e5e7eb);
  background: var(--360t-gray-50, #f8fafc);
  color: var(--360t-primary, #00973A);
  padding: 2px 6px;
  border-radius: 999px;
  font-weight: 600;
}

.meta-time { opacity: 0.8; }

/* Bottom-right discrete metadata toggle */
.meta-toggle-btn {
  position: absolute;
  right: 8px;
  bottom: 8px;
  width: 28px;
  height: 28px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 999px;
  border: 1px solid var(--360t-mid-gray, #e5e7eb);
  background: var(--360t-gray-50, #f8fafc);
  color: var(--360t-dark-gray, #6b7280);
  cursor: pointer;
  transition: all 0.2s ease;
}

.meta-toggle-btn:hover {
  background: #ffffff;
  border-color: #d1d5db;
  color: var(--360t-primary, #00973A);
  box-shadow: 0 2px 6px rgba(0,0,0,0.06);
}

.meta-toggle-btn.open {
  color: var(--360t-primary, #00973A);
}

