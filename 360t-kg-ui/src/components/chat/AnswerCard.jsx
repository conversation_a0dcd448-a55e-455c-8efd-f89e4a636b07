import React from 'react';
import './AnswerCard.css';

/**
 * AnswerCard
 * A clean, focused container for the assistant's main answer.
 * - Subtle left accent in brand green
 * - Neutral white background, generous spacing
 * - Tiny, unobtrusive meta row for confidence/time
 */
export default function AnswerCard({ children, confidence, timeMs, onToggleMeta, metaOpen }) {
  const hasMeta = typeof confidence === 'number' || typeof timeMs === 'number';

  return (
    <section className="answer-card" aria-label="Assistant answer">
      <div className="answer-content">
        {children}
      </div>
      {typeof onToggleMeta === 'function' && (
        <button
          type="button"
          className={`meta-toggle-btn ${metaOpen ? 'open' : ''}`}
          onClick={onToggleMeta}
          aria-pressed={!!metaOpen}
          aria-label={metaOpen ? 'Hide metadata' : 'Show metadata'}
          title={metaOpen ? 'Hide metadata' : 'Show metadata'}
        >
          <svg viewBox="0 0 24 24" width="16" height="16" aria-hidden="true">
            <path fill="currentColor" d="M11 7h2V9h-2V7Zm0 4h2v6h-2v-6Zm1-9a10 10 0 1 0 10 10A10.011 10.011 0 0 0 12 2Zm0 18a8 8 0 1 1 8-8 8.009 8.009 0 0 1-8 8Z"/>
          </svg>
        </button>
      )}
      {hasMeta && (
        <div className="answer-meta">
          {typeof confidence === 'number' && (
            <span className="meta-chip" aria-label="confidence score">
              {Math.round(confidence * 100)}% confidence
            </span>
          )}
          {typeof timeMs === 'number' && (
            <span className="meta-time" aria-label="processing time">{timeMs}ms</span>
          )}
        </div>
      )}
    </section>
  );
}
