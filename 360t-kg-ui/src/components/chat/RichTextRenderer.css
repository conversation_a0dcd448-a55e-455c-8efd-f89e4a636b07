/* RichTextRenderer component styles */
.rich-text-renderer {
  line-height: 1.6;
  color: #374151;
  font-size: inherit; /* Inherit from parent to respect FormatCRenderer sizing */
}

/* Answer section specific styling */
.rich-text-renderer.answer {
  font-size: inherit; /* Inherit from parent for proper scaling */
  font-weight: 400;
  color: #111827;
}

/* Summary section specific styling (used in FormatCRenderer) */
.rich-text-renderer.summary {
  font-size: inherit; /* Inherit from parent FormatCRenderer sizing */
  font-weight: 400;
  color: #111827;
}

/* Explanation section specific styling */
.rich-text-renderer.explanation {
  font-size: 0.95rem;
  color: #374151;
}

/* Enhanced paragraph styling */
.rich-text-renderer p {
  margin: 0 0 1rem 0;
  line-height: 1.7;
}

.rich-text-renderer p:last-child {
  margin-bottom: 0;
}

/* Enhanced emphasis styling - CRITICAL: Fixed visibility issues */
.rich-text-renderer strong {
  font-weight: 700 !important; /* Increased from 600 to 700 for better visibility */
  color: #111827 !important; /* Dark color with !important to override parent styles */
  display: inline !important; /* Ensure inline display is preserved */
}

.rich-text-renderer em {
  font-style: italic !important; /* Force italic style */
  color: #059669 !important; /* Green color for emphasis */
  font-weight: 500 !important; /* Semi-bold for better contrast */
  display: inline !important; /* Ensure inline display is preserved */
}

/* Additional formatting fixes - ensure all emphasis is visible */
.rich-text-renderer b {
  font-weight: 700 !important; /* Handle <b> tags */
  color: #111827 !important;
  display: inline !important;
}

.rich-text-renderer i {
  font-style: italic !important; /* Handle <i> tags */
  color: #059669 !important;
  font-weight: 500 !important;
  display: inline !important;
}

/* Enhanced list styling */
.rich-text-renderer ul,
.rich-text-renderer ol {
  margin: 0.75rem 0;
  padding-left: 1.5rem;
}

.rich-text-renderer li {
  margin: 0.5rem 0;
  line-height: 1.6;
}

.rich-text-renderer ul li {
  list-style-type: none;
  position: relative;
}

.rich-text-renderer ul li::before {
  content: '•';
  color: #3b82f6;
  font-weight: bold;
  position: absolute;
  left: -1rem;
  font-size: 1.1em;
}

.rich-text-renderer ol li {
  list-style-type: decimal;
  color: #374151;
}

.rich-text-renderer ol li::marker {
  color: #3b82f6;
  font-weight: 600;
}

/* Enhanced headings */
.rich-text-renderer h1,
.rich-text-renderer h2,
.rich-text-renderer h3,
.rich-text-renderer h4,
.rich-text-renderer h5,
.rich-text-renderer h6 {
  margin: 1.5rem 0 0.75rem 0;
  font-weight: 600;
  color: #111827;
  line-height: 1.3;
}

.rich-text-renderer h1 { font-size: 1.5rem; }
.rich-text-renderer h2 { font-size: 1.3rem; }
.rich-text-renderer h3 { font-size: 1.1rem; }
.rich-text-renderer h4 { font-size: 1rem; }

/* Code styling */
.rich-text-renderer code {
  background: #f3f4f6;
  color: #374151;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.875em;
  border: 1px solid #e5e7eb;
}

.rich-text-renderer pre {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1rem 0;
  overflow-x: auto;
  font-family: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.rich-text-renderer pre code {
  background: none;
  border: none;
  padding: 0;
  font-size: inherit;
}

/* Blockquote styling */
.rich-text-renderer blockquote {
  border-left: 4px solid #3b82f6;
  margin: 1rem 0;
  padding: 0.5rem 0 0.5rem 1rem;
  background: #f8fafc;
  border-radius: 0 0.25rem 0.25rem 0;
  font-style: italic;
  color: #475569;
}

/* Links */
.rich-text-renderer a {
  color: #3b82f6;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
}

.rich-text-renderer a:hover {
  color: #1d4ed8;
  border-bottom-color: #3b82f6;
}

/* Horizontal rules */
.rich-text-renderer hr {
  border: none;
  height: 1px;
  background: linear-gradient(90deg, transparent, #d1d5db, transparent);
  margin: 1.5rem 0;
}

/* Answer section specific enhancements */
.rich-text-renderer.answer p:first-child {
  font-size: 1.05rem;
  font-weight: 500;
  color: #111827;
}

/* Explanation section specific enhancements */
.rich-text-renderer.explanation {
  background: #f8fafc;
  border-radius: 0.5rem;
  padding: 1rem;
  border-left: 4px solid #3b82f6;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .rich-text-renderer {
    color: #e5e7eb;
  }
  
  .rich-text-renderer.answer {
    color: #f9fafb;
  }
  
  .rich-text-renderer strong {
    color: #f9fafb;
  }
  
  .rich-text-renderer em {
    color: #00973a;
  }
  
  .rich-text-renderer h1,
  .rich-text-renderer h2,
  .rich-text-renderer h3,
  .rich-text-renderer h4,
  .rich-text-renderer h5,
  .rich-text-renderer h6 {
    color: #f9fafb;
  }
  
  .rich-text-renderer code {
    background: #374151;
    color: #e5e7eb;
    border-color: #4b5563;
  }
  
  .rich-text-renderer pre {
    background: #1f2937;
    border-color: #374151;
  }
  
  .rich-text-renderer blockquote {
    background: #1f2937;
    color: #d1d5db;
    border-left-color: #3b82f6;
  }
  
  .rich-text-renderer.explanation {
    background: #1f2937;
    border-left-color: #3b82f6;
  }
}
