import React, { useState, useMemo } from 'react';
import './EntityList.css';

/**
 * EntityList component for displaying ranked entities with click-to-expand functionality
 * Features:
 * - Ranked entity display by relevance score
 * - Click to trigger NodeDetails view
 * - Category grouping and filtering
 * - Relevance score visualization
 * - Responsive grid layout
 * - Search and filter capabilities
 */
const EntityList = ({
  entities = [],
  onEntitySelect,
  showRelevanceScore = true,
  showCategory = true,
  groupByCategory = false,
  maxVisible = 10,
  searchable = false
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('relevance'); // 'relevance', 'name', 'category'

  // Get unique categories
  const categories = useMemo(() => {
    const cats = [...new Set(entities.map(e => e.category).filter(Boolean))];
    return cats.sort();
  }, [entities]);

  // Filter and sort entities
  const processedEntities = useMemo(() => {
    let filtered = entities;

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(entity =>
        entity.name?.toLowerCase().includes(term) ||
        entity.description?.toLowerCase().includes(term) ||
        entity.category?.toLowerCase().includes(term)
      );
    }

    // Apply category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(entity => entity.category === selectedCategory);
    }

    // Sort entities
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return (a.name || '').localeCompare(b.name || '');
        case 'category':
          return (a.category || '').localeCompare(b.category || '');
        case 'relevance':
        default:
          return (b.relevance_score || 0) - (a.relevance_score || 0);
      }
    });

    return filtered.slice(0, maxVisible);
  }, [entities, searchTerm, selectedCategory, sortBy, maxVisible]);

  // Group entities by category if requested
  const groupedEntities = useMemo(() => {
    if (!groupByCategory) {
      return { all: processedEntities };
    }

    const groups = {};
    processedEntities.forEach(entity => {
      const category = entity.category || 'Other';
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(entity);
    });

    return groups;
  }, [processedEntities, groupByCategory]);

  // Handle entity click
  const handleEntityClick = (entity) => {
    // Enhanced debugging for entity click investigation
    console.log('🔍 EntityList handleEntityClick - DEBUGGING ENTITY CLICK FLOW:');
    console.log('1. Entity clicked:', entity);
    console.log('2. Entity ID:', entity?.id);
    console.log('3. Entity name:', entity?.name);
    console.log('4. Entity type:', typeof entity);
    console.log('5. Entity keys:', entity ? Object.keys(entity) : 'no entity');
    console.log('6. onEntitySelect function exists:', typeof onEntitySelect === 'function');
    
    if (onEntitySelect) {
      console.log('7. Calling onEntitySelect with entity:', entity);
      onEntitySelect(entity);
      console.log('8. onEntitySelect call completed');
    } else {
      console.error('❌ EntityList: onEntitySelect prop is missing or not a function');
    }
  };

  // Get relevance score color
  const getRelevanceColor = (score) => {
    // Emphasize high confidence; de-emphasize medium/low with neutrals
    if (score >= 0.8) return '#00973a';      // brand green (strong)
    if (score >= 0.6) return '#22c55e';      // medium green
    if (score >= 0.4) return '#9ca3af';      // neutral gray (no alarming red)
    return '#d1d5db';                         // light gray
  };

  // Get category color
  const getCategoryColor = (category) => {
    const colors = {
      'EMS': '#40C4FF',
      'SEP': '#A5D6A7',
      'MMC': '#FFAB91',
      'TRADING': '#E1BEE7',
      'RISK': '#FFCDD2',
      'COMPLIANCE': '#FFF9C4',
      'WORKFLOW': '#C8E6C9',
      'SYSTEM': '#BBDEFB'
    };
    return colors[category?.toUpperCase()] || '#f3f4f6';
  };

  // Render entity item
  const renderEntity = (entity, index) => {
    // Debug logging for entity rendering investigation
    if (process.env.NODE_ENV === 'development') {
      console.log('EntityList Render Debug:', {
        index,
        entity,
        entityId: entity?.id,
        entityName: entity?.name,
        entityType: typeof entity,
        entityKeys: entity ? Object.keys(entity) : 'no entity'
      });
    }

    const relevanceScore = entity.relevance_score || 0;
    const categoryColor = getCategoryColor(entity.category);

    return (
      <div
        key={entity.id || `entity-${index}`}
        className="entity-item"
        onClick={() => handleEntityClick(entity)}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleEntityClick(entity);
          }
        }}
        aria-label={`View details for ${entity.name}`}
      >
        <div className="entity-header">
          <div className="entity-name" title={entity.name}>
            {entity.name}
          </div>
          {showRelevanceScore && (
            <div 
              className="entity-relevance"
              style={{ color: getRelevanceColor(relevanceScore) }}
              title={`Relevance: ${(relevanceScore * 100).toFixed(1)}%`}
            >
              {(relevanceScore * 100).toFixed(0)}%
            </div>
          )}
        </div>

        {entity.description && (
          <div className="entity-description" title={entity.description}>
            {entity.description}
          </div>
        )}

        <div className="entity-footer">
          {showCategory && entity.category && (
            <div 
              className="entity-category"
              style={{ backgroundColor: categoryColor }}
            >
              {entity.category}
            </div>
          )}
          
          {entity.properties && Object.keys(entity.properties).length > 0 && (
            <div className="entity-properties-indicator" title="Has additional properties">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,17A1.5,1.5 0 0,1 10.5,15.5A1.5,1.5 0 0,1 12,14A1.5,1.5 0 0,1 13.5,15.5A1.5,1.5 0 0,1 12,17M12,10.5C10.07,10.5 8.5,8.93 8.5,7C8.5,5.07 10.07,3.5 12,3.5C13.93,3.5 15.5,5.07 15.5,7C15.5,8.93 13.93,10.5 12,10.5Z" />
              </svg>
            </div>
          )}
        </div>

        {showRelevanceScore && (
          <div className="entity-relevance-bar">
            <div 
              className="relevance-fill"
              style={{ 
                width: `${relevanceScore * 100}%`,
                backgroundColor: getRelevanceColor(relevanceScore)
              }}
            />
          </div>
        )}
      </div>
    );
  };

  if (entities.length === 0) {
    return (
      <div className="entity-list-empty">
        <div className="empty-message">No entities found</div>
      </div>
    );
  }

  return (
    <div className="entity-list">
      {/* Controls */}
      {(searchable || categories.length > 1) && (
        <div className="entity-list-controls">
          {searchable && (
            <div className="search-control">
              <input
                type="text"
                placeholder="Search entities..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>
          )}

          {categories.length > 1 && (
            <div className="filter-control">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="category-filter"
              >
                <option value="all">All Categories</option>
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
          )}

          <div className="sort-control">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="sort-select"
            >
              <option value="relevance">Sort by Relevance</option>
              <option value="name">Sort by Name</option>
              <option value="category">Sort by Category</option>
            </select>
          </div>
        </div>
      )}

      {/* Entity Grid */}
      {groupByCategory ? (
        <div className="entity-groups">
          {Object.entries(groupedEntities).map(([category, categoryEntities]) => (
            <div key={category} className="entity-group">
              <h5 className="group-title">{category}</h5>
              <div className="entity-grid">
                {categoryEntities.map((entity, index) => renderEntity(entity, index))}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="entity-grid">
          {processedEntities.map((entity, index) => renderEntity(entity, index))}
        </div>
      )}

      {/* Results Info */}
      <div className="entity-list-info">
        Showing {processedEntities.length} of {entities.length} entities
        {searchTerm && ` matching "${searchTerm}"`}
        {selectedCategory !== 'all' && ` in ${selectedCategory}`}
      </div>
    </div>
  );
};

export default EntityList;
