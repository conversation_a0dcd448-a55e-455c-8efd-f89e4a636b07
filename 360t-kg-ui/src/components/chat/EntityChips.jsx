import React from 'react';
import './EntityChips.css';

/**
 * EntityChips
 * Compact, chip-style list of entities that fits many items
 * without overpowering the main answer.
 */
export default function EntityChips({ entities = [], onSelect }) {
  if (!Array.isArray(entities) || entities.length === 0) return null;

  return (
    <div className="entity-chips" role="list" aria-label="Related entities">
      {entities.map((e, i) => {
        const score = typeof e.relevance_score === 'number' ? Math.round(e.relevance_score * 100) : null;
        return (
          <button
            key={e.id || `entity-${i}`}
            className="entity-chip"
            onClick={() => onSelect && onSelect(e)}
            role="listitem"
            title={e.description || e.name}
            aria-label={`Open ${e.name || 'entity'} details`}
          >
            <span className="chip-name">{e.name || e.id}</span>
            {score != null && (
              <span className="chip-score" aria-hidden="true">{score}%</span>
            )}
          </button>
        );
      })}
    </div>
  );
}

