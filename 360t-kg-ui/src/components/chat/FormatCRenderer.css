/* FormatCRenderer.css - Styling for native Format C rendering */

.format-c-response {
  display: flex;
  flex-direction: column;
  gap: var(--360t-space-4, 1rem);
  margin: var(--360t-space-4, 1rem) 0;
  font-family: var(--360t-font-family-primary, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif);
}

.format-c-summary {
  position: relative; /* Anchor related pill positioning */
  padding: 0; /* AnswerCard handles padding */
  margin-bottom: var(--360t-space-3, 0.75rem);
  overflow: visible; /* Allow popover to extend beyond summary card */
}

/* decorative top stripe removed to keep answer clean */
.format-c-summary::before { content: none; }

.format-c-summary-content {
  font-size: clamp(0.95rem, 0.92rem + 0.2vw, 1.05rem); /* Reverted to original size to restore text formatting inheritance */
  line-height: 1.6;
  color: var(--360t-text, #333333);
  font-family: var(--360t-font-family-primary, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif);
}

.format-c-summary-content p {
  margin: 0 0 12px 0;
}

.format-c-summary-content p:last-child {
  margin-bottom: 0;
}

/* Ensure no markdown headers are visible */
.format-c-summary-content h1,
.format-c-summary-content h2,
.format-c-summary-content h3,
.format-c-summary-content h4,
.format-c-summary-content h5,
.format-c-summary-content h6 {
  display: none !important;
}

/* Enhanced text formatting with higher specificity and better contrast */
.format-c-summary-content strong {
  font-weight: 700 !important;  /* Increased to 700 with !important for visibility */
  color: #111827 !important;    /* Dark color for better contrast */
  display: inline !important;
}

.format-c-summary-content em {
  font-style: italic !important;
  color: #059669 !important;    /* Green emphasis color */
  font-weight: 500 !important;  /* Semi-bold for better visibility */
  display: inline !important;
}

/* Results section styling */
.result-content {
  font-size: 14px;
  line-height: 1.5;
}

.result-content p {
  margin: 0 0 8px 0;
}

.result-content p:last-child {
  margin-bottom: 0;
}

/* Follow-up questions container - Enhanced with 360T styling and prominent positioning */
.format-c-response [data-testid="follow-up-questions"] {
  background: linear-gradient(135deg, rgba(0, 151, 58, 0.04) 0%, rgba(255, 255, 255, 0.95) 100%);
  border-radius: var(--360t-radius-lg, 8px);
  padding: var(--360t-space-5, 1.25rem);
  border: 2px solid rgba(0, 151, 58, 0.2);
  box-shadow: var(--360t-shadow-md, 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06));
  margin-top: var(--360t-space-4, 1rem);
  position: relative;
}

.format-c-response [data-testid="follow-up-questions"]::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--360t-primary, #00973A) 0%, var(--360t-primary-dark, #007d30) 100%);
  border-radius: var(--360t-radius-lg, 8px) var(--360t-radius-lg, 8px) 0 0;
}

/* Follow-up questions title styling */
.format-c-response [data-testid="follow-up-questions"] .MuiTypography-subtitle1 {
  color: var(--360t-primary, #00973A);
  font-weight: var(--360t-font-semibold, 600);
  font-size: var(--360t-text-lg, 1.125rem);
  margin-bottom: var(--360t-space-2, 0.5rem);
}

/* Follow-up questions icon styling */
.format-c-response [data-testid="follow-up-questions"] .MuiSvgIcon-root {
  color: var(--360t-primary, #00973A);
}

/* Follow-up cards hover effects */
.format-c-response [data-testid="follow-up-questions"] .MuiCard-root {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(0, 151, 58, 0.15);
}

.format-c-response [data-testid="follow-up-questions"] .MuiCard-root:hover {
  border-color: var(--360t-primary, #00973A);
  box-shadow: 0 6px 16px rgba(0, 151, 58, 0.2);
  transform: translateY(-2px);
  background: rgba(0, 151, 58, 0.02);
}

/* Accordion styling improvements with 360T theme */
.format-c-response .MuiAccordion-root {
  border: 1px solid var(--360t-mid-gray, #e2e8f0);
  border-radius: var(--360t-radius-lg, 8px) !important;
  box-shadow: var(--360t-shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05));
  background: var(--360t-white, #ffffff);
  margin-bottom: var(--360t-space-3, 0.75rem);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.format-c-response .MuiAccordion-root:before {
  display: none;
}

.format-c-response .MuiAccordion-root:hover {
  box-shadow: var(--360t-shadow-md, 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06));
  border-color: rgba(0, 151, 58, 0.3);
}

.format-c-response .MuiAccordionSummary-root {
  background: var(--360t-white, #ffffff);
  border-radius: var(--360t-radius-lg, 8px) var(--360t-radius-lg, 8px) 0 0;
  min-height: 56px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 0 var(--360t-space-4, 1rem);
}

.format-c-response .MuiAccordionSummary-root:hover {
  background: rgba(0, 151, 58, 0.04);
}

.format-c-response .MuiAccordionSummary-expandIconWrapper {
  color: var(--360t-primary, #00973A);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.format-c-response .MuiAccordionDetails-root {
  padding: var(--360t-space-5, 1.25rem);
  background: var(--360t-white, #ffffff);
  border-radius: 0 0 var(--360t-radius-lg, 8px) var(--360t-radius-lg, 8px);
  border-top: 1px solid var(--360t-mid-gray, #e2e8f0);
}

/* Entity list specific styling */
.format-c-response .entity-item {
  margin-bottom: 8px;
  padding: 8px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.format-c-response .entity-item:hover {
  background: #f0f7ff;
  border-color: #1976d2;
}

/* Metadata/Debug should be visually secondary */
.format-c-response .debug-accordion {
  background: var(--360t-gray-50, #f8fafc);
  border-color: var(--360t-gray-200, #e5e7eb);
}
.format-c-response .debug-accordion .MuiAccordionSummary-root {
  background: var(--360t-gray-50, #f8fafc);
}

/* Tone down hover emphasis for debug */
.format-c-response .debug-accordion:hover {
  border-color: var(--360t-gray-300, #cbd5e1);
  box-shadow: none;
}

/* Inline metadata panel (toggled from AnswerCard bottom-right icon) */
.metadata-panel {
  padding: 0.75rem 1rem;
  background: var(--360t-gray-50, #f8fafc);
  border: 1px solid var(--360t-gray-200, #e5e7eb);
  border-radius: 8px;
}

/* Confidence chip styling with 360T theme */
.format-c-response .MuiChip-root {
  font-size: var(--360t-text-xs, 0.75rem);
  height: 28px;
  font-family: var(--360t-font-family-primary, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif);
  font-weight: var(--360t-font-medium, 500);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* High confidence styling (80%+) */
.format-c-response .MuiChip-colorSuccess {
  background-color: rgba(0, 151, 58, 0.12);
  color: var(--360t-primary, #00973A);
  border-color: var(--360t-primary, #00973A);
}

.format-c-response .MuiChip-colorSuccess:hover {
  background-color: rgba(0, 151, 58, 0.18);
  box-shadow: 0 2px 4px rgba(0, 151, 58, 0.2);
}

/* Medium confidence styling (60-80%) */
.format-c-response .MuiChip-colorWarning {
  background-color: rgba(255, 152, 0, 0.12);
  color: #e65100;
  border-color: #ff9800;
}

.format-c-response .MuiChip-colorWarning:hover {
  background-color: rgba(255, 152, 0, 0.18);
  box-shadow: 0 2px 4px rgba(255, 152, 0, 0.2);
}

/* Low confidence styling (<60%) */
.format-c-response .MuiChip-colorDefault {
  background-color: rgba(158, 158, 158, 0.12);
  color: var(--360t-dark-gray, #4a5568);
  border-color: var(--360t-mid-gray, #e2e8f0);
}

/* Enhanced animations and transitions */
.format-c-summary {
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.format-c-response [data-testid="follow-up-questions"] {
  animation: slideInUp 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) 0.1s both;
}

.format-c-response .MuiAccordion-root {
  animation: slideInUp 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) 0.2s both;
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: translateY(15px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Improved visual hierarchy */
.format-c-response .MuiTypography-subtitle1 {
  font-weight: var(--360t-font-semibold, 600);
  color: var(--360t-text, #333333);
}

/* Entity hover improvements */
.format-c-response .entity-item:hover {
  background: rgba(0, 151, 58, 0.05);
  border-color: var(--360t-primary, #00973A);
  box-shadow: 0 2px 8px rgba(0, 151, 58, 0.15);
  transform: translateY(-1px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .format-c-response {
    margin: var(--360t-space-2, 0.5rem) 0;
    gap: var(--360t-space-3, 0.75rem);
  }
  
  .format-c-summary {
    padding: var(--360t-space-4, 1rem);
    border-left-width: 3px;
  }
  
  .format-c-summary-content {
    font-size: var(--360t-text-sm, 0.875rem);
    line-height: var(--360t-leading-normal, 1.5);
  }
  
  .result-content {
    font-size: var(--360t-text-sm, 0.875rem);
  }
  
  .format-c-response [data-testid="follow-up-questions"] {
    padding: var(--360t-space-4, 1rem);
    border-width: 1px;
  }
  
  .format-c-response [data-testid="follow-up-questions"] .MuiTypography-subtitle1 {
    font-size: var(--360t-text-base, 1rem);
  }
  
  .format-c-response .MuiAccordionSummary-root {
    min-height: 48px;
    padding: 0 var(--360t-space-3, 0.75rem);
  }
  
  .format-c-response .MuiAccordionDetails-root {
    padding: var(--360t-space-4, 1rem);
  }
  
  .format-c-response .MuiChip-root {
    height: 24px;
    font-size: var(--360t-text-xs, 0.75rem);
  }
}

@media (max-width: 480px) {
  .format-c-summary {
    padding: var(--360t-space-3, 0.75rem);
  }
  
  .format-c-response [data-testid="follow-up-questions"] {
    padding: var(--360t-space-3, 0.75rem);
    margin-top: var(--360t-space-3, 0.75rem);
  }
}

/* Ensure clean text appearance - no markdown artifacts */
.format-c-response .format-c-summary-content::before,
.format-c-response .format-c-summary-content::after {
  content: none;
}

/* Hide any residual markdown syntax */
.format-c-response .format-c-summary-content *[class*="markdown"],
.format-c-response .format-c-summary-content *[class*="header"] {
  display: none !important;
}

/* Clean list styling for any remaining lists */
.format-c-response .format-c-summary-content ul,
.format-c-response .format-c-summary-content ol {
  margin: 8px 0;
  padding-left: 20px;
}

.format-c-response .format-c-summary-content li {
  margin: 4px 0;
  line-height: 1.4;
}

/* Smooth scroll behavior for expanding sections */
.format-c-response {
  scroll-behavior: smooth;
}

/* Focus states for accessibility */
.format-c-response .MuiAccordionSummary-root:focus-visible {
  outline: 2px solid var(--360t-primary, #00973A);
  outline-offset: 2px;
  border-radius: var(--360t-radius-md, 6px);
}

.format-c-response [data-testid="follow-up-questions"] .MuiCard-root:focus-visible {
  outline: 2px solid var(--360t-primary, #00973A);
  outline-offset: 2px;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .format-c-summary,
  .format-c-response [data-testid="follow-up-questions"],
  .format-c-response .MuiAccordion-root {
    animation: none;
  }
  
  .format-c-response .MuiAccordion-root,
  .format-c-response .MuiAccordionSummary-root,
  .format-c-response [data-testid="follow-up-questions"] .MuiCard-root,
  .format-c-response .entity-item,
  .format-c-response .MuiChip-root {
    transition: none;
  }
}
