/* SourceTooltip.css */

.source-tooltip-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
  position: relative;
  min-height: 60px;
}

.source-tooltip-container:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.source-tooltip-container.clickable {
  cursor: pointer;
}

.source-tooltip-container.clickable:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.source-tooltip-container:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Icon Wrapper */
.source-icon-wrapper {
  position: relative;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.document-icon {
  width: 32px;
  height: 32px;
  transition: transform 0.2s ease;
}

.source-tooltip-container:hover .document-icon {
  transform: scale(1.1);
}

/* Document Type Icons */
.document-icon.pdf {
  color: #dc2626;
}

.document-icon.doc {
  color: #2563eb;
}

.document-icon.web {
  color: #059669;
}

.document-icon.other {
  color: #6b7280;
}

/* Category Badge */
.source-category-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  padding: 0.125rem 0.25rem;
  font-size: 0.625rem;
  font-weight: 600;
  color: #ffffff;
  border-radius: 4px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  min-width: 20px;
  text-align: center;
  line-height: 1.2;
}

/* Source Info */
.source-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.source-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.source-type {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

/* Link Indicator */
.source-link-indicator {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
  color: #6b7280;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.source-tooltip-container.clickable:hover .source-link-indicator {
  opacity: 1;
  color: #3b82f6;
}

.source-link-indicator svg {
  width: 100%;
  height: 100%;
}

/* Tooltip */
.source-tooltip {
  position: fixed;
  z-index: 1000;
  pointer-events: none;
  max-width: 300px;
  animation: tooltipFadeIn 0.2s ease-out;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-100%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(-100%) scale(1);
  }
}

.tooltip-content {
  background-color: #1f2937;
  color: #ffffff;
  padding: 0.75rem;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.25);
  font-size: 0.875rem;
  line-height: 1.4;
}

.tooltip-title {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #ffffff;
}

.tooltip-snippet {
  color: #d1d5db;
  margin-bottom: 0.5rem;
  font-size: 0.8rem;
  line-height: 1.3;
}

.tooltip-url {
  color: #93c5fd;
  font-size: 0.75rem;
  word-break: break-all;
  margin-bottom: 0.5rem;
}

.tooltip-category {
  color: #d1d5db;
  font-size: 0.75rem;
  font-weight: 500;
}

.tooltip-category span {
  font-weight: 600;
}

.tooltip-arrow {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #1f2937;
}

/* Tooltip states */
.tooltip-no-content {
  color: #9ca3af;
  font-style: italic;
}

.tooltip-score {
  font-size: 0.8rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .source-tooltip-container {
    padding: 0.5rem;
    gap: 0.5rem;
    min-height: 50px;
  }

  .document-icon {
    width: 28px;
    height: 28px;
  }

  .source-title {
    font-size: 0.8rem;
  }

  .source-type {
    font-size: 0.7rem;
  }

  .source-tooltip {
    max-width: 250px;
  }

  .tooltip-content {
    padding: 0.5rem;
    font-size: 0.8rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .source-tooltip-container {
    border-width: 2px;
  }

  .source-tooltip-container:hover {
    border-color: #000000;
  }

  .tooltip-content {
    background-color: #000000;
    border: 1px solid #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .source-tooltip-container,
  .document-icon,
  .source-link-indicator {
    transition: none;
  }

  .source-tooltip-container:hover {
    transform: none;
  }

  .source-tooltip-container:hover .document-icon {
    transform: none;
  }

  .source-tooltip {
    animation: none;
  }
}
