/* FollowUpCarousel Styles - Modern carousel for follow-up questions */

.follow-up-carousel {
  position: relative;
  width: 100%;
  max-width: 1080px; /* Increased from 900px to 1080px (20% wider) for better content display */
  margin: var(--360t-space-2) auto var(--360t-space-4) auto; /* Reduced top margin for less prominence */
  padding: 0 35px; /* Adjusted padding to accommodate arrows without text overlap */
  outline: none;
  opacity: 0.7; /* Make slightly less prominent than input */
  transition: opacity 0.7s ease;
  border-radius: 14px;  
}

.follow-up-carousel:focus-visible {
  outline: 2px solid var(--360t-primary);
  outline-offset: 2px;
  border-radius: var(--360t-radius-lg);
}

.follow-up-carousel.disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* Main carousel container - Made compact */
.carousel-container {
  position: relative;
  height: 48px; /* Reduced from 64px to 48px for slimmer profile */
  overflow: hidden;
  border-radius: 14px; /* Slightly smaller radius for slimmer profile */
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.4) 0%, 
    rgba(250, 250, 250, 0.3) 100%
  ); /* Reduced opacity to let parent gradient show through */
  backdrop-filter: blur(8px);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06); /* More subtle shadow */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0.9; /* Make less prominent than input */
  transform: scale(0.98); /* Slightly smaller visual presence */
}

.carousel-container:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08); /* More subtle hover shadow */
  transform: translateY(-1px) scale(1.005); /* Gentler transform */
  opacity: 1; /* Increase opacity on hover for interaction feedback */
}

/* Navigation arrows */
.nav-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9); /* Semi-transparent background for better visibility over content */
  color: var(--360t-dark-gray);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10; /* Higher z-index to ensure visibility over content */
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(8px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.nav-arrow:hover:not(:disabled) {
  background: rgba(0, 151, 58, 0.1);
  color: var(--360t-primary);
  transform: translateY(-50%) scale(1.1);
}

.nav-arrow:active:not(:disabled) {
  transform: translateY(-50%) scale(0.95);
}

.nav-arrow:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.nav-arrow svg {
  width: 18px;
  height: 18px;
}

.nav-arrow-prev {
  left: 10px; /* Positioned inside the container for visibility */
}

.nav-arrow-next {
  right: 10px; /* Positioned inside the container for visibility */
}

/* Question card */
.question-card {
  position: relative;
  height: 100%;
  /* Fixed: Equal padding on both sides for perfect centering */
  /* Left padding accounts for navigation arrow width + safe space */
  /* Right padding mirrors left padding for perfect visual balance */
  padding: var(--360t-space-2) 60px var(--360t-space-2) 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 14px;
  overflow: hidden;
}

.question-card:hover .card-overlay {
  opacity: 1;
}

.question-card:active {
  transform: scale(0.98);
}

.question-card.transitioning {
  opacity: 0.8;
}

/* Card overlay for glass morphism effect */
.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(0, 151, 58, 0.05) 0%, 
    rgba(0, 151, 58, 0.02) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

/* Card content */
.card-content {
  flex: 1;
  min-width: 0; /* Allows text truncation */
}

.question-text {
  font-size: var(--360t-text-base); /* Reduced from text-lg to text-base for slimmer height */
  font-weight: var(--360t-font-medium);
  color: var(--360t-text);
  line-height: var(--360t-leading-tight); /* Tighter line height for compactness */
  margin-bottom: var(--360t-space-0-5); /* Reduced margin */
  display: -webkit-box;
  -webkit-line-clamp: 1; /* Reduced to single line for slim profile */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.context-hint {
  font-size: var(--360t-text-xs); /* Reduced from text-sm to text-xs for slimmer height */
  color: var(--360t-dark-gray);
  opacity: 0.7; /* Slightly more subtle */
  line-height: var(--360t-leading-tight); /* Tighter line height */
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}



/* Pause indicator */
.pause-indicator {
  position: absolute;
  top: var(--360t-space-2);
  right: var(--360t-space-2);
  width: 20px;
  height: 20px;
  color: var(--360t-primary);
  opacity: 0.7;
  z-index: 3;
  animation: pausePulse 1.5s ease-in-out infinite;
}

.pause-indicator svg {
  width: 100%;
  height: 100%;
}

@keyframes pausePulse {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

/* Empty state */
.follow-up-carousel-empty {
  text-align: center;
  padding: var(--360t-space-8);
  color: var(--360t-dark-gray);
}

.follow-up-carousel-empty .empty-icon {
  font-size: var(--360t-text-2xl);
  margin-bottom: var(--360t-space-4);
  opacity: 0.6;
}

.follow-up-carousel-empty .empty-message {
  font-size: var(--360t-text-base);
  color: var(--360t-dark-gray);
  opacity: 0.8;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .follow-up-carousel {
    max-width: 100%;
    margin: var(--360t-space-3) 0;
  }

  .carousel-container {
    height: 100px;
  }

  .question-card {
    /* Mobile: Equal padding for perfect centering */
    padding: var(--360t-space-3) var(--360t-space-12) var(--360t-space-3) var(--360t-space-12);
  }

  .question-text {
    font-size: var(--360t-text-base);
    -webkit-line-clamp: 1;
  }

  .context-hint {
    display: none; /* Hide context hints on mobile to save space */
  }

  .nav-arrow {
    width: 36px;
    height: 36px;
  }

  .nav-arrow svg {
    width: 16px;
    height: 16px;
  }

  .nav-arrow-prev {
    left: var(--360t-space-2);
  }

  .nav-arrow-next {
    right: var(--360t-space-2);
  }
}

/* Small mobile screens */
@media (max-width: 480px) {
  .carousel-container {
    height: 90px;
  }

  .question-card {
    /* Small mobile: Equal padding for perfect centering */
    padding: var(--360t-space-2) var(--360t-space-10) var(--360t-space-2) var(--360t-space-10);
  }

  .question-text {
    font-size: var(--360t-text-sm);
  }

}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .carousel-container {
    background: var(--360t-white);
    border: 2px solid var(--360t-border);
  }

  .nav-arrow {
    background: var(--360t-white);
    border: 1px solid var(--360t-border);
  }

  .card-overlay {
    display: none; /* Remove subtle overlays in high contrast mode */
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .follow-up-carousel,
  .carousel-container,
  .question-card,
  .nav-arrow,
  .pagination-dot,
  .card-overlay {
    transition: none;
    animation: none;
  }

  .nav-arrow:hover:not(:disabled) {
    transform: translateY(-50%);
  }

  .question-card:active {
    transform: none;
  }

  .pause-indicator {
    animation: none;
  }
}

/* Focus styles for keyboard navigation */
.question-card:focus-visible {
  outline: 2px solid var(--360t-primary);
  outline-offset: 2px;
}

.nav-arrow:focus-visible {
  outline: 2px solid var(--360t-primary);
  outline-offset: 2px;
}

