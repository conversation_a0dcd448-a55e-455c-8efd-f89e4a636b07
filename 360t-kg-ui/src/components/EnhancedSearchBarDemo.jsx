import React, { useState } from 'react';
import EnhancedSearchBar from './EnhancedSearchBar';

// Demo component to showcase the Enhanced Search Bar functionality
const EnhancedSearchBarDemo = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [mockResults] = useState([
    {
      id: '1',
      name: 'Database Server Alpha',
      group: 'Infrastructure',
      color: '#3b82f6',
      properties: { category: 'server' },
    },
    {
      id: '2',
      name: 'API Gateway Beta',
      group: 'Services',
      color: '#10b981',
      properties: { category: 'api' },
    },
    {
      id: '3',
      name: 'Load Balancer Gamma',
      group: 'Network',
      color: '#f59e0b',
      properties: { category: 'network' },
    },
    {
      id: '4',
      name: 'Cache Store Delta',
      group: 'Storage',
      color: '#ef4444',
      properties: { category: 'cache' },
    },
    {
      id: '5',
      name: 'Message Queue Epsilon',
      group: 'Messaging',
      color: '#8b5cf6',
      properties: { category: 'queue' },
    },
  ]);

  const handleSearchChange = (query) => {
    console.log('Search query changed:', query);
    setSearchQuery(query);
  };

  const handleNodeSelect = (node) => {
    console.log('Node selected:', node);
    alert(`Selected node: ${node.name} (${node.group})`);
  };

  const handleCenterOnNode = (node) => {
    console.log('Center on node:', node);
    // In a real app, this would center the graph view on the selected node
  };

  // Filter results based on search query
  const filteredResults = searchQuery.trim() 
    ? mockResults.filter(node => 
        node.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        node.group.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : [];

  return (
    <div style={{
      position: 'relative',
      width: '100vw',
      height: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    }}>
      <div style={{
        marginBottom: '2rem',
        textAlign: 'center',
        color: 'white',
      }}>
        <h1 style={{ fontSize: '2.5rem', fontWeight: '700', margin: '0 0 1rem 0' }}>
          Enhanced Search Bar Demo
        </h1>
        <p style={{ fontSize: '1.1rem', opacity: 0.9, margin: 0 }}>
          Modern glassmorphism search with autocomplete and animations
        </p>
      </div>

      {/* Enhanced Search Bar */}
      <EnhancedSearchBar
        searchQuery={searchQuery}
        onSearchChange={handleSearchChange}
        searchResults={filteredResults}
        onNodeSelect={handleNodeSelect}
        onCenterOnNode={handleCenterOnNode}
        placeholder="Search for nodes, services, or infrastructure..."
        disabled={false}
        showRecentSearches={true}
        maxResults={10}
      />

      {/* Demo Information */}
      <div style={{
        marginTop: '3rem',
        padding: '1.5rem',
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(10px)',
        borderRadius: '16px',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        maxWidth: '600px',
        color: 'white',
      }}>
        <h3 style={{ margin: '0 0 1rem 0', fontSize: '1.2rem' }}>Features Demonstrated:</h3>
        <ul style={{ margin: 0, paddingLeft: '1.5rem', lineHeight: 1.6 }}>
          <li>🔍 Real-time search with debouncing</li>
          <li>✨ Smooth animations with Framer Motion</li>
          <li>🎨 Glassmorphism design with backdrop blur</li>
          <li>📱 Responsive design for mobile devices</li>
          <li>⌨️ Full keyboard navigation support</li>
          <li>♿ Accessibility compliance (ARIA labels)</li>
          <li>💾 Recent searches persistence</li>
          <li>🎯 Material UI autocomplete integration</li>
        </ul>
      </div>

      {/* Search Results Display */}
      {searchQuery && (
        <div style={{
          marginTop: '2rem',
          padding: '1rem',
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(10px)',
          borderRadius: '12px',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          color: 'white',
          minWidth: '300px',
        }}>
          <h4 style={{ margin: '0 0 0.5rem 0' }}>
            Current search: "{searchQuery}"
          </h4>
          <p style={{ margin: 0, opacity: 0.8, fontSize: '0.9rem' }}>
            {filteredResults.length} result{filteredResults.length !== 1 ? 's' : ''} found
          </p>
        </div>
      )}
    </div>
  );
};

export default EnhancedSearchBarDemo;