import React, { useState, useEffect, useCallback } from 'react';
import { getNodeIcon } from '../constants/iconMap';
import { getCategoryColor, getContrastTextColor } from '../constants/categoryColors';
import '../styles/NodeDetailsModern.css';

/**
 * RelationshipDetails component displays detailed information about a selected relationship
 * @param {Object} selectedRelationship - The selected relationship object
 * @param {Function} onClose - Callback when closing details
 * @param {Function} onNodeSelect - Callback when a related node is selected
 */
function RelationshipDetails({ selectedRelationship, onClose, onNodeSelect }) {
  const [relationshipData, setRelationshipData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // State for resizable panel
  const [panelWidth, setPanelWidth] = useState(() => {
    const saved = localStorage.getItem('relationshipDetailsWidth');
    return saved ? parseInt(saved) : 28;
  });
  const [isResizing, setIsResizing] = useState(false);
  
  // State for expanded text fields
  const [expandedTexts, setExpandedTexts] = useState({
    fact: false
  });

  // Helper function to get display name for a node
  const getDisplayName = useCallback((node) => {
    if (!node) return 'Unknown';
    return node.properties?.name || node.label || node.name || node.title || node.id || 'Unknown';
  }, []);

  // Helper function to get node type
  const getNodeType = useCallback((node) => {
    if (!node) return 'Default';
    if (node.labels && Array.isArray(node.labels) && node.labels.length > 0) {
      return node.labels[0];
    }
    return node.type || node.group || 'Default';
  }, []);

  // Helper function to get node category for badge display
  const getNodeCategory = useCallback((node) => {
    if (!node) return null;
    return node.properties?.category || null;
  }, []);

  // Function to split text into sentences intelligently
  const splitIntoSentences = useCallback((text) => {
    if (!text) return [];
    
    // Smart sentence splitting that handles common edge cases
    const sentences = text
      .replace(/([.!?])\s+/g, '$1|SPLIT|') // Mark sentence boundaries
      .split('|SPLIT|')
      .map(sentence => sentence.trim())
      .filter(sentence => sentence.length > 0)
      .filter(sentence => {
        // Filter out very short fragments that aren't meaningful sentences
        return sentence.length > 3 && /[a-zA-Z]/.test(sentence);
      });
    
    return sentences;
  }, []);

  // Function to toggle text expansion
  const toggleTextExpansion = useCallback((field) => {
    setExpandedTexts(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  }, []);

  // Handle panel resizing
  const handleMouseDown = useCallback((e) => {
    e.preventDefault();
    setIsResizing(true);
  }, []);

  const handleMouseMove = useCallback((e) => {
    if (!isResizing) return;
    
    const newWidth = ((window.innerWidth - e.clientX) / window.innerWidth) * 100;
    const clampedWidth = Math.max(20, Math.min(50, newWidth));
    setPanelWidth(clampedWidth);
  }, [isResizing]);

  const handleMouseUp = useCallback(() => {
    if (isResizing) {
      setIsResizing(false);
      localStorage.setItem('relationshipDetailsWidth', panelWidth.toString());
    }
  }, [isResizing, panelWidth]);

  // Render expandable fact text with modern card styling
  const renderExpandableFactText = useCallback((text, title = 'Fact') => {
    if (!text) return null;
    
    const sentences = splitIntoSentences(text);
    const isExpanded = expandedTexts.fact;
    const maxSentences = 4; // Show 4 sentences initially
    const needsExpansion = sentences.length > maxSentences;
    const displaySentences = isExpanded || !needsExpansion ? sentences : sentences.slice(0, maxSentences);

    return (
      <div className="expandable-text-container" title={title}>
        <div className="property-name-green">
          {title}
          {needsExpansion && (
            <button 
              className="expand-text-inline"
              onClick={() => toggleTextExpansion('fact')}
              title={isExpanded ? 'Show less' : 'Show more'}
            >
              {isExpanded ? '[-]' : '[+]'}
            </button>
          )}
        </div>
        <div className="property-value-expandable">
          <div className="text-content-bulleted">
            <ul className="sentence-bullet-list">
              {displaySentences.map((sentence, index) => (
                <li key={index} className="sentence-bullet-item">
                  {sentence}
                </li>
              ))}
            </ul>
            {needsExpansion && !isExpanded && (
              <button 
                className="expand-text-more"
                onClick={() => toggleTextExpansion('fact')}
                title="Click to show more"
              >
                [...{sentences.length - maxSentences} more sentences]
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }, [splitIntoSentences, expandedTexts.fact, toggleTextExpansion]);

  // Process relationship data when selectedRelationship changes
  useEffect(() => {
    if (selectedRelationship) {
      console.log('🔍 RelationshipDetails RECEIVED:', {
        selectedRelationship,
        hasFact: !!selectedRelationship.fact,
        hasPropertiesFact: !!selectedRelationship.properties?.fact,
        factValue: selectedRelationship.fact,
        propertiesFactValue: selectedRelationship.properties?.fact,
        allProperties: selectedRelationship.properties
      });
      
      setIsLoading(true);
      setError(null);
      
      try {
        // Process the relationship data
        const processedData = {
          id: selectedRelationship.id || `${selectedRelationship.source}-${selectedRelationship.target}`,
          type: selectedRelationship.type || selectedRelationship.label || 'RELATES_TO',
          source: selectedRelationship.source,
          target: selectedRelationship.target,
          properties: selectedRelationship.properties || {},
          fact: selectedRelationship.fact || selectedRelationship.properties?.fact || null,
          // Additional metadata
          sourceNode: selectedRelationship.sourceNode || null,
          targetNode: selectedRelationship.targetNode || null
        };
        
        console.log('🔍 RelationshipDetails PROCESSED:', {
          processedData,
          extractedFact: processedData.fact,
          hasExtractedFact: !!processedData.fact
        });
        
        setRelationshipData(processedData);
      } catch (err) {
        console.error('Error processing relationship data:', err);
        setError('Failed to process relationship details');
      } finally {
        setIsLoading(false);
      }
    }
  }, [selectedRelationship]);

  // Handle ESC key to close
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [onClose]);

  // Add mouse event listeners for resizing
  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = 'default';
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp]);

  // Handle node clicks
  const handleNodeClick = useCallback((node) => {
    if (onNodeSelect && node) {
      onNodeSelect(node);
    }
  }, [onNodeSelect]);

  // Render loading state
  if (isLoading) {
    return (
      <div className="details-panel-modern">
        <div className="loading-overlay">
          <div className="loading-spinner"></div>
          <p>Loading relationship details...</p>
        </div>
      </div>
    );
  }

  // Render error state
  if (error && !relationshipData) {
    return (
      <div className="details-panel-modern">
        <div className="error-message" style={{ margin: '20px' }}>
          <p>⚠️ {error}</p>
          <button className="close-button" onClick={onClose} style={{ top: '10px', right: '10px' }}>×</button>
        </div>
      </div>
    );
  }

  // Render when no relationship is selected
  if (!relationshipData) {
    return (
      <div className="details-panel-modern empty-panel">
        <p>No relationship selected</p>
      </div>
    );
  }

  return (
    <div className="node-details-modern" style={{ '--panel-width': `${panelWidth}%` }}>
      {/* Resize Handle */}
      <div 
        className="resize-handle" 
        onMouseDown={handleMouseDown}
        title="Drag to resize panel"
      />
      
      <div className="node-details-header">
        <div className="relationship-icon-container" style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <div className="relationship-type-icon" style={{
            width: '20px',
            height: '20px',
            backgroundColor: '#00973a',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontSize: '10px',
            fontWeight: 'bold'
          }}>
            →
          </div>
          <div>
            <h2 className="node-title" title={relationshipData.properties?.name || relationshipData.type}>
              {relationshipData.properties?.name || relationshipData.type}
            </h2>
            <span className="detail-type-modern">Relationship</span>
          </div>
        </div>
        <button className="icon-btn" onClick={onClose} title="Close details panel">×</button>
      </div>

      <div className="node-details-content">
        {/* Fact Content - Most Prominent Section */}
        {relationshipData.fact && (
          <div className="direct-properties-content">
            {renderExpandableFactText(relationshipData.fact, 'Fact')}
          </div>
        )}

        {/* Additional Properties */}
        {relationshipData.properties && Object.entries(relationshipData.properties)
          .filter(([key]) => key !== 'fact' && key !== 'name')
          .length > 0 && (
          <div className="section-card-modern">
            <div className="section-header-card">
              <div className="section-icon-title">
                <span className="section-icon">📋</span>
                <h3 className="section-title-card">Properties</h3>
              </div>
              <span className="section-count">
                {Object.entries(relationshipData.properties).filter(([key]) => key !== 'fact' && key !== 'name').length}
              </span>
            </div>
            <div className="properties-content-card">
              {Object.entries(relationshipData.properties)
                .filter(([key]) => key !== 'fact' && key !== 'name')
                .map(([key, value]) => (
                  <div key={key} className="expandable-text-container">
                    <div className="property-name-green">
                      {key.replace(/_/g, ' ')}
                    </div>
                    <div className="property-value-expandable">
                      <div className="text-content">
                        {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                      </div>
                    </div>
                  </div>
                ))
              }
            </div>
          </div>
        )}

        {/* Connected Nodes - Modern Card Layout */}
        <div className="section-card-modern">
          <div className="section-header-card">
            <div className="section-icon-title">
              <span className="section-icon">🔗</span>
              <h3 className="section-title-card">Connected Nodes</h3>
            </div>
            <span className="section-count">2</span>
          </div>
          
          {/* Source Node */}
          <div className="node-connection-modern">
            <h5 className="connection-label-modern">Source</h5>
            {relationshipData.sourceNode ? (
              <div 
                className="relationship-node-modern clickable"
                onClick={() => handleNodeClick(relationshipData.sourceNode)}
                title={`Click to select: ${getDisplayName(relationshipData.sourceNode)}`}
              >
                <div className="node-content-modern">
                  <span className="arrow-modern">←</span>
                  <span className="node-name-modern">{getDisplayName(relationshipData.sourceNode)}</span>
                </div>
                {getNodeCategory(relationshipData.sourceNode) ? (
                  <span className="node-category-badge-modern" style={{
                    backgroundColor: getCategoryColor(getNodeCategory(relationshipData.sourceNode)),
                    color: getContrastTextColor(getCategoryColor(getNodeCategory(relationshipData.sourceNode)))
                  }}>
                    {getNodeCategory(relationshipData.sourceNode)}
                  </span>
                ) : (
                  <span className="node-type-modern">({getNodeType(relationshipData.sourceNode)})</span>
                )}
              </div>
            ) : (
              <div className="relationship-node-modern">
                <div className="node-content-modern">
                  <span className="arrow-modern">←</span>
                  <span className="node-name-modern">{relationshipData.source}</span>
                </div>
              </div>
            )}
          </div>

          {/* Target Node */}
          <div className="node-connection-modern">
            <h5 className="connection-label-modern">Target</h5>
            {relationshipData.targetNode ? (
              <div 
                className="relationship-node-modern clickable"
                onClick={() => handleNodeClick(relationshipData.targetNode)}
                title={`Click to select: ${getDisplayName(relationshipData.targetNode)}`}
              >
                <div className="node-content-modern">
                  <span className="arrow-modern">→</span>
                  <span className="node-name-modern">{getDisplayName(relationshipData.targetNode)}</span>
                </div>
                {getNodeCategory(relationshipData.targetNode) ? (
                  <span className="node-category-badge-modern" style={{
                    backgroundColor: getCategoryColor(getNodeCategory(relationshipData.targetNode)),
                    color: getContrastTextColor(getCategoryColor(getNodeCategory(relationshipData.targetNode)))
                  }}>
                    {getNodeCategory(relationshipData.targetNode)}
                  </span>
                ) : (
                  <span className="node-type-modern">({getNodeType(relationshipData.targetNode)})</span>
                )}
              </div>
            ) : (
              <div className="relationship-node-modern">
                <div className="node-content-modern">
                  <span className="arrow-modern">→</span>
                  <span className="node-name-modern">{relationshipData.target}</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Relationship Type Information */}
        <div className="section-card-modern">
          <div className="section-header-card">
            <div className="section-icon-title">
              <span className="section-icon">ℹ️</span>
              <h3 className="section-title-card">Relationship Information</h3>
            </div>
          </div>
          <div className="properties-content-card">
            <div className="expandable-text-container">
              <div className="property-name-green">Type</div>
              <div className="property-value-expandable">
                <div className="text-content">{relationshipData.type}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="panel-footer-modern">
        <p className="hint-modern">Press ESC to return to graph view</p>
      </div>
    </div>
  );
}

export default RelationshipDetails;
