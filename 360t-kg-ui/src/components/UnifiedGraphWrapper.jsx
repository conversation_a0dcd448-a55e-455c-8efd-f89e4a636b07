import React, { useCallback, useMemo, useRef, useEffect, useState } from 'react';
import ForceGraph2D from 'react-force-graph-2d';
import * as d3 from 'd3';
import { smartLimitGraphNodes } from '../utils/graphLimiting';
import { 
  TECHNICAL_TYPE_COLORS,
  DEFAULT_2D_NODE_SIZES,
  DEFAULT_RELATIONSHIP_COLORS
} from '../constants/graphDefaults.js';
import {
  getNodeType,
  getNodeName,
  getNodeColor,
  getNodeSize,
  getLinkColor,
  getNodeTooltip
} from '../utils/nodeUtils.js';

// Default configurations from centralized constants
const defaultNodeColors = TECHNICAL_TYPE_COLORS;
const defaultNodeSizes = DEFAULT_2D_NODE_SIZES;

const defaultNodeShapes = {
  // Map actual node types from the data to appropriate SVG icons
  'Entity': 'svg:business-connection-connect-communication-teamwork-people-svgrepo-com.svg',
  'Parameter': 'svg:configuration-gear-options-preferences-settings-system-svgrepo-com.svg',
  'Module': 'svg:system-svgrepo-com.svg',
  'Configuration': 'svg:configuration-gear-options-preferences-settings-system-svgrepo-com.svg',
  'Feature': 'svg:diploma-verified-svgrepo-com.svg',
  'Workflow': 'svg:workflow-svgrepo-com.svg',
  'Role': 'svg:user-alt-1-svgrepo-com.svg',
  'Product': 'svg:finance-department-trader-trading-cfo-svgrepo-com.svg',
  // Legacy types for backward compatibility
  'UI_Area': 'circle',
  'ConfigurationItem': 'star',
  'TestCase': 'wye',
  'Document': 'svg:finance-book-svgrepo-com.svg',
  'Default': 'circle'
};

const defaultRelationshipColors = DEFAULT_RELATIONSHIP_COLORS;

// Shape mapping for canvas drawing
const shapeMap = {
  'circle': 'circle',
  'square': 'square',
  'triangle': 'triangle',
  'diamond': 'diamond',
  'star': 'star',
  'wye': 'wye',
  'cross': 'cross'
};

/**
 * Unified Graph Wrapper component using React Force Graph
 * Replaces the D3.js-based GraphView component
 */
const UnifiedGraphWrapper = ({
  data,
  selectedNode, // Accept selectedNode as prop from parent
  onNodeSelect,
  onRelationshipSelect = () => {}, // Callback for relationship selection
  customConfig = {},
  nodeColors = defaultNodeColors,
  nodeSizes = defaultNodeSizes,
  onCenterOnNodeReady = () => {}, // Callback to provide centerOnNode function to parent
  onNodeClick = () => {}, // Callback for additional node click handling (e.g., close search dropdown)
  nodeShapes = defaultNodeShapes,
  relationshipColors = defaultRelationshipColors,
  relationshipLineStyles = {},
  relationshipTypeFilter = null, // Array of relationship types to show, null means show all
  isDetailView = false, // Flag to indicate if we're in detailed view mode
  openGraphitiSettings = () => {} // Callback to open Graphiti settings panel
}) => {
  // Focus Mode v1: derive a quick-access lookup of connected ids when a node is selected
  const connectedLookup = useMemo(() => {
    if (!selectedNode || !data?.links) return null;
    const selId = selectedNode.id?.toString() || selectedNode.data?.id?.toString();
    if (!selId) return null;

    const set = new Set([selId]);
    (data.links || []).forEach(l => {
      const s = (l.source?.id ?? l.source ?? l.from)?.toString();
      const t = (l.target?.id ?? l.target ?? l.to)?.toString();
      if (s === selId && t) set.add(t);
      if (t === selId && s) set.add(s);
    });
    return set;
  }, [selectedNode, data?.links]);
  // Debug logging removed - infinite re-rendering issue resolved
  const fgRef = useRef();
  // const [selectedNode, setSelectedNode] = useState(null); // Removed - using prop from parent
  // Removed hoveredNode and hoveredLink state to prevent unnecessary re-renders
  // that were causing zoom-freeze-repeat cycle
  const [iconCache, setIconCache] = useState(new Map());
  // Use ref instead of state for tooltip timeout to avoid re-renders
  const tooltipTimeoutRef = useRef(null);



  // Memoize configuration objects with customConfig override support
  const memoizedNodeColors = useMemo(() => ({
    ...nodeColors,
    ...customConfig.colors
  }), [nodeColors, customConfig.colors]);

  const memoizedNodeSizes = useMemo(() => ({
    ...nodeSizes,
    ...customConfig.sizes
  }), [nodeSizes, customConfig.sizes]);

  const memoizedNodeShapes = useMemo(() => ({
    ...nodeShapes,
    ...customConfig.shapes
  }), [nodeShapes, customConfig.shapes]);

  const memoizedRelationshipColors = useMemo(() => ({
    ...relationshipColors,
    ...customConfig.relationshipColors
  }), [relationshipColors, customConfig.relationshipColors]);

  const memoizedRelationshipLineStyles = useMemo(() => ({
    ...relationshipLineStyles,
    ...customConfig.relationshipLineStyles
  }), [relationshipLineStyles, customConfig.relationshipLineStyles]);

  // Helper functions using centralized utilities


  const getNodeColorForComponent = useCallback((node) => {
    return getNodeColor(node, { nodeColors: memoizedNodeColors });
  }, [memoizedNodeColors]);

  const getNodeSizeForComponent = useCallback((node) => {
    return getNodeSize(node, false, { nodeSizes: memoizedNodeSizes });
  }, [memoizedNodeSizes]);

  const getNodeShape = useCallback((node) => {
    const nodeType = getNodeType(node);
    return memoizedNodeShapes[nodeType] || memoizedNodeShapes['Default'] || 'circle';
  }, [memoizedNodeShapes, getNodeType]);

  const getLinkColorForComponent = useCallback((link) => {
    return getLinkColor(link, { relationshipColors: memoizedRelationshipColors });
  }, [memoizedRelationshipColors]);

  const getLinkDashArray = useCallback((link) => {
    const linkType = link.type || link.label || 'Default';
    const style = memoizedRelationshipLineStyles[linkType];
    if (style === 'dashed') return [5, 5];
    if (style === 'dotted') return [2, 2];
    return []; // Solid line
  }, [memoizedRelationshipLineStyles]);

  // Fixed adaptive text scaling for proper zoom behavior - reduced text size
  const getAdaptiveTextSize = useCallback((globalScale, type = 'node-label') => {
    // Corrected scaling logic: larger text when zoomed in, smaller when zoomed out
    if (type === 'node-label') {
      // Reduced text size for less cluttered view: 3 * globalScale instead of 5
      return Math.max(3, Math.min(12, 3 * globalScale)); // Range: 3px to 12px (reduced from 4-16px)
    } else if (type === 'link-label') {
      return Math.max(2, Math.min(10, 3 * globalScale)); // Range: 2px to 10px (reduced from 3-14px)
    }
    return Math.max(3, Math.min(12, 3 * globalScale)); // Default to reduced node-label sizing
  }, []);

  // Enhanced adaptive line width scaling matching original behavior
  const getAdaptiveLineWidth = useCallback((baseWidth, globalScale) => {
    // Match the stroke-width scaling from original: 1.5 / k
    return Math.max(0.5, baseWidth / globalScale);
  }, []);

  // Tooltip helper functions matching original GraphView behavior
  const isDocumentNode = useCallback((node) => {
    return node.type === 'Document' || node.labels?.includes('Document');
  }, []);

  const isEntityNode = useCallback((node) => {
    return node.type === 'Entity' || node.labels?.includes('Entity');
  }, []);

  const getDocumentTooltipContent = useCallback((node) => {
    if (!isDocumentNode(node)) return null;
    return node.name || node.title || 'Document';
  }, [isDocumentNode]);

  const getEntityTooltipContent = useCallback((node) => {
    if (!isEntityNode(node)) return null;
    // Priority: full summary > name > fallback
    const summary = node.data?.properties?.summary || node.properties?.summary || node.summary;
    if (summary && summary.trim()) {
      return summary;
    }
    return node.name || node.data?.name || 'Entity';
  }, [isEntityNode]);

  const getRelationshipTooltipContent = useCallback((link) => {
    // Priority: fact property > label > type > fallback
    const fact = link.data?.properties?.fact || link.properties?.fact || link.fact;
    if (fact && fact.trim()) {
      return fact;
    }
    return link.label || link.type || 'Relationship';
  }, []);

  // React state-based tooltip management (consistent with 3D implementation)
  const [tooltip, setTooltip] = useState({ visible: false, content: '', x: 0, y: 0, type: 'node' });

  const showTooltip = useCallback((content, x, y, type = 'node') => {
    // Clear any existing timeout
    if (tooltipTimeoutRef.current) {
      clearTimeout(tooltipTimeoutRef.current);
    }

    const timeout = setTimeout(() => {
      setTooltip({
        visible: true,
        content,
        x: x + 10,
        y: y - 10,
        type
      });
    }, 500); // Reduced delay from 2000ms to 500ms for better UX

    tooltipTimeoutRef.current = timeout;
  }, []);

  const hideTooltip = useCallback(() => {
    // Clear timeout
    if (tooltipTimeoutRef.current) {
      clearTimeout(tooltipTimeoutRef.current);
      tooltipTimeoutRef.current = null;
    }

    setTooltip({ visible: false, content: '', x: 0, y: 0, type: 'node' });
  }, []);

  const updateTooltipPosition = useCallback((x, y) => {
    const tooltip = document.querySelector('.graph-tooltip');
    if (tooltip) {
      tooltip.style.left = `${x + 10}px`;
      tooltip.style.top = `${y - 10}px`;
    }
  }, []);

  // Transform data for React Force Graph format
  const forceGraphData = useMemo(() => {
    const startTime = performance.now();
    console.log('🔄 Starting graph data transformation...');

    if (!data || !data.nodes || !data.links) {
      console.log('❌ No data available, returning empty graph');
      return { nodes: [], links: [] };
    }

    // Apply node limit for performance management using smart limiting
    const nodeLimit = customConfig.nodeLimit || 0; // 0 means no limit
    const limitedData = nodeLimit > 0 && data.nodes.length > nodeLimit
      ? smartLimitGraphNodes(data, nodeLimit)
      : { nodes: data.nodes, links: data.links || [], statistics: { wasLimited: false } };
    
    const limitedNodes = limitedData.nodes;

    // Apply node type visibility filtering
    const nodeTypeVisibility = customConfig.nodeTypeVisibility || {};
    const visibilityFilteredNodes = limitedNodes.filter(node => {
      const nodeType = getNodeType(node);
      // If visibility is not set for this node type, default to visible (true)
      const isVisible = nodeTypeVisibility[nodeType] !== false;
      if (!isVisible) {
        console.log(`🔍 Filtering out node type: ${nodeType}, node: ${node.id}`);
      }
      return isVisible;
    });

    console.log(`🔍 Node visibility filtering: ${data.nodes.length} → ${limitedNodes.length} (after limit) → ${visibilityFilteredNodes.length} (after visibility filter)`);

    const transformedNodes = visibilityFilteredNodes.map(node => {
      const nodeType = getNodeType(node);
      return {
        id: node.id?.toString(),
        name: getNodeName(node),
        group: nodeType,
        color: getNodeColorForComponent(node),
        val: Math.sqrt(getNodeSizeForComponent(node)) * 2, // Adjust size for force graph
        shape: getNodeShape(node),
        data: node // Preserve original data
      };
    });

    // Create a set of valid node IDs for link filtering
    const validNodeIds = new Set(transformedNodes.map(node => node.id));

    // Filter links to only include those connecting visible nodes and matching relationship type filter
    const filteredLinks = limitedData.links.filter(link => {
      const sourceId = link.source?.toString() || link.from?.toString();
      const targetId = link.target?.toString() || link.to?.toString();
      
      // Basic validation: must have valid source and target
      if (!validNodeIds.has(sourceId) || !validNodeIds.has(targetId)) {
        return false;
      }
      
      // Apply relationship type filtering if specified
      if (relationshipTypeFilter && Array.isArray(relationshipTypeFilter)) {
        const linkType = link.type || link.label || 'RELATES_TO'; // Default to RELATES_TO if no type
        return relationshipTypeFilter.includes(linkType);
      }
      
      return true;
    });

    const transformedLinks = filteredLinks.map(link => ({
      source: link.source?.toString() || link.from?.toString(),
      target: link.target?.toString() || link.to?.toString(),
      label: link.label || link.type || '',
      type: link.type || link.label || 'RELATES_TO', // Preserve relationship type
      color: getLinkColorForComponent(link),
      width: 1.5,
      dashArray: getLinkDashArray(link),
      data: link // Preserve original data
    }));

    const result = {
      nodes: transformedNodes,
      links: transformedLinks
    };

    const endTime = performance.now();
    const transformTime = endTime - startTime;
    const limitMessage = nodeLimit > 0 && data.nodes.length > nodeLimit
      ? ` (limited from ${data.nodes.length})`
      : '';
    console.log(`✅ Graph data transformation completed in ${transformTime.toFixed(2)}ms: ${result.nodes.length} nodes${limitMessage}, ${result.links.length} links`);

    return result;
  }, [data, customConfig.nodeLimit, customConfig.nodeTypeVisibility, getNodeColorForComponent, getNodeSizeForComponent, getNodeShape, getLinkColorForComponent]);



  const centerOnNode = useCallback((node) => {
    if (fgRef.current && node) {
      try {
        console.log('🎯 Centering on node:', { 
          id: node.id, 
          name: node.name, 
          type: typeof node, 
          hasCoordinates: typeof node.x === 'number' && typeof node.y === 'number' 
        });

        // Handle case where an event object might be passed instead of a node
        if (node.target || node.currentTarget || typeof node === 'object' && node.constructor.name === 'SyntheticBaseEvent') {
          console.error('❌ Event object passed instead of node object:', node);
          return;
        }

        // Get node ID for lookup
        const nodeId = node.id?.toString() || node.data?.id?.toString();

        if (!nodeId) {
          console.warn('❌ Node has no ID for centering:', node);
          return;
        }

        // Try to find the node in the current graph data to get coordinates
        const currentGraphData = fgRef.current.graphData();
        let graphNode = null;

        if (currentGraphData && currentGraphData.nodes) {
          graphNode = currentGraphData.nodes.find(n => n.id?.toString() === nodeId);
        }

        // If not found in graph, use the original node but it might lack coordinates
        if (!graphNode) {
          console.log('⚠️ Node not found in current graph, using search result node:', nodeId);
          graphNode = node;
        } else {
          console.log('✅ Found node in current graph with coordinates:', graphNode);
        }

        // Check if the node has valid coordinates
        if (typeof graphNode.x === 'number' && typeof graphNode.y === 'number' &&
            !isNaN(graphNode.x) && !isNaN(graphNode.y)) {

          console.log('🎯 Centering at coordinates:', graphNode.x, graphNode.y);

          // Stop any ongoing animations first
          fgRef.current.pauseAnimation();

          // Center the camera on the node with smooth animation
          fgRef.current.centerAt(graphNode.x, graphNode.y, 1500);

          // Set a comfortable zoom level
          setTimeout(() => {
            const currentZoom = fgRef.current.zoom();
            if (currentZoom < 1.2) {
              fgRef.current.zoom(1.8, 800); // Zoom in with 800ms animation
            } else if (currentZoom > 4) {
              fgRef.current.zoom(2.5, 800); // Zoom out if too close
            }

            // Resume animation after centering
            setTimeout(() => {
              fgRef.current.resumeAnimation();
            }, 100);
          }, 300);

          // Highlight the node temporarily
          onNodeSelect(graphNode);
          setTimeout(() => {
            onNodeSelect(null);
          }, 3000); // Remove highlight after 3 seconds

          console.log('✅ Node centering completed');
        } else {
          console.warn('❌ Node missing coordinates, attempting to add to graph first:', { 
            nodeId, 
            nodeType: typeof graphNode,
            x: graphNode?.x, 
            y: graphNode?.y 
          });
          
          // If the node doesn't have coordinates, it might not be in the current graph
          // Try to add it to the graph first, then center on it
          if (currentGraphData && currentGraphData.nodes) {
            // Create a node object with basic positioning
            const nodeToAdd = {
              ...graphNode,
              id: nodeId,
              // Place it at a random position near the center, force simulation will position it properly
              x: (Math.random() - 0.5) * 100,
              y: (Math.random() - 0.5) * 100,
            };

            console.log('🔄 Adding node to graph for centering:', nodeToAdd);

            // Add the node to the graph if it's not already there
            const updatedNodes = [...currentGraphData.nodes];
            if (!updatedNodes.find(n => n.id?.toString() === nodeId)) {
              updatedNodes.push(nodeToAdd);
              fgRef.current.graphData({ 
                nodes: updatedNodes, 
                links: currentGraphData.links || [] 
              });
            }

            // Wait for the force simulation to position the node, then center on it
            setTimeout(() => {
              const repositionedNode = fgRef.current.graphData().nodes.find(n => n.id?.toString() === nodeId);
              if (repositionedNode && typeof repositionedNode.x === 'number' && typeof repositionedNode.y === 'number') {
                console.log('🔄 Retrying with simulation-positioned coordinates:', repositionedNode.x, repositionedNode.y);
                fgRef.current.centerAt(repositionedNode.x, repositionedNode.y, 1500);
                onNodeSelect(repositionedNode);
                setTimeout(() => onNodeSelect(null), 3000);
              } else {
                console.warn('❌ Node still missing coordinates after adding to graph and waiting for simulation');
              }
            }, 1500); // Wait longer for simulation to position the node
          }
        }
      } catch (error) {
        console.error('❌ Error centering on node:', error);
      }
    }
  }, []); // Remove forceGraphData dependency to prevent infinite re-renders

  // Provide centerOnNode function to parent component
  useEffect(() => {
    if (onCenterOnNodeReady) {
      onCenterOnNodeReady(centerOnNode);
    }
  }, [centerOnNode, onCenterOnNodeReady]);

  // Enhanced SVG icon loading function with retry logic
  const loadSVGAsImage = useCallback((svgPath, retries = 3) => {
    return new Promise((resolve, reject) => {
      const img = new Image();

      img.onload = () => {
        console.log(`Successfully loaded SVG icon: ${svgPath}`);
        resolve(img);
      };

      img.onerror = () => {
        if (retries > 0) {
          console.warn(`Failed to load SVG icon: ${svgPath}, retrying... (${retries} attempts left)`);
          setTimeout(() => {
            loadSVGAsImage(svgPath, retries - 1).then(resolve).catch(reject);
          }, 1000);
        } else {
          console.error(`Failed to load SVG icon after all retries: ${svgPath}`);
          reject(new Error(`Failed to load SVG: ${svgPath}`));
        }
      };

      // Add crossOrigin for better compatibility
      img.crossOrigin = 'anonymous';
      img.src = svgPath;
    });
  }, []);

  // Pre-load SVG icons with enhanced error handling
  useEffect(() => {
    const loadIcons = async () => {
      const iconsToLoad = new Set(); // Use Set to avoid duplicates

      // Find all SVG shapes in use from nodeShapes configuration
      Object.values(memoizedNodeShapes).forEach(shape => {
        if (typeof shape === 'string' && shape.startsWith('svg:')) {
          const svgFile = shape.substring(4);
          iconsToLoad.add(svgFile);
        }
      });

      // SVG shapes are loaded based on configuration only
      // Individual node shapes will be handled during rendering

      if (iconsToLoad.size > 0) {
        console.log(`Loading ${iconsToLoad.size} SVG icons:`, Array.from(iconsToLoad));
      }

      // Load each icon with error handling
      const loadPromises = Array.from(iconsToLoad).map(async (svgFile) => {
        try {
          const img = await loadSVGAsImage(`/svg/${svgFile}`);
          setIconCache(prev => new Map(prev.set(svgFile, img)));
          return { svgFile, success: true };
        } catch (error) {
          console.warn(`Failed to load SVG icon: ${svgFile}`, error);
          // Set a placeholder in cache to avoid repeated attempts
          setIconCache(prev => new Map(prev.set(svgFile, null)));
          return { svgFile, success: false, error };
        }
      });

      // Wait for all icons to load (or fail)
      const results = await Promise.allSettled(loadPromises);
      const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
      const failed = results.length - successful;

      console.log(`Icon loading complete: ${successful} successful, ${failed} failed`);
    };

    loadIcons();
  }, [memoizedNodeShapes, loadSVGAsImage]); // Removed forceGraphData to prevent circular dependency

  // Cleanup tooltips on unmount
  useEffect(() => {
    return () => {
      hideTooltip();
    };
  }, [hideTooltip]);

  // Enhanced shape drawing functions
  const drawNodeShape = useCallback((ctx, x, y, radius, shapeName) => {
    ctx.beginPath();

    switch (shapeName) {
      case 'circle':
        ctx.arc(x, y, radius, 0, 2 * Math.PI);
        break;

      case 'square':
        ctx.rect(x - radius, y - radius, radius * 2, radius * 2);
        break;

      case 'triangle':
        ctx.moveTo(x, y - radius);
        ctx.lineTo(x - radius * 0.866, y + radius * 0.5);
        ctx.lineTo(x + radius * 0.866, y + radius * 0.5);
        ctx.closePath();
        break;

      case 'diamond':
        ctx.moveTo(x, y - radius);
        ctx.lineTo(x + radius, y);
        ctx.lineTo(x, y + radius);
        ctx.lineTo(x - radius, y);
        ctx.closePath();
        break;

      case 'star':
        const spikes = 5;
        const outerRadius = radius;
        const innerRadius = radius * 0.4;
        let rot = Math.PI / 2 * 3;
        const step = Math.PI / spikes;

        ctx.moveTo(x, y - outerRadius);
        for (let i = 0; i < spikes; i++) {
          ctx.lineTo(x + Math.cos(rot) * outerRadius, y + Math.sin(rot) * outerRadius);
          rot += step;
          ctx.lineTo(x + Math.cos(rot) * innerRadius, y + Math.sin(rot) * innerRadius);
          rot += step;
        }
        ctx.lineTo(x, y - outerRadius);
        ctx.closePath();
        break;

      case 'wye':
        // Y-shaped symbol
        ctx.moveTo(x, y - radius);
        ctx.lineTo(x, y);
        ctx.moveTo(x, y);
        ctx.lineTo(x - radius * 0.7, y + radius * 0.7);
        ctx.moveTo(x, y);
        ctx.lineTo(x + radius * 0.7, y + radius * 0.7);
        break;

      case 'cross':
        ctx.moveTo(x - radius, y);
        ctx.lineTo(x + radius, y);
        ctx.moveTo(x, y - radius);
        ctx.lineTo(x, y + radius);
        break;

      default:
        // Default to circle
        ctx.arc(x, y, radius, 0, 2 * Math.PI);
        break;
    }
  }, []);

  // Custom node painting function with enhanced rendering
  const paintNode = useCallback((node, ctx, globalScale) => {
    // Focus dimming: if we have a selection, dim nodes not connected to it
    const shouldDim = connectedLookup && !connectedLookup.has(node.id);
    if (shouldDim) {
      ctx.globalAlpha = 0.25;
    }
    const nodeRadius = Math.sqrt(node.val || 1) * 4;
    const shapeName = node.shape || 'circle';
    const isSelected = selectedNode?.id === node.id;
    // Removed isHovered since hoveredNode state was removed to prevent re-renders
    const isHovered = false;



    // Handle SVG icons
    if (shapeName.startsWith('svg:')) {
      const svgFile = shapeName.substring(4);
      const cachedImage = iconCache.get(svgFile);

      if (cachedImage) {
        const size = nodeRadius * 2;

        // Draw shadow for selected/hovered nodes
        if (isSelected || isHovered) {
          ctx.shadowColor = isSelected ? '#00973A' : '#6b7280';
          ctx.shadowBlur = 8;
          ctx.shadowOffsetX = 2;
          ctx.shadowOffsetY = 2;
        }

        ctx.drawImage(cachedImage, node.x - size/2, node.y - size/2, size, size);

        // Reset shadow
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;

        // Draw selection border for SVG icons
        if (isSelected || isHovered) {
          ctx.beginPath();
          ctx.arc(node.x, node.y, nodeRadius + 2, 0, 2 * Math.PI);
          ctx.strokeStyle = isSelected ? '#00973A' : '#6b7280';
          ctx.lineWidth = 2;
          ctx.stroke();
        }
      } else {
        // Draw enhanced placeholder for failed/loading SVG icons
        ctx.beginPath();
        ctx.arc(node.x, node.y, nodeRadius, 0, 2 * Math.PI);
        ctx.fillStyle = node.color || '#6b7280';
        ctx.fill();
        ctx.strokeStyle = '#374151';
        ctx.lineWidth = 1;
        ctx.stroke();

        // Add a small icon indicator in the center
        const iconSize = nodeRadius * 0.6;
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(
          node.x - iconSize / 2,
          node.y - iconSize / 2,
          iconSize,
          iconSize
        );

        // Add a simple document/file icon shape
        ctx.fillStyle = '#374151';
        ctx.fillRect(
          node.x - iconSize / 3,
          node.y - iconSize / 3,
          iconSize * 0.6,
          iconSize * 0.8
        );
      }
    } else {
      // Draw geometric shapes
      drawNodeShape(ctx, node.x, node.y, nodeRadius, shapeName);

      // Fill the shape
      ctx.fillStyle = node.color || '#6b7280';
      ctx.fill();

      // Draw border with selection highlighting
      ctx.strokeStyle = isSelected ? '#00973A' : (isHovered ? '#6b7280' : '#374151');
      ctx.lineWidth = isSelected ? 3 : (isHovered ? 2 : 1);
      ctx.stroke();
    }

    // Adaptive text rendering with improved positioning
    const textSize = getAdaptiveTextSize(globalScale, 'node-label');
    // If dimmed, we reduce label prominence as well
    const labelAlpha = shouldDim ? 0.35 : 1.0;

    // Adaptive text scaling working correctly - debug logging removed

    // Only render text if we have valid coordinates and other conditions are met
    if (textSize >= 4 && node.name && typeof node.x === 'number' && typeof node.y === 'number' && !isNaN(node.x) && !isNaN(node.y)) {
      const maxTextWidth = nodeRadius * 3; // Limit text width
      const truncatedName = node.name.length > 15 ? node.name.substring(0, 12) + '...' : node.name;

      ctx.font = `${textSize}px -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif`;
      const textMetrics = ctx.measureText(truncatedName);
      const textWidth = Math.min(textMetrics.width, maxTextWidth);
      const textHeight = textSize;

      // Position text below the node
      const textY = node.y + nodeRadius + textHeight + 5;

      // Draw text background with reduced opacity for better aesthetics
      ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
      ctx.fillRect(
        node.x - textWidth / 2 - 4,
        textY - textHeight / 2 - 2,
        textWidth + 8,
        textHeight + 4
      );

      // Draw text border
      ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)';
      ctx.lineWidth = 0.5;
      ctx.stroke();

      // Draw text
      ctx.fillStyle = `rgba(31,41,55,${labelAlpha})`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';



      ctx.fillText(truncatedName, node.x, textY);

      // reset global alpha if changed
      if (shouldDim) ctx.globalAlpha = 1;
    }
  }, [selectedNode, iconCache, getAdaptiveTextSize, drawNodeShape]); // Removed hoveredNode dependency

  // Enhanced link painting function with dash patterns and better labels
  const paintLink = useCallback((link, ctx, globalScale) => {
    // Focus dimming for links: only keep full opacity if either end is in the connected set
    let shouldDim = false;
    if (connectedLookup) {
      const s = (link.source?.id ?? link.source)?.toString();
      const t = (link.target?.id ?? link.target)?.toString();
      if (!connectedLookup.has(s) && !connectedLookup.has(t)) {
        shouldDim = true;
        ctx.globalAlpha = 0.25;
      }
    }
    const start = link.source;
    const end = link.target;

    // Calculate link properties
    const dx = end.x - start.x;
    const dy = end.y - start.y;
    const distance = Math.sqrt(dx * dx + dy * dy);

    if (distance < 1) return; // Skip very short links

    // Normalize direction vector
    const unitX = dx / distance;
    const unitY = dy / distance;

    // Adjust start and end points to account for node radius
    const nodeRadius = 15; // Approximate node radius
    const adjustedStartX = start.x + unitX * nodeRadius;
    const adjustedStartY = start.y + unitY * nodeRadius;
    const adjustedEndX = end.x - unitX * nodeRadius;
    const adjustedEndY = end.y - unitY * nodeRadius;

    // Draw link line with enhanced styling
    ctx.beginPath();
    ctx.moveTo(adjustedStartX, adjustedStartY);
    ctx.lineTo(adjustedEndX, adjustedEndY);

    // Set line style based on link properties with proper scaling
    ctx.strokeStyle = link.color || '#9ca3af';
    ctx.lineWidth = getAdaptiveLineWidth(link.width || 1.5, globalScale);

    // Handle dash patterns based on relationship line styles configuration
    const dashArray = getLinkDashArray(link);
    ctx.setLineDash(dashArray);

    ctx.stroke();

    // Reset alpha after drawing the line
    if (shouldDim) ctx.globalAlpha = 1;

    // Reset line dash for other drawing operations
    ctx.setLineDash([]);

    // Draw arrowhead for directed relationships
    if (distance > 30) { // Only draw arrows for longer links
      const arrowSize = Math.min(8, distance * 0.1);
      const arrowX = adjustedEndX - unitX * arrowSize;
      const arrowY = adjustedEndY - unitY * arrowSize;

      // Calculate perpendicular vector for arrow wings
      const perpX = -unitY * arrowSize * 0.5;
      const perpY = unitX * arrowSize * 0.5;

      ctx.beginPath();
      ctx.moveTo(adjustedEndX, adjustedEndY);
      ctx.lineTo(arrowX + perpX, arrowY + perpY);
      ctx.moveTo(adjustedEndX, adjustedEndY);
      ctx.lineTo(arrowX - perpX, arrowY - perpY);
      ctx.strokeStyle = link.color || '#9ca3af';
      ctx.lineWidth = Math.max(1, ctx.lineWidth * 0.8);
      ctx.stroke();
    }

    // Draw relationship text label - check properties first for actual relationship name
    const linkLabel = link.data?.properties?.name || link.data?.properties?.fact || link.data?.name || link.label || link.type || '';
    if (linkLabel && distance > 50) { // Only show labels on longer links to avoid clutter
      // Calculate midpoint for label positioning
      const midX = (adjustedStartX + adjustedEndX) / 2;
      const midY = (adjustedStartY + adjustedEndY) / 2;

      // Set text properties
      ctx.fillStyle = link.color || '#6b7280';
      ctx.font = `${getAdaptiveTextSize(globalScale, 'link-label')}px Arial`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      // Add text background for better readability
      const textMetrics = ctx.measureText(linkLabel);
      const textWidth = textMetrics.width;
      const textHeight = getAdaptiveTextSize(globalScale, 'link-label');
      const padding = 2;

      // Draw background rectangle
      ctx.fillStyle = 'rgba(248, 250, 252, 0.8)'; // Semi-transparent background
      ctx.fillRect(
        midX - textWidth / 2 - padding,
        midY - textHeight / 2 - padding,
        textWidth + padding * 2,
        textHeight + padding * 2
      );

      // Draw text
      ctx.fillStyle = (shouldDim ? 'rgba(107,114,128,0.5)' : (link.color || '#6b7280'));
      ctx.fillText(linkLabel, midX, midY);
    }
  }, [getAdaptiveTextSize, getLinkDashArray, getAdaptiveLineWidth]);

  // Zoom helpers
  const zoomBy = useCallback((factor = 1.2) => {
    if (!fgRef.current) return;
    const current = fgRef.current.zoom();
    fgRef.current.zoom(Math.max(0.1, Math.min(8, current * factor)), 400);
  }, []);

  const fitToSelection = useCallback(() => {
    if (!fgRef.current) return;
    try {
      // If a node is selected, center on it; otherwise fit to graph bounds
      if (selectedNode && typeof selectedNode.x === 'number' && typeof selectedNode.y === 'number') {
        fgRef.current.centerAt(selectedNode.x, selectedNode.y, 800);
        const currentZoom = fgRef.current.zoom();
        if (currentZoom < 1.2) fgRef.current.zoom(1.6, 600);
      } else {
        fgRef.current.zoomToFit(800, 50, n => true);
      }
    } catch (e) {
      console.warn('Fit/Center failed:', e);
    }
  }, [selectedNode]);

  // Simple navigate helper for Help button
  const openDocs = useCallback(() => {
    try {
      // Adjust path if your docs are served under another route
      window.open('/docs', '_self');
    } catch {
      // Fallback to repo docs
      window.open('/docs/ui-design-specs/README.md', '_self');
    }
  }, []);

  return (
    <div className="app-shell">
      {/* Removed inline settings/help icon-only buttons per request */}

      {/* Floating zoom control */}
      <div style={{
        position: 'absolute',
        right: 12,
        top: 12,
        zIndex: 10,
        display: 'flex',
        flexDirection: 'column',
        gap: 8
      }}>
        <button
          onClick={() => zoomBy(1.2)}
          title="Zoom in"
          style={{
            width: 36, height: 36, borderRadius: 8, border: '1px solid #e2e8f0',
            background: '#ffffff', boxShadow: '0 1px 2px rgba(0,0,0,0.08)', cursor: 'pointer'
          }}
          aria-label="Zoom in"
        >＋</button>
        <button
          onClick={() => zoomBy(1/1.2)}
          title="Zoom out"
          style={{
            width: 36, height: 36, borderRadius: 8, border: '1px solid #e2e8f0',
            background: '#ffffff', boxShadow: '0 1px 2px rgba(0,0,0,0.08)', cursor: 'pointer'
          }}
          aria-label="Zoom out"
        >－</button>
        <button
          onClick={fitToSelection}
          title="Fit to selection"
          style={{
            width: 36, height: 36, borderRadius: 8, border: '1px solid #e2e8f0',
            background: '#ffffff', boxShadow: '0 1px 2px rgba(0,0,0,0.08)', cursor: 'pointer',
            fontSize: 10, lineHeight: '36px'
          }}
          aria-label="Fit to selection"
        >⤢</button>
        <button
          onClick={() => selectedNode && centerOnNode(selectedNode)}
          disabled={!selectedNode}
          title="Center selected"
          style={{
            width: 36, height: 36, borderRadius: 8, border: '1px solid #e2e8f0',
            background: selectedNode ? '#ffffff' : '#f1f5f9',
            color: '#111827',
            boxShadow: '0 1px 2px rgba(0,0,0,0.08)', cursor: selectedNode ? 'pointer' : 'not-allowed',
            fontSize: 12, lineHeight: '36px'
          }}
          aria-label="Center selected node"
        >◎</button>
      </div>

      <ForceGraph2D
        ref={fgRef}
        graphData={forceGraphData}
        nodeId="id"
        nodeLabel={() => ''} // We handle labels in canvas
        nodeColor="color"
        nodeVal="val"
        nodeCanvasObject={paintNode}
        onNodeClick={(node, event) => {
          if (event) event.stopPropagation();
          onNodeSelect(node.data || node); // Notify parent of selection
          onNodeClick(node.data || node); // Call additional click handler (e.g., close search dropdown)
          hideTooltip(); // Hide tooltip on click
        }}
        onNodeHover={(node, prevNode, event) => {
          // Removed setHoveredNode to prevent unnecessary re-renders

          // Safely access canvas for cursor styling
          if (fgRef.current && fgRef.current.canvas) {
            fgRef.current.canvas.style.cursor = node ? 'pointer' : 'default';
          }

          if (node) {
            // Show tooltip for hovered node
            let tooltipContent = '';
            if (isDocumentNode(node)) {
              tooltipContent = getDocumentTooltipContent(node);
            } else if (isEntityNode(node)) {
              tooltipContent = getEntityTooltipContent(node);
            } else {
              tooltipContent = node.name || node.id || 'Node';
            }

            // Use mouse event coordinates for proper tooltip positioning
            if (tooltipContent && event) {
              try {
                // Use the actual mouse coordinates from the event
                const mouseX = event.clientX || event.pageX || 0;
                const mouseY = event.clientY || event.pageY || 0;
                showTooltip(tooltipContent, mouseX, mouseY, 'node');
              } catch (error) {
                console.warn('Error positioning node tooltip:', error);
                // Fallback to canvas-based positioning if event coordinates fail
                if (fgRef.current && fgRef.current.canvas) {
                  const canvas = fgRef.current.canvas;
                  if (canvas && typeof canvas.getBoundingClientRect === 'function') {
                    const rect = canvas.getBoundingClientRect();
                    // Convert graph coordinates to screen coordinates
                    const screenCoords = fgRef.current.graph2ScreenCoords(node.x, node.y);
                    const mouseX = rect.left + screenCoords.x;
                    const mouseY = rect.top + screenCoords.y;
                    showTooltip(tooltipContent, mouseX, mouseY, 'node');
                  }
                }
              }
            }
          } else {
            hideTooltip();
          }
        }}
        onNodeDrag={(node) => {
          hideTooltip(); // Hide tooltip during drag
        }}
        onNodeDragEnd={(node) => {
          // Node drag ended - could add custom logic here
        }}
        linkSource="source"
        linkTarget="target"
        linkLabel="label"
        linkColor="color"
        linkCanvasObject={paintLink}
        onLinkClick={(link, event) => {
          if (event) event.stopPropagation();
          console.log('🔗 2D Link clicked:', link);
          hideTooltip();

          if (!link) return;

          // Find source and target nodes from the graph data
          const sourceNode = forceGraphData.nodes.find(n => n.id === link.source?.id || n.id === link.source);
          const targetNode = forceGraphData.nodes.find(n => n.id === link.target?.id || n.id === link.target);

          // Create relationship object with enhanced data
          const relationship = {
            id: link.id || `${link.source?.id || link.source}-${link.target?.id || link.target}`,
            type: link.type || link.label || 'RELATES_TO',
            source: link.source?.id || link.source,
            target: link.target?.id || link.target,
            properties: link.properties || {},
            // Extract fact property for RelationshipDetails display
            fact: link.data?.properties?.fact || link.properties?.fact || link.fact,
            sourceNode: sourceNode?.data || sourceNode,
            targetNode: targetNode?.data || targetNode,
            // Include original link data
            originalLink: link
          };

          // Call the relationship selection callback
          onRelationshipSelect(relationship);
        }}
        onLinkHover={(link, prevLink) => {
          // Removed setHoveredLink to prevent unnecessary re-renders

          if (link) {
            const tooltipContent = getRelationshipTooltipContent(link);
            // Safely access canvas for tooltip positioning
            if (tooltipContent && fgRef.current && fgRef.current.canvas) {
              try {
                const canvas = fgRef.current.canvas;
                if (canvas && typeof canvas.getBoundingClientRect === 'function') {
                  const rect = canvas.getBoundingClientRect();
                  // Position tooltip at link midpoint
                  const midX = rect.left + ((link.source.x + link.target.x) / 2);
                  const midY = rect.top + ((link.source.y + link.target.y) / 2);
                  showTooltip(tooltipContent, midX, midY, 'link');
                }
              } catch (error) {
                console.warn('Error accessing canvas for link tooltip:', error);
              }
            }
          } else {
            hideTooltip();
          }
        }}
        backgroundColor="#f8fafc"
        width={undefined}
        height={undefined}
        enableNodeDrag={true}
        enableZoomInteraction={true}
        enablePanInteraction={true}
        minZoom={0.1}
        maxZoom={8}
        nodeRelSize={1}
        cooldownTicks={100}
        d3AlphaDecay={0.02}
        d3VelocityDecay={0.3}
        linkDistance={100}
        chargeStrength={-300}
      />

      {/* Custom 2D Tooltip */}
      {tooltip.visible && (
        <div
          style={{
            position: 'absolute',
            left: `${tooltip.x}px`,
            top: `${tooltip.y}px`,
            maxWidth: '300px',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            padding: '8px 12px',
            borderRadius: '4px',
            fontSize: '12px',
            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
            pointerEvents: 'none',
            zIndex: 1000,
            wordWrap: 'break-word',
            whiteSpace: 'pre-wrap',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',
            lineHeight: '1.4'
          }}
        >
          {tooltip.content}
        </div>
      )}

    </div>
  );
};


export default UnifiedGraphWrapper;
