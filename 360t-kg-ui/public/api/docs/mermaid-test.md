# Mermaid Integration Test

This document tests the mermaid diagram rendering functionality.

## Simple Flowchart

```mermaid
graph TD
    A[Start] --> B{Is it working?}
    B -->|Yes| C[Great!]
    B -->|No| D[Debug]
    C --> E[End]
    D --> F[Fix Issues]
    F --> B
```

## Sequence Diagram

```mermaid
sequenceDiagram
    participant User
    participant Browser
    participant DocumentationReader
    participant Mermaid
    
    User->>Browser: Load document
    Browser->>DocumentationReader: Render content
    DocumentationReader->>Mermaid: Process diagrams
    Mermaid->>DocumentationReader: Return SVG
    DocumentationReader->>Browser: Display rendered diagrams
    Browser->>User: Show complete document
```

## Class Diagram

```mermaid
classDiagram
    class DocumentationReader {
        +useState()
        +useEffect()
        +useRef()
        +processMermaidDiagrams()
        +generateTableOfContents()
    }
    
    class MermaidRenderer {
        +initialize()
        +parse()
        +render()
    }
    
    DocumentationReader --> MermaidRenderer : uses
```

## Git Graph

```mermaid
gitgraph
    commit id: "Initial setup"
    branch feature/mermaid
    commit id: "Add mermaid dependency"
    commit id: "Implement rendering logic"
    commit id: "Add error handling"
    checkout main
    merge feature/mermaid
    commit id: "Release v1.0"
```

## Error Example

This should show an error display:

```mermaid
invalid mermaid syntax
this will not render
```

## Multiple Diagrams Test

Here's a simple state diagram:

```mermaid
stateDiagram-v2
    [*] --> Loading
    Loading --> Success
    Loading --> Error
    Success --> [*]
    Error --> Retry
    Retry --> Loading
```

And a pie chart:

```mermaid
pie title Documentation Sections
    "Introduction" : 25
    "Examples" : 35
    "Error Handling" : 20
    "Testing" : 20
```

## Conclusion

If you can see the diagrams above rendered as SVG graphics instead of code blocks, the mermaid integration is working correctly!