# 3D Graph Visualization Implementation

## Overview

This document describes the implementation of 3D graph visualization capabilities for the Knowledge Graph Visualizer, following a hybrid architecture that extends the existing 2D React Force Graph implementation.

## Architecture

### Hybrid Architecture Design

The implementation uses a **separate component approach** with shared state management:

- **Unified3DGraphWrapper**: New 3D-specific component using `react-force-graph-3d`
- **GraphViewWrapper**: Extended with lazy loading and mode switching logic
- **App.jsx**: Centralized state management for 2D/3D mode switching
- **Header**: UI controls for mode toggling

### Key Benefits

1. **Separation of Concerns**: 2D and 3D logic are completely separate
2. **Performance Optimization**: 3D dependencies are lazy-loaded
3. **Maintainability**: Each component has a single responsibility
4. **Bundle Optimization**: Automatic code splitting for 3D components

## Components

### 1. Unified3DGraphWrapper (`src/components/Unified3DGraphWrapper.jsx`)

**Purpose**: 3D graph rendering with full feature parity to the 2D implementation.

**Key Features**:
- ForceGraph3D integration with camera positioning
- Click-to-focus with smooth camera transitions
- Escape key handling for returning to 2D mode
- WebGL memory management and cleanup
- Performance monitoring and stats display
- Node limits for optimal 3D performance (default: 1000 nodes)

**Props Interface**:
```javascript
{
  data,                    // Graph data (nodes/links)
  onNodeSelect,           // Node selection callback
  customConfig,           // Configuration object
  nodeColors,             // Node color mapping
  nodeSizes,              // Node size mapping
  onCenterOnNodeReady,    // Camera positioning callback
  onNodeClick,            // Node click handler
  nodeShapes,             // Node shape mapping
  relationshipColors,     // Edge color mapping
  relationshipLineStyles, // Edge style mapping
  onEscape               // Escape key callback
}
```

### 2. GraphViewWrapper (`src/components/GraphViewWrapper.jsx`)

**Purpose**: Mode switching logic with lazy loading and error boundaries.

**Enhancements**:
- React.lazy() for 3D component loading
- ErrorBoundary for graceful 3D loading failures
- LoadingFallback component for better UX
- Conditional rendering based on environment flags

**Environment Variables**:
- `VITE_USE_REACT_FORCE_GRAPH=true`: Enable React Force Graph
- `VITE_ENABLE_3D_MODE=true`: Enable 3D mode toggle

### 3. App.jsx State Management

**New State Variables**:
```javascript
const [is3DMode, setIs3DMode] = useState(false);
```

**URL Synchronization**:
- 3D mode: `?mode=3d`
- 2D mode: parameter removed from URL
- Automatic restoration on page reload

### 4. Header Component (`src/components/Header.jsx`)

**3D Toggle Button**:
- Only visible in Explorer view when 3D is enabled
- Dynamic icons (cube for 3D, square for 2D)
- Active state styling
- Keyboard accessibility

## Performance Optimizations

### 1. WebGL Memory Management

```javascript
// Automatic cleanup on component unmount
useEffect(() => {
  return () => {
    if (fgRef.current) {
      const renderer = fgRef.current.renderer();
      if (renderer) {
        renderer.dispose();
        const gl = renderer.getContext();
        const loseContext = gl.getExtension('WEBGL_lose_context');
        if (loseContext) loseContext.loseContext();
      }
    }
  };
}, []);
```

### 2. Performance Monitoring

Real-time stats display (development mode only):
- FPS monitoring
- Node/link counts
- WebGL render calls
- Memory usage (geometries/textures)

### 3. Node Limits

Configurable node limits for optimal 3D performance:
- Default: 1000 nodes (vs unlimited for 2D)
- Configurable via `customConfig.nodeLimit`
- Automatic data filtering when limit exceeded

## User Experience

### Mode Switching

1. **Toggle Button**: Click 3D/2D icon in header
2. **Escape Key**: Press ESC in 3D mode to return to 2D
3. **URL Support**: Direct links with `?mode=3d` parameter

### Visual Feedback

- Loading spinner during 3D component lazy loading
- "Press ESC to return to 2D view" hint in 3D mode
- Active state styling for toggle button
- Performance stats overlay (development only)

### Error Handling

- Graceful fallback to 2D mode if 3D loading fails
- Error boundaries prevent application crashes
- Retry functionality for failed 3D loads

## Testing

### Automated Test Suite (`test-3d-implementation.cjs`)

Comprehensive Playwright test covering:

1. **Component Loading**: 3D toggle button presence
2. **Mode Switching**: 2D ↔ 3D transitions
3. **Canvas Rendering**: WebGL canvas verification
4. **User Interaction**: Node clicking and camera controls
5. **Keyboard Navigation**: Escape key functionality
6. **URL Synchronization**: Parameter persistence
7. **Performance Monitoring**: Stats display verification
8. **Memory Management**: Multiple mode switches without crashes

**Test Results**: ✅ All 9 tests passing

### Performance Requirements

- **Load Time**: Sub-3 second graph loads
- **Response Time**: Sub-2 second mode switching
- **Memory**: No memory leaks during mode switching
- **Stability**: No crashes after multiple transitions

## Configuration

### Environment Variables

```bash
# .env file
VITE_USE_REACT_FORCE_GRAPH=true
VITE_ENABLE_3D_MODE=true
```

### Custom Configuration

```javascript
const customConfig = {
  nodeLimit: 1000,           // Max nodes for 3D rendering
  cameraDistance: 200,       // Default camera distance
  transitionDuration: 1000   // Camera transition time (ms)
};
```

## Dependencies

### New Dependencies

```json
{
  "react-force-graph-3d": "^1.28.0"
}
```

### Bundle Impact

- **Lazy Loading**: 3D dependencies only loaded when needed
- **Code Splitting**: Automatic bundle separation
- **Size Impact**: ~200KB additional bundle size (lazy-loaded)

## Browser Compatibility

### WebGL Requirements

- **WebGL 1.0**: Minimum requirement
- **WebGL 2.0**: Enhanced performance
- **Hardware Acceleration**: Required for optimal performance

### Supported Browsers

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Deployment

### Production Considerations

1. **Feature Flags**: Use environment variables to control 3D availability
2. **Performance Monitoring**: Monitor WebGL performance in production
3. **Fallback Strategy**: Graceful degradation for unsupported browsers
4. **Bundle Optimization**: Ensure lazy loading is working correctly

### Monitoring

- WebGL context creation success/failure rates
- 3D mode usage analytics
- Performance metrics (FPS, render times)
- Error rates and fallback usage

## Future Enhancements

### Planned Features

1. **VR/AR Support**: WebXR integration
2. **Advanced Camera Controls**: Orbit, pan, zoom presets
3. **3D Node Shapes**: Custom 3D geometries
4. **Spatial Layouts**: 3D-specific force algorithms
5. **Performance Profiles**: Device-specific optimizations

### Technical Debt

1. **Unified Configuration**: Merge 2D/3D config systems
2. **Shared Utilities**: Extract common graph utilities
3. **Type Safety**: Add TypeScript definitions
4. **Testing Coverage**: Expand automated test scenarios

## Troubleshooting

### Common Issues

1. **3D Mode Not Available**: Check `VITE_ENABLE_3D_MODE` environment variable
2. **Performance Issues**: Reduce node limit or check hardware acceleration
3. **WebGL Errors**: Verify browser WebGL support
4. **Memory Leaks**: Ensure proper component unmounting

### Debug Tools

- Performance stats overlay (development mode)
- Browser WebGL inspector
- React DevTools for component state
- Network tab for bundle loading verification
