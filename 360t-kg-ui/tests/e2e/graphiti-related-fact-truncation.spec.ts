import { test, expect } from '@playwright/test';

// Utility: navigate to available dev port (5177 default, 5178 fallback)
async function gotoApp(page) {
  try {
    await page.goto('http://localhost:5177');
    await page.waitForSelector('.chat-view-modern, .graph-container', { timeout: 8000 });
  } catch {
    await page.goto('http://localhost:5178');
    await page.waitForSelector('.chat-view-modern, .graph-container', { timeout: 8000 });
  }
}

// Ensure Graphiti mode before app loads
test.beforeEach(async ({ page }) => {
  await page.addInitScript(() => {
    try {
      localStorage.setItem(
        'kg-visualizer-unified-config',
        JSON.stringify({ state: { config: { mode: 'graphiti' } } })
      );
    } catch {}
  });
});

test.describe('Graphiti Related section - relationship fact truncation', () => {
  test('truncates fact to 15 chars with tooltip showing full text', async ({ page }) => {
    await gotoApp(page);

    // Open chat and send a generic query to trigger Graphiti results
    const input = page.locator('[data-testid="chat-input"], textarea');
    if (await input.count()) {
      await input.first().fill('List relationships with facts for testing');
      await page.keyboard.press('Enter');
    }

    // Wait for Related section to render (Graphiti)
    const relatedHeader = page.locator('text=Related');
    await relatedHeader.waitFor({ state: 'visible', timeout: 30000 }).catch(() => {});

    // Look for truncated fact elements in the Related section
    const truncatedFacts = page.locator('[data-testid="graphiti-fact-truncated"]');

    // Allow some time for streaming/LLM
    await truncatedFacts.first().waitFor({ state: 'visible', timeout: 30000 }).catch(() => {});

    const count = await truncatedFacts.count();
    console.log(`Found ${count} truncated fact chips in Related section`);

    // If none found, skip further assertions but keep screenshot for diagnostics
    if (count === 0) {
      console.warn('No truncated facts found in Related section - environment may lack data.');
      await page.screenshot({ path: 'test-results/graphiti-fact-truncation-no-data.png', fullPage: true });
      test.fixme(true, 'Environment did not yield Graphiti relationships with fact');
      return;
    }

    const first = truncatedFacts.first();
    await expect(first).toBeVisible();

    // Assert truncation pattern: up to 15 chars + optional ellipsis
    const txt = (await first.textContent())?.trim() || '';
    expect(txt.length).toBeGreaterThan(0);
    expect(/^.{0,15}(\.\.\.)?$/.test(txt)).toBeTruthy();

    // Hover to show tooltip and verify full text equals data-full-fact attribute
    const full = await first.getAttribute('data-full-fact');
    expect(full && full.length > 0).toBeTruthy();

    await first.hover();
    const tooltip = page.locator('[role="tooltip"]');
    await expect(tooltip).toBeVisible({ timeout: 5000 });
    const tipText = (await tooltip.textContent())?.trim() || '';

    // Tooltip should contain the full, untruncated text
    expect(tipText).toContain(full!.slice(0, Math.min(20, full!.length))); // prefix check

    await page.screenshot({ path: 'test-results/graphiti-fact-truncation.png', fullPage: true });
  });
});

