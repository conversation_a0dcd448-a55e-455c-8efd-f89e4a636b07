/**
 * ChatDisclaimer Visibility Fix Script
 *
 * This script provides multiple solutions to make the disclaimer visible again.
 * Use this when the disclaimer was previously dismissed but needs to be shown.
 */

console.log('🔧 ChatDisclaimer Visibility Fix');
console.log('================================');

// Solution 1: Clear localStorage (Temporary Fix)
function clearDisclaimerDismissal() {
  try {
    localStorage.removeItem('kg-chat-disclaimer-dismissed');
    console.log('✅ Cleared disclaimer dismissal from localStorage');
    console.log('   Refresh the page to see the disclaimer');
    return true;
  } catch (error) {
    console.error('❌ Failed to clear localStorage:', error);
    return false;
  }
}

// Solution 2: Force show disclaimer (Development/Testing)
function forceShowDisclaimer() {
  try {
    // Clear localStorage
    localStorage.removeItem('kg-chat-disclaimer-dismissed');

    // Trigger React state update by dispatching a storage event
    window.dispatchEvent(new StorageEvent('storage', {
      key: 'kg-chat-disclaimer-dismissed',
      oldValue: '1',
      newValue: null,
      url: window.location.href
    }));

    console.log('✅ Forced disclaimer to show');
    console.log('   The disclaimer should appear immediately');
    return true;
  } catch (error) {
    console.error('❌ Failed to force show disclaimer:', error);
    return false;
  }
}

// Solution 3: Reset all chat-related localStorage
function resetChatStorage() {
  try {
    const keys = Object.keys(localStorage);
    const chatKeys = keys.filter(key => key.includes('chat') || key.includes('kg-'));

    console.log('🧹 Found chat-related localStorage keys:', chatKeys);

    chatKeys.forEach(key => {
      const oldValue = localStorage.getItem(key);
      localStorage.removeItem(key);
      console.log(`   Removed: ${key} = ${oldValue}`);
    });

    console.log('✅ Reset all chat-related storage');
    console.log('   Refresh the page to see clean state');
    return true;
  } catch (error) {
    console.error('❌ Failed to reset chat storage:', error);
    return false;
  }
}

// Solution 4: Debug current state
function debugDisclaimerState() {
  console.log('\n🔍 Current Disclaimer State:');
  console.log('============================');

  const dismissed = localStorage.getItem('kg-chat-disclaimer-dismissed');
  const shouldShow = !dismissed;

  console.log('localStorage value:', dismissed);
  console.log('Should show disclaimer:', shouldShow);

  // Check DOM
  const element = document.querySelector('.chat-disclaimer');
  console.log('Disclaimer element in DOM:', !!element);

  if (element) {
    const styles = window.getComputedStyle(element);
    console.log('Element styles:', {
      display: styles.display,
      visibility: styles.visibility,
      opacity: styles.opacity,
      height: styles.height
    });
  }

  // Check React component state (if available)
  const messagesContainer = document.querySelector('.messages-container-modern');
  if (messagesContainer) {
    console.log('Messages container children:', messagesContainer.children.length);
    const hasDisclaimer = Array.from(messagesContainer.children).some(child =>
      child.classList.contains('chat-disclaimer')
    );
    console.log('Contains disclaimer element:', hasDisclaimer);
  }
}

// Export functions for easy use
window.disclaimerFix = {
  clear: clearDisclaimerDismissal,
  forceShow: forceShowDisclaimer,
  reset: resetChatStorage,
  debug: debugDisclaimerState
};

// Auto-run debug
debugDisclaimerState();

console.log('\n📋 Available Commands:');
console.log('======================');
console.log('disclaimerFix.clear()     - Clear dismissal and refresh');
console.log('disclaimerFix.forceShow() - Force show immediately');
console.log('disclaimerFix.reset()     - Reset all chat storage');
console.log('disclaimerFix.debug()     - Debug current state');
console.log('\n💡 Quick fix: disclaimerFix.clear() then refresh page');