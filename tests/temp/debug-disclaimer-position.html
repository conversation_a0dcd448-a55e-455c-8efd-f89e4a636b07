<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug ChatDisclaimer Position</title>
    <style>
        /* Copy the relevant styles from ChatViewModern.css to test positioning */
        .messages-container-modern {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden; /* This might be the issue */
            min-height: 0;
            background: transparent;
            height: 400px;
            border: 2px solid red; /* Debug border */
        }

        .messages-list-area {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 2rem 1.5rem;
            padding-top: 5rem; /* Large top padding */
            padding-bottom: 32px;
            border: 2px solid blue; /* Debug border */
        }

        /* Disclaimer styles */
        .chat-disclaimer {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 10px 12px;
            border-radius: 8px;
            border: 1px solid transparent;
            margin: 8px 12px 12px 12px;
            background: #e8f1fd;
            border-color: #b6d4fe;
            color: #0b3d91;
        }

        .chat-disclaimer-content {
            flex: 1 1 auto;
        }

        .chat-disclaimer-text {
            margin: 0;
            font-size: 0.92rem;
            line-height: 1.35rem;
        }
    </style>
</head>
<body>
    <div style="height: 100vh; display: flex; flex-direction: column;">
        <h1>Debug ChatDisclaimer Position</h1>

        <!-- This simulates the ChatView structure -->
        <div class="messages-container-modern">
            <!-- AI Disclaimer banner - should appear here -->
            <div class="chat-disclaimer">
                <div class="chat-disclaimer-content">
                    <p class="chat-disclaimer-text">
                        AI Assist: Responses are generated from 360T user guides and related documentation.
                        While we strive for accuracy, AI may be incomplete or incorrect.
                    </p>
                </div>
            </div>

            <!-- Scrollable messages area -->
            <div class="messages-list-area">
                <h3>Messages would go here...</h3>
                <p>This is the scrollable messages area</p>
                <p>Notice the large top padding (5rem) to make space for floating controls</p>
            </div>
        </div>
    </div>
</body>
</html>