/**
 * ChatDisclaimer Debug Script
 *
 * This script tests the disclaimer visibility logic and identifies potential issues.
 * Run this in browser console to debug disclaimer state.
 */

// Test localStorage state
console.log('=== ChatDisclaimer Debug Test ===');
console.log('localStorage key:', 'kg-chat-disclaimer-dismissed');
console.log('localStorage value:', localStorage.getItem('kg-chat-disclaimer-dismissed'));
console.log('Should show disclaimer:', !localStorage.getItem('kg-chat-disclaimer-dismissed'));

// Test React state logic simulation
const simulateReactState = () => {
  const isDismissed = localStorage.getItem('kg-chat-disclaimer-dismissed');
  const shouldShow = !isDismissed;
  console.log('React useState simulation:');
  console.log('  isDismissed:', isDismissed);
  console.log('  shouldShow:', shouldShow);
  return shouldShow;
};

const showDisclaimer = simulateReactState();

// Check DOM elements
console.log('\n=== DOM Investigation ===');
const disclaimerElement = document.querySelector('.chat-disclaimer');
console.log('Disclaimer element in DOM:', disclaimerElement);

if (disclaimerElement) {
  const computedStyle = window.getComputedStyle(disclaimerElement);
  console.log('Disclaimer styles:');
  console.log('  display:', computedStyle.display);
  console.log('  visibility:', computedStyle.visibility);
  console.log('  opacity:', computedStyle.opacity);
  console.log('  position:', computedStyle.position);
  console.log('  z-index:', computedStyle.zIndex);
  console.log('  height:', computedStyle.height);
  console.log('  overflow:', computedStyle.overflow);
} else {
  console.log('❌ No disclaimer element found in DOM - this indicates the React conditional is not rendering');
}

// Check messages container structure
const messagesContainer = document.querySelector('.messages-container-modern');
console.log('Messages container:', messagesContainer);

if (messagesContainer) {
  console.log('Messages container children:', Array.from(messagesContainer.children).map(child => ({
    className: child.className,
    tagName: child.tagName,
    isDisclaimer: child.classList.contains('chat-disclaimer')
  })));
}

// Clear localStorage to force disclaimer to show (for testing)
console.log('\n=== Test Actions ===');
console.log('To test disclaimer visibility:');
console.log('1. Run: localStorage.removeItem("kg-chat-disclaimer-dismissed")');
console.log('2. Refresh the page');
console.log('3. The disclaimer should appear at the top of the chat messages area');

// Auto-test: Clear localStorage and suggest refresh
if (confirm('Clear localStorage and refresh to test disclaimer visibility?')) {
  localStorage.removeItem('kg-chat-disclaimer-dismissed');
  location.reload();
}