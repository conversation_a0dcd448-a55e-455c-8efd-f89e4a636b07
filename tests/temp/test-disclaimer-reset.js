// Test script to reset disclaimer and test functionality
console.log('ChatDisclaimer Test Script');

// Clear localStorage to reset disclaimer
if (typeof localStorage !== 'undefined') {
  localStorage.removeItem('kg-chat-disclaimer-dismissed');
  console.log('✅ Disclaimer localStorage key cleared');
  console.log('🔄 Refresh the page to see disclaimer again');
} else {
  console.log('❌ localStorage not available (run this in browser console)');
}

// Instructions for manual testing
console.log(`
📝 Manual Testing Instructions:
1. Open browser developer console
2. Run: localStorage.removeItem('kg-chat-disclaimer-dismissed')
3. Refresh the page
4. Disclaimer should appear at top of chat messages
5. Test "Dismiss" button - should hide disclaimer temporarily
6. Test "Don't show again" - should hide and persist in localStorage
7. Test error/warning variant by triggering validation errors
`);