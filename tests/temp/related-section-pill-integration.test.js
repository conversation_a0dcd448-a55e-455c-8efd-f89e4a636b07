/**
 * Integration Test: RelatedSectionPill in FormatCRenderer
 *
 * This test verifies that the RelatedSectionPill component has been properly
 * integrated into FormatCRenderer, replacing the old accordion-style RELATED section.
 *
 * Key Integration Points Tested:
 * 1. RelatedSectionPill is positioned correctly at bottom left of answer card
 * 2. Component receives correct data from FormatCRenderer (entities, facts, edges)
 * 3. Component handles Graphiti-only rendering logic properly
 * 4. No interference with existing functionality (metadata, results sections)
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import FormatCRenderer from '../../../360t-kg-ui/src/components/chat/FormatCRenderer';

// Mock UnifiedChips component since it's complex and we're testing integration
jest.mock('../../../360t-kg-ui/src/components/chat/UnifiedChips', () => {
  return function MockUnifiedChips({ entities, facts, edges, onEntitySelect }) {
    return (
      <div data-testid="unified-chips-mock">
        <span data-testid="entities-count">{entities?.length || 0}</span>
        <span data-testid="facts-count">{facts?.length || 0}</span>
        <span data-testid="edges-count">{edges?.length || 0}</span>
        {entities?.map((entity, idx) => (
          <button
            key={idx}
            data-testid={`entity-${idx}`}
            onClick={() => onEntitySelect(entity)}
          >
            {entity.name}
          </button>
        ))}
      </div>
    );
  };
});

describe('FormatCRenderer - RelatedSectionPill Integration', () => {
  const mockOnNodeSelect = jest.fn();
  const mockOnSendMessage = jest.fn();

  // Sample Graphiti response with related data
  const graphitiFormatCResponse = {
    version: '3.0',
    format: 'C',
    core: {
      summary: 'This is a Graphiti response with related entities and edges.',
      results: [
        {
          source: { title: 'Additional Fact 1' },
          content: 'This is additional context from Graphiti search.'
        }
      ],
      confidence_score: 0.85,
      processing_time_ms: 1250
    },
    extensions: {
      graphiti: {
        nodes: [
          {
            id: 'node-1',
            name: 'Trading System',
            properties: {
              business_category: 'TRADING',
              description: 'Primary trading system'
            },
            source: 'graphiti'
          },
          {
            id: 'node-2',
            name: 'Risk Management',
            properties: {
              business_category: 'RISK',
              description: 'Risk management module'
            },
            source: 'graphiti'
          }
        ],
        edges: [
          {
            id: 'edge-1',
            type: 'INTEGRATES_WITH',
            source: { id: 'node-1', name: 'Trading System' },
            target: { id: 'node-2', name: 'Risk Management' },
            properties: {
              fact: 'Trading system integrates with risk management for real-time monitoring'
            }
          }
        ],
        search_algorithm: 'hybrid_search',
        search_time_ms: 150
      }
    }
  };

  // Atlas-only response (should not show RelatedSectionPill)
  const atlasOnlyResponse = {
    version: '3.0',
    format: 'C',
    core: {
      summary: 'This is an Atlas-only response.',
      results: [],
      confidence_score: 0.92
    },
    extensions: {
      atlas_rag: {
        entities: [
          {
            id: 'atlas-entity-1',
            name: 'Atlas Entity',
            category: 'SYSTEM'
          }
        ],
        pagerank_scores: [0.8, 0.6]
      }
    }
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Graphiti Responses', () => {
    it('should render RelatedSectionPill for Graphiti responses with related data', () => {
      render(
        <FormatCRenderer
          response={graphitiFormatCResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
        />
      );

      // Main answer should be rendered
      expect(screen.getByTestId('message-summary')).toBeInTheDocument();
      expect(screen.getByText(/This is a Graphiti response/)).toBeInTheDocument();

      // RelatedSectionPill should be present
      const relatedPill = screen.getByRole('button', { name: /Toggle related information/ });
      expect(relatedPill).toBeInTheDocument();
      expect(relatedPill).toHaveTextContent('4 related'); // 2 nodes + 1 fact + 1 edge
    });

    it('should position RelatedSectionPill at bottom left of answer card', () => {
      const { container } = render(
        <FormatCRenderer
          response={graphitiFormatCResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
        />
      );

      const pillContainer = container.querySelector('.related-pill-container');
      expect(pillContainer).toBeInTheDocument();

      // Check CSS positioning
      const computedStyle = window.getComputedStyle(pillContainer);
      expect(computedStyle.position).toBe('absolute');
    });

    it('should pass correct data to UnifiedChips when pill is opened', async () => {
      render(
        <FormatCRenderer
          response={graphitiFormatCResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
        />
      );

      const relatedPill = screen.getByRole('button', { name: /Toggle related information/ });

      // Open the popover
      fireEvent.click(relatedPill);

      await waitFor(() => {
        // Check that UnifiedChips receives the correct data
        expect(screen.getByTestId('unified-chips-mock')).toBeInTheDocument();
        expect(screen.getByTestId('entities-count')).toHaveTextContent('2'); // 2 entities
        expect(screen.getByTestId('facts-count')).toHaveTextContent('1');   // 1 fact
        expect(screen.getByTestId('edges-count')).toHaveTextContent('1');   // 1 edge
      });
    });

    it('should handle entity selection through RelatedSectionPill', async () => {
      render(
        <FormatCRenderer
          response={graphitiFormatCResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
        />
      );

      const relatedPill = screen.getByRole('button', { name: /Toggle related information/ });

      // Open the popover
      fireEvent.click(relatedPill);

      await waitFor(() => {
        const entityButton = screen.getByTestId('entity-0');
        fireEvent.click(entityButton);

        expect(mockOnNodeSelect).toHaveBeenCalledWith(
          expect.objectContaining({
            id: 'node-1',
            name: 'Trading System'
          })
        );
      });
    });

    it('should not interfere with existing metadata and results sections', () => {
      render(
        <FormatCRenderer
          response={graphitiFormatCResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
          showDebugInfo={true}
        />
      );

      // Main answer content should still be present
      expect(screen.getByTestId('message-summary')).toBeInTheDocument();

      // RelatedSectionPill should be present
      expect(screen.getByRole('button', { name: /Toggle related information/ })).toBeInTheDocument();

      // Both should coexist without conflicts
      const answerCard = screen.getByTestId('message-summary');
      const relatedPill = screen.getByRole('button', { name: /Toggle related information/ });

      expect(answerCard).toBeInTheDocument();
      expect(relatedPill).toBeInTheDocument();
    });
  });

  describe('Atlas-Only Responses', () => {
    it('should not render RelatedSectionPill for Atlas-only responses', () => {
      render(
        <FormatCRenderer
          response={atlasOnlyResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
        />
      );

      // Main answer should be rendered
      expect(screen.getByTestId('message-summary')).toBeInTheDocument();
      expect(screen.getByText(/This is an Atlas-only response/)).toBeInTheDocument();

      // RelatedSectionPill should NOT be present
      expect(screen.queryByRole('button', { name: /Toggle related information/ })).not.toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should not render RelatedSectionPill when no related data is available', () => {
      const emptyGraphitiResponse = {
        ...graphitiFormatCResponse,
        core: {
          ...graphitiFormatCResponse.core,
          results: []
        },
        extensions: {
          graphiti: {
            nodes: [],
            edges: [],
            search_algorithm: 'hybrid_search'
          }
        }
      };

      render(
        <FormatCRenderer
          response={emptyGraphitiResponse}
          onNodeSelect={mockOnNodeSelect}
          onSendMessage={mockOnSendMessage}
        />
      );

      // Main answer should be rendered
      expect(screen.getByTestId('message-summary')).toBeInTheDocument();

      // RelatedSectionPill should NOT be present (no data)
      expect(screen.queryByRole('button', { name: /Toggle related information/ })).not.toBeInTheDocument();
    });
  });
});

console.log('✅ RelatedSectionPill integration test created successfully');
console.log('📍 Test Location: /tests/temp/related-section-pill-integration.test.js');
console.log('🎯 Key Integration Points Covered:');
console.log('   - Proper positioning in FormatCRenderer');
console.log('   - Data flow from FormatCRenderer to RelatedSectionPill');
console.log('   - Graphiti-only rendering logic');
console.log('   - Entity selection handling');
console.log('   - Non-interference with existing functionality');