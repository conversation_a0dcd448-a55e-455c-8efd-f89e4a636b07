<!-- Sync Impact Report
- Version change: 2.1.0 → 2.1.1 (PATCH - Reality-based corrections from CLAUDE.md analysis)
- Modified principles: Corrected overengineered CI/CD, testing, and innovation sections
- Added sections: None
- Removed sections: None (corrected existing sections)
- Templates requiring updates: ✅ .specify/templates/plan-template.md (Removed overengineered requirements)
- Follow-up TODOs: None
-->

# 360T Knowledge Graph Visualizer Constitution

## Core Principles

### I. Test-Driven Development (NON-NEGOTIABLE)
TDD is mandatory for all new features. Tests MUST be written → User approved → Tests fail → Then implementation follows. Red-Green-Refactor cycle is strictly enforced across all service boundaries (frontend, backend, Python AI).

### II. Integration-First Architecture
System components MUST validate contracts between services. All API changes require contract tests, inter-service communication MUST be tested end-to-end, and shared schemas MUST be versioned and validated.

### III. Unified Configuration Management
Single source of truth for all configuration with security-first approach. Azure credentials managed via environment variables only, never stored client-side. Configuration changes MUST propagate through all services consistently.

### IV. Microservices with Clear Boundaries
6-service architecture with well-defined responsibilities. Each service MUST have independent deployability, clear API contracts, and proper health monitoring. Cross-service dependencies MUST be explicit and documented.

### V. Observability and Performance
Comprehensive logging and monitoring required. All services MUST expose health endpoints, structured logging MUST be implemented, and performance metrics MUST be tracked (especially search response times <200ms).

### VI. Format-C Response Standard
All AI responses MUST follow Format-C schema validation. Generic RAG workflow: RAG System → FormatCBuilder → Contextual Follow-ups → Format-C Response. Entity-aware contextual intelligence is REQUIRED for follow-up generation.

## Architecture Standards

### Service Communication
- REST APIs with OpenAPI documentation
- Message passing through well-defined contracts
- Service discovery through proxy layer
- Circuit breakers for external dependencies

### Data Management
- Neo4j for graph data with proper indexing
- PostgreSQL for metadata and user data
- Redis for caching and session management
- All database operations MUST be transactional

### Security Requirements
- Authentication via JWT tokens
- Authorization at service boundaries
- Rate limiting on all external endpoints
- CORS policies strictly enforced
- Secrets management via environment variables

### Performance Standards
- Frontend response time <100ms
- API response time <200ms
- Search queries <500ms
- Database queries <50ms
- Memory usage monitored and optimized

## Development Standards

### Code Quality
- ESLint and Prettier for frontend
- Black and flake8 for Python
- Type checking with TypeScript
- Code review required for all changes
- Automated linting in CI/CD

### Documentation
- API documentation MUST be current
- User guides updated with features
- Architecture diagrams maintained
- Code comments explain WHY, not WHAT
- CLAUDE.md updated for AI assistants

### Version Control
- Semantic versioning for all services
- Feature branches from main
- Pull request reviews required
- Commit messages follow conventional format
- Tags for all releases

## Quality Assurance

### Testing Requirements
- Unit tests for all functions
- Integration tests for service boundaries
- Contract tests for API compatibility
- E2E tests for user workflows
- Performance tests for critical paths

### CI/CD Pipeline
- All tests MUST pass before merge
- Security scanning on all PRs
- Performance benchmarks monitored
- Deployment to staging for validation
- Production deployments with rollback capability

### Error Handling
- Graceful degradation for failures
- Proper error logging and monitoring
- User-friendly error messages
- Circuit breakers for external services
- Automatic retry with exponential backoff

## Critical Implementation Patterns

### RAG System Integration
When adding new RAG systems, follow the Generic RAG Workflow:
1. **Generate Response**: RAG system produces answer text and entities
2. **Use FormatCBuilder**: Call `transform_*_to_format_c()` method (never create separate response builders)
3. **Entity Extraction**: FormatCBuilder automatically extracts entities for contextual follow-ups
4. **Contextual Intelligence**: System generates domain-specific questions based on 8 business categories

### Response Builder Anti-Patterns
**❌ Don't Do**: Create separate ResponseBuilder instances for new RAG systems
**❌ Don't Do**: Implement custom follow-up generation logic per RAG system
**❌ Don't Do**: Parse Format-C JSON manually - use schema validation utilities
**✅ Do**: Use FormatCBuilder for all response formatting
**✅ Do**: Let entity-aware intelligence generate contextual follow-ups automatically
**✅ Do**: Validate responses against Format-C schema in `360t-kg-api/schemas/`

### Configuration Development Patterns
Use the unified configuration store for all configuration management, ensuring Azure credentials are never stored client-side and environment-specific settings are properly managed.

## Service Management

### Development Server Management
The project uses `scripts/simple-dev.js` for reliable service startup with proper sequencing and health checks. Services start in order: API Server → Proxy Server → Python AI → Frontend.

### Process Debugging
Use standard process management commands for debugging stuck processes, clearing development caches, and validating service connectivity.

## Database Operations

### Neo4j Management
Database operations use standard Neo4j management with cypher-shell for basic operations and custom scripts for graph analysis, data loading, and entity management.

### Data Loading and Schema
Manual database operations are currently required using Neo4j Browser or cypher-shell, with existing analysis scripts for understanding current data structure.

## Development Workflow

### Testing Workflow
- Always run linting before commits
- Test search functionality after any `UnifiedSearchBar.jsx` changes
- Verify chat responses after `ChatView.jsx` modifications
- Check graph rendering after coordinate/positioning changes
- Validate Format-C schema compliance when modifying response structures
- Run comprehensive test suites for critical validations

### Configuration Testing
- Test configuration persistence across browser sessions
- Verify automatic legacy migration from old localStorage keys
- Test both Graphiti and AtlasRAG modes with respective configuration panels
- Validate Azure provider selection shows appropriate environment status

## Governance

This constitution supersedes all other development practices. Amendments require documentation, team approval, and migration plan for existing code.

### Amendment Process
1. Proposed changes documented with rationale
2. Team review and consensus required
3. Impact analysis on existing code
4. Migration plan for affected systems
5. Version increment according to semantic versioning

### Compliance Requirements
- All PRs MUST verify constitutional compliance
- Complexity MUST be justified with business value
- Constitution violations MUST be documented in complexity tracking
- Regular audits of compliance across codebase

### Enforcement
- Constitution checks in CI/CD pipeline
- Code reviews MUST reference relevant principles
- Non-compliance requires explicit team approval
- Technical debt tracked and prioritized

**Version**: 2.1.1 | **Ratified**: 2025-09-25 | **Last Amended**: 2025-09-25