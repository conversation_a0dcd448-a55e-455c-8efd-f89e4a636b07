{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(npm run lint)", "Bash(npm run build:*)", "Bash(lsof:*)", "Bash(kill:*)", "<PERSON><PERSON>(curl:*)", "Bash(find:*)", "Bash(cp:*)", "Bash(npm install:*)", "Bash(node:*)", "Bash(GRAPH_API_PORT=3002 node graph-api-server.js)", "<PERSON><PERSON>(python:*)", "Bash(echo)", "Bash(lsof:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(npx playwright test:*)", "Bash(ls:*)", "Bash(find:*)", "Bash(npm run test:*)", "Bash(node:*)", "<PERSON><PERSON>(mv:*)", "Bash(grep:*)", "Bash(kill:*)", "<PERSON><PERSON>(playwright test:*)", "Bash(tar:*)", "<PERSON><PERSON>(git clean:*)", "Bash(git checkout:*)", "Bash(rm:*)", "Bash(npm run dev:*)", "Bash(npm test)", "Bash(npm run:*)", "Bash(rg:*)", "<PERSON><PERSON>(pkill:*)", "Bash(npx kill-port:*)", "WebFetch(domain:medium.com)", "WebFetch(domain:gist.github.com)", "WebFetch(domain:blog.scottlogic.com)", "Bash(GRAPHITI_DATABASE=neo4j python graphiti_hybrid_search.py \"What are the trading strategies available in 360T?\" --edge-count 3 --node-count 2)", "WebFetch(domain:help.getzep.com)", "WebFetch(domain:github.com)", "Bash(../.venv/bin/python:*)", "Bash(sudo lsof:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(echo:*)", "Bash(/Users/<USER>/Documents/Trainings/KnowledgeGraphVisualizer/.venv/bin/python3 ../graphiti_hybrid_search_streaming.py \"What is EMS?\" --uri \"bolt://localhost:7687\" --user \"neo4j\" --password \"1979@rabu\" --database \"neo4j\")", "Bash(env NEO4J_PASSWORD=\"1979@rabu\" /Users/<USER>/Documents/Trainings/KnowledgeGraphVisualizer/.venv/bin/python3 ./graphiti_hybrid_search_streaming.py \"What is EMS?\")", "Bash(git add:*)", "Bash(git commit:*)", "<PERSON><PERSON>(wget:*)", "Bash(.venv/bin/python3:*)", "<PERSON><PERSON>(claude doctor)", "<PERSON><PERSON>(source:*)", "Bash(PATH=\"$HOME/.claude/local:$PATH\" claude --version)", "Bash(npm update:*)", "Bash(PATH=\"$HOME/.claude/local:$PATH\" claude doctor)", "Bash(npm restart)", "Bash(npm start)", "Bash(npm test:*)", "<PERSON><PERSON>(timeout:*)", "Bash(pip install:*)", "Bash(.venv/bin/pip install:*)", "Bash(.venv/bin/python:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(./process-manager.sh:*)", "<PERSON><PERSON>(chmod:*)", "Bash(brew services:*)", "<PERSON><PERSON>(neo4j status:*)", "Bash(neo4j-admin database load:*)", "<PERSON><PERSON>(neo4j:*)", "Bash(cypher-shell:*)", "Bash(time cypher-shell:*)", "Bash(ps:*)", "<PERSON><PERSON>(time curl:*)", "Bash(open http://localhost:5178/?view=chat)", "Bash(./start-services.sh:*)", "Bash(NEO4J_URI=bolt://localhost:7687 NEO4J_USER=neo4j NEO4J_PASSWORD=\"1979@rabu\" node scripts/update-entity-categories.js --dry-run)", "Bash(NEO4J_URI=bolt://localhost:7687 NEO4J_USER=neo4j NEO4J_PASSWORD=\"1979@rabu\" node scripts/update-entity-categories.js)", "<PERSON><PERSON>(mkdir:*)", "mcp__sequential-thinking__sequentialthinking", "mcp__context-7__resolve-library-id", "mcp__context-7__get-library-docs", "Bash(./fastapi_env/bin/python:*)", "<PERSON><PERSON>(npx playwright:*)", "Bash(open http://localhost:5177/)", "<PERSON><PERSON>(touch:*)", "Bash(./consolidate-docs.sh:*)", "Bash(bash:*)", "Bash(./final-docs-cleanup.sh:*)", "Bash(conda:*)", "<PERSON><PERSON>(pip show:*)", "Bash(/opt/anaconda3/bin/conda env list)", "Bash(npx stylelint:*)", "Bash(npx eslint:*)", "mcp__ide__getDiagnostics", "WebSearch", "Bash(PGPASSWORD=postgres psql:*)", "Bash(npx mocha tests/atlas-rag-integration.test.js:*)", "Bash(npm ls:*)", "WebFetch(domain:pypi.org)", "Read(//Users/<USER>/.claude/**)", "Read(//Users/<USER>/Desktop/**)", "Read(//Users/<USER>/Documents/Trainings/AutoSchemaKG/**)", "Read(//Users/<USER>/Downloads/**)", "Read(/Users/<USER>/Documents/Trainings/AutoSchemaKG/**)", "Read(/Users/<USER>/Documents/Trainings/AutoSchemaKG/**)", "Read(/Users/<USER>/Documents/Trainings/AutoSchemaKG/**)", "Bash(PYTHONPATH=src python:*)", "Bash(pip search:*)", "Bash(pip index:*)", "Bash(env:*)", "<PERSON><PERSON>(printenv:*)", "Bash(open http://localhost:5177/?view=chat)", "<PERSON><PERSON>(open:*)", "<PERSON><PERSON>(python3:*)", "Read(//Users/<USER>/Documents/Trainings/KnowledgeGraphVisualizer/**)", "Bash(cd:*)"], "deny": []}}