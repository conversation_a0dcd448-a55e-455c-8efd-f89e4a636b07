// ============================================
// Authentication Service
// ============================================

const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');

// Configuration constants
const SALT_ROUNDS = 12;
const TOKEN_EXPIRES_IN = '24h';

/**
 * Hash a password using bcrypt
 * @param {string} password - Plain text password
 * @returns {Promise<string>} - Hashed password
 */
const hashPassword = async (password) => {
  try {
    if (!password || typeof password !== 'string') {
      throw new Error('Password must be a non-empty string');
    }
    
    return await bcrypt.hash(password, SALT_ROUNDS);
  } catch (error) {
    throw new Error(`Password hashing failed: ${error.message}`);
  }
};

/**
 * Compare a plain text password with a hashed password
 * @param {string} password - Plain text password
 * @param {string} hash - Hashed password
 * @returns {Promise<boolean>} - True if passwords match
 */
const comparePassword = async (password, hash) => {
  try {
    if (!password || !hash) {
      return false;
    }
    
    return await bcrypt.compare(password, hash);
  } catch (error) {
    console.error('Password comparison failed:', error);
    return false;
  }
};

/**
 * Generate a JWT token for a user
 * @param {Object} user - User object containing id, username, email
 * @returns {string} - JWT token
 */
const generateToken = (user) => {
  try {
    if (!user || !user.id || !user.username || !user.email) {
      throw new Error('User object must contain id, username, and email');
    }

    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      throw new Error('JWT_SECRET environment variable is required');
    }

    const payload = {
      id: user.id,
      username: user.username,
      email: user.email,
      iat: Math.floor(Date.now() / 1000)
    };

    return jwt.sign(payload, jwtSecret, { 
      expiresIn: TOKEN_EXPIRES_IN,
      issuer: '360t-kg-api',
      subject: user.id.toString()
    });
  } catch (error) {
    throw new Error(`Token generation failed: ${error.message}`);
  }
};

/**
 * Verify and decode a JWT token
 * @param {string} token - JWT token to verify
 * @returns {Object} - Decoded token payload
 */
const verifyToken = (token) => {
  try {
    if (!token) {
      throw new Error('Token is required');
    }

    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      throw new Error('JWT_SECRET environment variable is required');
    }

    return jwt.verify(token, jwtSecret);
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      throw new Error('Invalid token');
    }
    if (error.name === 'TokenExpiredError') {
      throw new Error('Token expired');
    }
    if (error.name === 'NotBeforeError') {
      throw new Error('Token not active');
    }
    
    throw new Error(`Token verification failed: ${error.message}`);
  }
};

/**
 * Extract token from Authorization header
 * @param {string} authHeader - Authorization header value
 * @returns {string|null} - Extracted token or null
 */
const extractTokenFromHeader = (authHeader) => {
  if (!authHeader || typeof authHeader !== 'string') {
    return null;
  }

  const parts = authHeader.split(' ');
  
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return null;
  }

  return parts[1];
};

/**
 * Validate user input for registration
 * @param {Object} userData - User registration data
 * @returns {Object} - Validation result
 */
const validateRegistrationData = (userData) => {
  const errors = [];

  if (!userData.username || typeof userData.username !== 'string' || userData.username.trim().length < 3) {
    errors.push('Username must be at least 3 characters long');
  }

  if (!userData.email || typeof userData.email !== 'string' || !isValidEmail(userData.email)) {
    errors.push('Valid email address is required');
  }

  if (!userData.password || typeof userData.password !== 'string' || userData.password.length < 6) {
    errors.push('Password must be at least 6 characters long');
  }

  return {
    valid: errors.length === 0,
    errors
  };
};

/**
 * Simple email validation
 * @param {string} email - Email to validate
 * @returns {boolean} - True if valid email format
 */
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

module.exports = {
  hashPassword,
  comparePassword,
  generateToken,
  verifyToken,
  extractTokenFromHeader,
  validateRegistrationData,
  isValidEmail
};