#!/usr/bin/env python3
"""
Enhanced Response Builder for Graphiti Chat v2.0
================================================

Builds structured JSON responses from Graphiti search results with:
- Entity metadata extraction and ranking
- Source document URL extraction  
- Badge generation from categories
- Follow-up question generation
- Schema v2.0 compliance
"""

import json
import re
import time
from typing import Dict, List, Any, Optional, Tuple
from urllib.parse import urlparse
import hashlib

# Category color mapping for badges
CATEGORY_COLORS = {
    'EMS': '#40C4FF',      # Energy Management System - Blue
    'SEP': '#A5D6A7',      # Settlement/Processing - Green  
    'MMC': '#FFAB91',      # Market Making/Clearing - Orange
    'TRADING': '#E1BEE7',  # Trading - Purple
    'RISK': '#FFCDD2',     # Risk Management - Red
    'COMPLIANCE': '#FFF9C4', # Compliance - Yellow
    'WORKFLOW': '#C8E6C9', # Workflow - Light Green
    'SYSTEM': '#BBDEFB',   # System - Light Blue
    'DEFAULT': '#F5F5F5'   # Default - Gray
}

class ResponseBuilder:
    """Builds structured v2.0 responses from Graphiti search results"""
    
    def __init__(self):
        self.processing_start_time = time.time()
        
    def build_structured_response(
        self,
        answer: str,
        search_results,
        citations: List[str],
        question: str,
        search_metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Build a complete v2.0 structured response
        
        Args:
            answer: LLM-generated answer text
            search_results: Graphiti search results object with .edges and .nodes
            citations: List of citation references
            question: Original user question
            search_metadata: Additional metadata about the search
            
        Returns:
            Dict matching the v2.0 JSON schema
        """
        try:
            import sys
            # 🔍 DEBUG: Log search results structure
            print(f"🔍 ResponseBuilder.build_structured_response: search_results type = {type(search_results)}", file=sys.stderr)
            print(f"🔍 Has nodes: {hasattr(search_results, 'nodes')}", file=sys.stderr)
            print(f"🔍 Has edges: {hasattr(search_results, 'edges')}", file=sys.stderr)
            if hasattr(search_results, 'nodes'):
                print(f"🔍 Nodes count: {len(search_results.nodes)}", file=sys.stderr)
            if hasattr(search_results, 'edges'):
                print(f"🔍 Edges count: {len(search_results.edges)}", file=sys.stderr)

            # Extract entities with metadata - handle both dict and object formats
            nodes = []
            edges = []
            
            if isinstance(search_results, dict):
                nodes = search_results.get('nodes', [])
                edges = search_results.get('edges', [])
            else:
                nodes = getattr(search_results, 'nodes', [])
                edges = getattr(search_results, 'edges', [])
            
            entities = self._extract_entities(nodes)
            sources = self._extract_sources(edges)

            # 🔍 DEBUG: Log extraction results
            print(f"🔍 Extracted {len(entities)} entities, {len(sources)} sources", file=sys.stderr)
            
            # Generate badges from categories
            badges = self._generate_badges(entities, sources)
            
            # No longer parse sections - Format C JSON handles structure directly
            sections = []  # Empty sections for v2.0 compatibility
            
            # Generate follow-up questions
            follow_up = self._generate_follow_up_questions(question, entities, answer)
            
            # Build metadata
            metadata = self._build_metadata(search_metadata, len(entities), len(sources))
            
            # Extract passage count from enhanced_metadata for Atlas RAG
            passage_count = len(sources)  # Default to sources count
            if search_metadata:
                # Check for Atlas RAG specific metadata
                if 'returned' in search_metadata:
                    passage_count = search_metadata['returned'].get('passages', len(sources))
                elif 'atlas_rag' in search_metadata:
                    passage_count = search_metadata['atlas_rag'].get('returned', {}).get('passages', len(sources))
            
            # Clean and validate answer
            clean_answer = self._clean_answer_text(answer)
            
            response = {
                "version": "2.0",
                "answer": clean_answer,
                "sections": sections,
                "sources": sources,
                "entities": entities,
                "badges": badges,
                "follow_up": follow_up,
                "metadata": metadata,
                "source_count": passage_count  # Override with actual passage count
            }
            
            return response
            
        except Exception as e:
            # Return fallback response on error
            return self._build_fallback_response(answer, str(e))
    
    def _extract_entities(self, nodes: List[Any]) -> List[Dict[str, Any]]:
        """Extract and rank entities from Graphiti nodes"""
        entities = []
        
        import sys
        # 🔍 DEBUG: Log incoming nodes for entity extraction
        print(f"🔍 ResponseBuilder._extract_entities: Processing {len(nodes)} nodes", file=sys.stderr)
        print(f"🔍 Node types: {[type(node).__name__ for node in nodes[:3]]}", file=sys.stderr)  # Show first 3 types

        for i, node in enumerate(nodes):
            try:
                # 🔍 DEBUG: Log each node being processed
                print(f"🔍 Node {i+1}: type={type(node).__name__}, uuid={getattr(node, 'uuid', 'MISSING')}, name={getattr(node, 'name', 'MISSING')}", file=sys.stderr)
                print(f"🔍 Node {i+1}: labels={getattr(node, 'labels', 'MISSING')}, group_id={getattr(node, 'group_id', 'MISSING')}", file=sys.stderr)
                
                # Extract basic entity information
                entity = {
                    "id": getattr(node, 'uuid', ''),
                    "name": getattr(node, 'name', 'Unknown Entity'),
                    "relevance_score": float(getattr(node, 'score', 0.0))
                }
                
                # Add optional fields if available
                if hasattr(node, 'summary') and node.summary:
                    entity["description"] = node.summary[:500]  # Limit description length
                
                # Extract category from labels or group_id
                category = self._extract_category(node)
                if category:
                    entity["category"] = category
                
                # Add additional properties
                properties = {}
                if hasattr(node, 'labels') and node.labels:
                    properties["labels"] = node.labels
                if hasattr(node, 'group_id') and node.group_id:
                    properties["group_id"] = node.group_id
                if hasattr(node, 'created_at') and node.created_at:
                    properties["created_at"] = str(node.created_at)
                
                if properties:
                    entity["properties"] = properties
                
                # 🔍 DEBUG: Log extracted entity
                print(f"✅ Successfully extracted entity: {entity}", file=sys.stderr)
                entities.append(entity)

            except Exception as e:
                print(f"Warning: Failed to extract entity from node: {e}", file=sys.stderr)
                continue

        # Sort by relevance score (highest first)
        entities.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)

        # 🔍 DEBUG: Log final entity results
        print(f"🔍 Total entities extracted: {len(entities)}", file=sys.stderr)
        print(f"🔍 Top 3 entities: {[e.get('name', 'NO_NAME') for e in entities[:3]]}", file=sys.stderr)

        # Limit to top 15 entities
        final_entities = entities[:15]
        print(f"🔍 Final entities after limit: {len(final_entities)}", file=sys.stderr)
        return final_entities
    
    def _extract_sources(self, edges: List[Any]) -> List[Dict[str, Any]]:
        """Extract source documents with URLs from Graphiti edges"""
        sources = []
        seen_urls = set()
        
        for edge in edges:
            try:
                # Try to extract URL from edge properties or group_id
                url = self._extract_url_from_edge(edge)
                if not url or url in seen_urls:
                    continue
                
                seen_urls.add(url)
                
                # Generate source ID
                source_id = hashlib.md5(url.encode()).hexdigest()[:8]
                
                # Extract document title
                title = self._extract_document_title(edge, url)
                
                # Determine document type
                doc_type = self._determine_document_type(url)
                
                # Extract snippet from fact
                snippet = getattr(edge, 'fact', '')[:120] if hasattr(edge, 'fact') else ''
                
                # Extract category
                category = self._extract_category(edge)
                
                source = {
                    "id": source_id,
                    "title": title,
                    "url": url,
                    "document_type": doc_type
                }
                
                if snippet:
                    source["snippet"] = snippet
                if category:
                    source["category"] = category
                
                sources.append(source)
                
            except Exception as e:
                print(f"Warning: Failed to extract source from edge: {e}")
                continue
        
        # Limit to top 20 sources
        return sources[:20]
    
    def _extract_url_from_edge(self, edge: Any) -> Optional[str]:
        """Extract URL from edge properties or group_id"""
        # Check if group_id contains a URL pattern
        if hasattr(edge, 'group_id') and edge.group_id:
            # Look for URL patterns in group_id
            url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+\.(pdf|doc|docx|html|htm)'
            match = re.search(url_pattern, edge.group_id, re.IGNORECASE)
            if match:
                return match.group(0)
        
        # Check other properties for URLs
        for attr in ['source_url', 'document_url', 'url', 'file_path']:
            if hasattr(edge, attr):
                value = getattr(edge, attr)
                if value and isinstance(value, str) and value.startswith(('http://', 'https://')):
                    return value
        
        return None
    
    def _extract_document_title(self, edge: Any, url: str) -> str:
        """Extract document title from edge or URL"""
        # Try to get title from edge properties
        for attr in ['document_title', 'title', 'name']:
            if hasattr(edge, attr):
                value = getattr(edge, attr)
                if value and isinstance(value, str):
                    return value[:200]  # Limit title length
        
        # Extract from URL as fallback
        try:
            parsed = urlparse(url)
            filename = parsed.path.split('/')[-1]
            if filename:
                # Remove file extension and clean up
                title = filename.rsplit('.', 1)[0]
                title = title.replace('_', ' ').replace('-', ' ')
                return title[:200]
        except:
            pass
        
        return "Document"
    
    def _determine_document_type(self, url: str) -> str:
        """Determine document type from URL"""
        try:
            parsed = urlparse(url)
            path = parsed.path.lower()
            
            if path.endswith('.pdf'):
                return 'PDF'
            elif path.endswith(('.doc', '.docx')):
                return 'DOC'
            elif path.endswith(('.html', '.htm')):
                return 'WEB'
            else:
                return 'OTHER'
        except:
            return 'OTHER'
    
    def _extract_category(self, obj: Any) -> Optional[str]:
        """Extract category from node or edge"""
        # Check group_id for category hints
        if hasattr(obj, 'group_id') and obj.group_id:
            group_id = obj.group_id.lower()
            for category in CATEGORY_COLORS.keys():
                if category.lower() in group_id:
                    return category
        
        # Check labels for category
        if hasattr(obj, 'labels') and obj.labels:
            for label in obj.labels:
                if label.upper() in CATEGORY_COLORS:
                    return label.upper()
        
        return None
    
    def _generate_badges(self, entities: List[Dict], sources: List[Dict]) -> List[Dict[str, Any]]:
        """Generate category badges from entities and sources"""
        category_counts = {}
        
        # Count categories from entities
        for entity in entities:
            category = entity.get('category')
            if category:
                category_counts[category] = category_counts.get(category, 0) + 1
        
        # Count categories from sources  
        for source in sources:
            category = source.get('category')
            if category:
                category_counts[category] = category_counts.get(category, 0) + 1
        
        # Generate badges
        badges = []
        for category, count in category_counts.items():
            badge = {
                "label": category,
                "tooltip": self._get_category_tooltip(category),
                "count": count
            }
            
            # Add color if available
            color = CATEGORY_COLORS.get(category, CATEGORY_COLORS['DEFAULT'])
            badge["color"] = color
            
            badges.append(badge)
        
        # Sort by count (highest first)
        badges.sort(key=lambda x: x['count'], reverse=True)
        
        return badges[:10]  # Limit to 10 badges
    
    def _get_category_tooltip(self, category: str) -> str:
        """Get tooltip text for category"""
        tooltips = {
            'EMS': 'Energy Management System',
            'SEP': 'Settlement and Processing',
            'MMC': 'Market Making and Clearing',
            'TRADING': 'Trading Operations',
            'RISK': 'Risk Management',
            'COMPLIANCE': 'Compliance and Regulation',
            'WORKFLOW': 'Business Workflows',
            'SYSTEM': 'System Components'
        }
        return tooltips.get(category, f'{category} Category')
    
    # Removed _parse_answer_sections() - obsolete after Format C JSON refactoring
    # Both Atlas RAG and Graphiti now generate Format C JSON directly, eliminating
    # the need for markdown section parsing. This function was only used for the
    # legacy markdown → v2.0 JSON pipeline.
    
    def _generate_follow_up_questions(self, question: str, entities: List[Dict], answer: str) -> List[Dict[str, Any]]:
        """Generate follow-up questions based on entities and answer"""
        follow_ups = []
        
        # Extract key entities for follow-up generation
        top_entities = entities[:3]
        
        for entity in top_entities:
            entity_name = entity.get('name', '')
            category = entity.get('category', '')
            
            if entity_name and len(follow_ups) < 3:
                # Generate contextual follow-up
                if category == 'EMS':
                    follow_up = {
                        "question": f"How does {entity_name} integrate with other systems?",
                        "context_hint": "System integration"
                    }
                elif category == 'TRADING':
                    follow_up = {
                        "question": f"What are the risk controls for {entity_name}?",
                        "context_hint": "Risk management"
                    }
                else:
                    follow_up = {
                        "question": f"Tell me more about {entity_name}",
                        "context_hint": "Entity details"
                    }
                
                follow_ups.append(follow_up)
        
        # Add generic follow-ups if needed
        if len(follow_ups) < 2:
            follow_ups.append({
                "question": "What are the related processes?",
                "context_hint": "Process overview"
            })
        
        return follow_ups[:3]
    
    def _build_metadata(self, search_metadata: Dict[str, Any], entity_count: int, source_count: int) -> Dict[str, Any]:
        """Build response metadata by merging search engine metadata with processing metrics"""
        processing_time = int((time.time() - self.processing_start_time) * 1000)
        
        # Start with basic metadata
        metadata = {
            "processing_time_ms": processing_time,
            "entity_count": entity_count,
            "source_count": source_count,
            "fallback_used": False
        }
        
        # If search_metadata exists, merge it in (this preserves all rich metadata from search engine)
        if search_metadata:
            # First add the basic fields for compatibility
            metadata.update({
                "search_type": search_metadata.get("search_type", "unknown"),
                "token_usage": search_metadata.get("token_usage", 0),
                "confidence_score": search_metadata.get("confidence_score", 0.0)
            })
            
            # Then add all the rich metadata directly from search engine
            # This includes graphiti_search, llm_models, and any other metadata
            for key, value in search_metadata.items():
                if key not in metadata:  # Don't override basic metadata we already set
                    metadata[key] = value
        
        return metadata
    
    def _clean_answer_text(self, answer: str) -> str:
        """Clean and validate answer text"""
        if not answer:
            return "I don't have enough information to answer this question."
        
        # Remove excessive whitespace
        cleaned = re.sub(r'\n\s*\n\s*\n', '\n\n', answer.strip())
        
        # Limit length
        if len(cleaned) > 5000:
            cleaned = cleaned[:4997] + "..."
        
        return cleaned
    
    def _build_fallback_response(self, answer: str, error: str) -> Dict[str, Any]:
        """Build fallback response when structured parsing fails"""
        return {
            "version": "2.0",
            "answer": self._clean_answer_text(answer) if answer else "An error occurred processing the response.",
            "sections": [],
            "sources": [],
            "entities": [],
            "badges": [],
            "follow_up": [],
            "metadata": {
                "processing_time_ms": int((time.time() - self.processing_start_time) * 1000),
                "fallback_used": True,
                "error": error,
                "entity_count": 0,
                "source_count": 0
            }
        }
