#!/usr/bin/env python3
"""
Prompt Processor for Graphiti Chat v2.0
=======================================

Handles prompt template loading, token management, and context optimization
for structured JSON responses.
"""

import os
import json
import re
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

class PromptProcessor:
    """Processes and optimizes prompts for structured JSON responses"""
    
    def __init__(self, prompts_dir: str = None):
        self.prompts_dir = prompts_dir or os.path.join(os.path.dirname(__file__), '..', 'prompts')
        self.templates = {}
        self.load_templates()
    
    def load_templates(self):
        """Load all prompt templates from the prompts directory"""
        prompts_path = Path(self.prompts_dir)
        if not prompts_path.exists():
            print(f"Warning: Prompts directory not found: {self.prompts_dir}")
            return
        
        for template_file in prompts_path.glob('*.txt'):
            template_name = template_file.stem
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    self.templates[template_name] = f.read()
                print(f"✅ Loaded prompt template: {template_name}")
            except Exception as e:
                print(f"❌ Failed to load template {template_name}: {e}")
    
    def build_structured_prompt(
        self,
        question: str,
        context: str,
        conversation_history: List[Dict[str, str]] = None,
        template_name: str = "structured-chat-v2",
        max_tokens: int = 8000
    ) -> str:
        """
        Build a structured prompt for JSON response generation
        
        Args:
            question: User's question
            context: Knowledge graph context
            conversation_history: Previous conversation messages
            template_name: Name of the prompt template to use
            max_tokens: Maximum token limit for the prompt
            
        Returns:
            Formatted prompt string
        """
        if template_name not in self.templates:
            raise ValueError(f"Template '{template_name}' not found. Available: {list(self.templates.keys())}")
        
        template = self.templates[template_name]
        
        # Build conversation context
        conversation_context = self._build_conversation_context(conversation_history)
        
        # Optimize context length if needed
        optimized_context = self._optimize_context_length(context, max_tokens - 2000)  # Reserve tokens for template
        
        # Format the prompt
        formatted_prompt = template.format(
            question=question,
            context=optimized_context,
            conversation_context=conversation_context
        )
        
        # Final token check and truncation if needed
        if self._estimate_tokens(formatted_prompt) > max_tokens:
            formatted_prompt = self._truncate_prompt(formatted_prompt, max_tokens)
        
        return formatted_prompt
    
    def _build_conversation_context(self, conversation_history: List[Dict[str, str]] = None) -> str:
        """Build conversation context section from history"""
        if not conversation_history:
            return ""
        
        # Limit to last 6 messages to keep context manageable
        recent_history = conversation_history[-6:] if len(conversation_history) > 6 else conversation_history
        
        if not recent_history:
            return ""
        
        context_lines = ["**CONVERSATION HISTORY:**"]
        for msg in recent_history:
            role = msg.get('role', 'unknown').upper()
            content = msg.get('content', '')[:200]  # Truncate long messages
            if content:
                context_lines.append(f"**{role}:** {content}...")
        
        context_lines.append("")  # Add blank line
        return "\n".join(context_lines)
    
    def _optimize_context_length(self, context: str, max_context_tokens: int) -> str:
        """Optimize context length while preserving important information"""
        estimated_tokens = self._estimate_tokens(context)
        
        if estimated_tokens <= max_context_tokens:
            return context
        
        # Split context into sections and prioritize
        sections = context.split('\n\n')
        prioritized_sections = []
        
        for section in sections:
            # Prioritize sections with entity references [1], [2], etc.
            if re.search(r'\[\d+\]', section):
                prioritized_sections.insert(0, section)  # High priority
            else:
                prioritized_sections.append(section)  # Lower priority
        
        # Rebuild context within token limit
        optimized_context = ""
        current_tokens = 0
        
        for section in prioritized_sections:
            section_tokens = self._estimate_tokens(section)
            if current_tokens + section_tokens <= max_context_tokens:
                optimized_context += section + "\n\n"
                current_tokens += section_tokens
            else:
                # Try to fit a truncated version
                remaining_tokens = max_context_tokens - current_tokens
                if remaining_tokens > 100:  # Only if we have reasonable space left
                    truncated_section = self._truncate_text(section, remaining_tokens)
                    optimized_context += truncated_section + "...\n\n"
                break
        
        return optimized_context.strip()
    
    def _estimate_tokens(self, text: str) -> int:
        """Estimate token count (rough approximation: 1 token ≈ 4 characters)"""
        return len(text) // 4
    
    def _truncate_text(self, text: str, max_tokens: int) -> str:
        """Truncate text to approximately max_tokens"""
        max_chars = max_tokens * 4
        if len(text) <= max_chars:
            return text
        
        # Try to truncate at sentence boundaries
        truncated = text[:max_chars]
        last_sentence = truncated.rfind('.')
        if last_sentence > max_chars * 0.8:  # If we can keep most of the text
            return truncated[:last_sentence + 1]
        
        return truncated
    
    def _truncate_prompt(self, prompt: str, max_tokens: int) -> str:
        """Truncate prompt if it exceeds token limit"""
        max_chars = max_tokens * 4
        if len(prompt) <= max_chars:
            return prompt
        
        # Find the context section and truncate it
        context_start = prompt.find("**KNOWLEDGE GRAPH CONTEXT:**")
        if context_start == -1:
            # Fallback: truncate from the end
            return prompt[:max_chars]
        
        context_end = prompt.find("**USER QUESTION:**", context_start)
        if context_end == -1:
            return prompt[:max_chars]
        
        # Calculate how much we need to remove
        excess_chars = len(prompt) - max_chars
        context_section = prompt[context_start:context_end]
        
        if len(context_section) > excess_chars + 100:  # Keep some buffer
            truncated_context = context_section[:-excess_chars - 100] + "\n\n[Context truncated for length]\n\n"
            return prompt[:context_start] + truncated_context + prompt[context_end:]
        
        return prompt[:max_chars]
    
    def validate_json_response(self, response: str) -> Tuple[bool, Optional[Dict], Optional[str]]:
        """
        Validate and parse JSON response from LLM
        
        Returns:
            (is_valid, parsed_json, error_message)
        """
        # Clean the response - remove any text before/after JSON
        cleaned_response = self._extract_json_from_response(response)
        
        if not cleaned_response:
            return False, None, "No JSON found in response"
        
        try:
            parsed = json.loads(cleaned_response)
            
            # Basic structure validation
            required_fields = ['version', 'answer', 'sections', 'sources', 'entities', 'badges', 'follow_up', 'metadata']
            missing_fields = [field for field in required_fields if field not in parsed]
            
            if missing_fields:
                return False, parsed, f"Missing required fields: {missing_fields}"
            
            return True, parsed, None
            
        except json.JSONDecodeError as e:
            return False, None, f"JSON parsing error: {e}"
    
    def _extract_json_from_response(self, response: str) -> Optional[str]:
        """Extract JSON object from LLM response"""
        # Remove common prefixes/suffixes
        response = response.strip()
        
        # Look for JSON object boundaries
        start_idx = response.find('{')
        if start_idx == -1:
            return None
        
        # Find the matching closing brace
        brace_count = 0
        end_idx = -1
        
        for i, char in enumerate(response[start_idx:], start_idx):
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    end_idx = i
                    break
        
        if end_idx == -1:
            return None
        
        return response[start_idx:end_idx + 1]
    
    def repair_json_response(self, response: str) -> Tuple[bool, Optional[str]]:
        """
        Attempt to repair common JSON issues in LLM responses
        
        Returns:
            (was_repaired, repaired_json)
        """
        if not response:
            return False, None
        
        # Extract JSON portion
        json_str = self._extract_json_from_response(response)
        if not json_str:
            return False, None
        
        # Common repairs
        repaired = json_str
        
        # Fix trailing commas
        repaired = re.sub(r',(\s*[}\]])', r'\1', repaired)
        
        # Fix unescaped quotes in strings
        repaired = self._fix_unescaped_quotes(repaired)
        
        # Fix missing quotes around keys
        repaired = re.sub(r'(\w+):', r'"\1":', repaired)
        
        # Try to parse the repaired version
        try:
            json.loads(repaired)
            return True, repaired
        except json.JSONDecodeError:
            return False, None
    
    def _fix_unescaped_quotes(self, json_str: str) -> str:
        """Fix unescaped quotes within string values"""
        # This is a simplified approach - in production, you'd want more robust parsing
        # For now, just escape quotes that appear to be within string values
        return json_str.replace('\\"', '"').replace('"', '\\"').replace('\\"\\"', '\\"')
    
    def get_template_info(self, template_name: str) -> Dict[str, Any]:
        """Get information about a specific template"""
        if template_name not in self.templates:
            return {"error": f"Template '{template_name}' not found"}
        
        template = self.templates[template_name]
        return {
            "name": template_name,
            "length": len(template),
            "estimated_tokens": self._estimate_tokens(template),
            "placeholders": re.findall(r'\{(\w+)\}', template),
            "has_examples": "EXAMPLE RESPONSE" in template,
            "has_schema": "JSON SCHEMA" in template
        }
    
    def list_templates(self) -> List[str]:
        """List all available templates"""
        return list(self.templates.keys())
