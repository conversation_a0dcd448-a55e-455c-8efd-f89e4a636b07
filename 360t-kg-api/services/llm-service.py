#!/usr/bin/env python3
"""
LLM Service for Graphiti Chat v2.0
==================================

Handles LLM integration with streaming JSON parsing, response validation,
and fallback mechanisms for structured responses.
"""

import json
import re
import time
import asyncio
import aiohttp
from typing import Dict, List, Any, Optional, Tuple, AsyncGenerator
from dataclasses import dataclass

@dataclass
class LLMResponse:
    """Structured LLM response with metadata"""
    content: str
    is_valid_json: bool
    parsed_json: Optional[Dict[str, Any]]
    processing_time_ms: int
    token_usage: int
    fallback_used: bool
    error: Optional[str] = None

class StreamingJSONParser:
    """Parses JSON from streaming LLM responses"""
    
    def __init__(self):
        self.buffer = ""
        self.json_started = False
        self.brace_count = 0
        self.in_string = False
        self.escape_next = False
    
    def add_chunk(self, chunk: str) -> Optional[str]:
        """
        Add a chunk of text and return complete JSON if found
        
        Returns:
            Complete JSON string if found, None otherwise
        """
        self.buffer += chunk
        
        # Look for JSON start if not found yet
        if not self.json_started:
            start_idx = self.buffer.find('{')
            if start_idx >= 0:
                self.json_started = True
                self.buffer = self.buffer[start_idx:]
                self.brace_count = 0
        
        if not self.json_started:
            return None
        
        # Parse character by character to find complete JSON
        start_pos = 0 if self.brace_count == 0 else len(self.buffer) - len(chunk)

        for i, char in enumerate(self.buffer[start_pos:], start_pos):
            if self.escape_next:
                self.escape_next = False
                continue

            if char == '\\':
                self.escape_next = True
                continue

            if char == '"' and not self.escape_next:
                self.in_string = not self.in_string
                continue

            if self.in_string:
                continue

            if char == '{':
                self.brace_count += 1
            elif char == '}':
                self.brace_count -= 1

                # Complete JSON found
                if self.brace_count == 0:
                    json_str = self.buffer[:i + 1]
                    self.buffer = self.buffer[i + 1:]
                    self.reset()
                    return json_str
        
        return None
    
    def reset(self):
        """Reset parser state"""
        self.json_started = False
        self.brace_count = 0
        self.in_string = False
        self.escape_next = False
    
    def get_partial_json(self) -> str:
        """Get current buffer content"""
        return self.buffer

class LLMService:
    """Service for LLM integration with structured JSON responses"""
    
    def __init__(self, base_url: str = "http://localhost:11434"):
        self.base_url = base_url.rstrip('/')
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def generate_structured_response(
        self,
        prompt: str,
        model: str = "deepseek-r1:8b",
        temperature: float = 0.3,
        timeout: int = 180,
        stream: bool = True
    ) -> LLMResponse:
        """
        Generate structured JSON response from LLM
        
        Args:
            prompt: Formatted prompt for structured response
            model: LLM model name
            temperature: Generation temperature
            timeout: Request timeout in seconds
            stream: Whether to use streaming response
            
        Returns:
            LLMResponse with parsed JSON and metadata
        """
        start_time = time.time()
        
        try:
            if stream:
                return await self._generate_streaming(prompt, model, temperature, timeout, start_time)
            else:
                return await self._generate_non_streaming(prompt, model, temperature, timeout, start_time)
        
        except asyncio.TimeoutError:
            return LLMResponse(
                content="",
                is_valid_json=False,
                parsed_json=None,
                processing_time_ms=int((time.time() - start_time) * 1000),
                token_usage=0,
                fallback_used=True,
                error="Request timeout"
            )
        except Exception as e:
            return LLMResponse(
                content="",
                is_valid_json=False,
                parsed_json=None,
                processing_time_ms=int((time.time() - start_time) * 1000),
                token_usage=0,
                fallback_used=True,
                error=str(e)
            )
    
    async def _generate_streaming(
        self,
        prompt: str,
        model: str,
        temperature: float,
        timeout: int,
        start_time: float
    ) -> LLMResponse:
        """Generate response using streaming API"""
        url = f"{self.base_url}/api/generate"
        
        payload = {
            "model": model,
            "prompt": prompt,
            "temperature": temperature,
            "stream": True,
            "options": {
                "num_predict": 2000,  # Limit response length
                "stop": ["</response>", "\n\n---"]  # Stop sequences
            }
        }
        
        parser = StreamingJSONParser()
        full_content = ""
        token_count = 0
        
        async with self.session.post(url, json=payload, timeout=timeout) as response:
            if response.status != 200:
                raise Exception(f"LLM API error: {response.status}")
            
            async for line in response.content:
                if not line:
                    continue
                
                try:
                    chunk_data = json.loads(line.decode('utf-8'))
                    if 'response' in chunk_data:
                        chunk_text = chunk_data['response']
                        full_content += chunk_text
                        token_count += 1  # Rough token count
                        
                        # Try to parse JSON from stream
                        complete_json = parser.add_chunk(chunk_text)
                        if complete_json:
                            # We have complete JSON, validate it
                            return self._create_response(
                                complete_json, start_time, token_count, False
                            )
                    
                    if chunk_data.get('done', False):
                        break
                        
                except json.JSONDecodeError:
                    continue
        
        # No complete JSON found in stream, try to extract from full content
        return self._create_response(full_content, start_time, token_count, True)
    
    async def _generate_non_streaming(
        self,
        prompt: str,
        model: str,
        temperature: float,
        timeout: int,
        start_time: float
    ) -> LLMResponse:
        """Generate response using non-streaming API"""
        url = f"{self.base_url}/api/generate"
        
        payload = {
            "model": model,
            "prompt": prompt,
            "temperature": temperature,
            "stream": False,
            "options": {
                "num_predict": 2000,
                "stop": ["</response>", "\n\n---"]
            }
        }
        
        async with self.session.post(url, json=payload, timeout=timeout) as response:
            if response.status != 200:
                raise Exception(f"LLM API error: {response.status}")
            
            result = await response.json()
            content = result.get('response', '')
            token_count = result.get('eval_count', len(content.split()))
            
            return self._create_response(content, start_time, token_count, False)
    
    def _create_response(
        self,
        content: str,
        start_time: float,
        token_count: int,
        fallback_used: bool
    ) -> LLMResponse:
        """Create LLMResponse from content"""
        processing_time = int((time.time() - start_time) * 1000)
        
        # Try to extract and validate JSON
        json_str = self._extract_json(content)
        if json_str:
            try:
                parsed_json = json.loads(json_str)
                return LLMResponse(
                    content=json_str,
                    is_valid_json=True,
                    parsed_json=parsed_json,
                    processing_time_ms=processing_time,
                    token_usage=token_count,
                    fallback_used=fallback_used
                )
            except json.JSONDecodeError as e:
                # Try to repair JSON
                repaired_json = self._repair_json(json_str)
                if repaired_json:
                    try:
                        parsed_json = json.loads(repaired_json)
                        return LLMResponse(
                            content=repaired_json,
                            is_valid_json=True,
                            parsed_json=parsed_json,
                            processing_time_ms=processing_time,
                            token_usage=token_count,
                            fallback_used=True
                        )
                    except json.JSONDecodeError:
                        pass
                
                return LLMResponse(
                    content=content,
                    is_valid_json=False,
                    parsed_json=None,
                    processing_time_ms=processing_time,
                    token_usage=token_count,
                    fallback_used=True,
                    error=f"JSON parsing failed: {e}"
                )
        
        return LLMResponse(
            content=content,
            is_valid_json=False,
            parsed_json=None,
            processing_time_ms=processing_time,
            token_usage=token_count,
            fallback_used=True,
            error="No JSON found in response"
        )
    
    def _extract_json(self, content: str) -> Optional[str]:
        """Extract JSON from LLM response"""
        # Remove common prefixes
        content = content.strip()
        
        # Look for JSON object
        start_idx = content.find('{')
        if start_idx == -1:
            return None
        
        # Find matching closing brace
        brace_count = 0
        end_idx = -1
        
        for i, char in enumerate(content[start_idx:], start_idx):
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    end_idx = i
                    break
        
        if end_idx == -1:
            return None
        
        return content[start_idx:end_idx + 1]
    
    def _repair_json(self, json_str: str) -> Optional[str]:
        """Attempt to repair common JSON issues"""
        if not json_str:
            return None
        
        # Fix trailing commas
        repaired = re.sub(r',(\s*[}\]])', r'\1', json_str)
        
        # Fix unescaped quotes (basic approach)
        repaired = repaired.replace('\\"', '"').replace('"', '\\"')
        repaired = repaired.replace('\\"\\"', '\\"')
        
        # Fix missing quotes around keys (basic approach)
        repaired = re.sub(r'(\w+):', r'"\1":', repaired)
        
        return repaired
    
    async def health_check(self) -> bool:
        """Check if LLM service is available"""
        try:
            url = f"{self.base_url}/api/tags"
            async with self.session.get(url, timeout=5) as response:
                return response.status == 200
        except:
            return False
