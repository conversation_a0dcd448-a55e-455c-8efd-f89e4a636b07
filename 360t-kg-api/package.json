{"name": "360t-kg-api", "version": "1.0.0", "description": "360T Knowledge Graph API with chat functionality", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "mocha test-chat-e2e.js --timeout 300000", "test:e2e": "mocha test-chat-e2e.js --timeout 300000", "test:api": "mocha test-chat-api.js --timeout 300000", "test:watch": "mocha test-chat-e2e.js --timeout 300000 --watch", "lint": "eslint .", "format": "prettier --write ."}, "dependencies": {"ajv": "^8.17.1", "ajv-formats": "^3.0.1", "axios": "^1.11.0", "bcrypt": "^6.0.0", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "neo4j-driver": "^5.25.0", "pg": "^8.8.0", "prom-client": "^15.1.3", "redis": "^4.5.1", "uuid": "^9.0.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"chai": "^4.5.0", "eslint": "^8.36.0", "mocha": "^10.8.2", "nodemon": "^2.0.20", "prettier": "^2.8.4", "puppeteer": "^19.11.1"}, "engines": {"node": ">=16.0.0"}, "keywords": ["knowledge-graph", "chat", "api", "360t"], "author": "360T Team", "license": "MIT"}