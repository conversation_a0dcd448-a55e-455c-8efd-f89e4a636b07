-- ============================================
-- Migration: Convert Integer IDs to UUIDs
-- WARNING: This migration is irreversible!
-- Make sure to backup your database before running!
-- ============================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Step 1: Add new UUID columns to all tables
ALTER TABLE users ADD COLUMN uuid_id UUID DEFAULT uuid_generate_v4();
ALTER TABLE chat_sessions ADD COLUMN uuid_id UUID DEFAULT uuid_generate_v4();
ALTER TABLE chat_sessions ADD COLUMN uuid_user_id UUID;
ALTER TABLE messages ADD COLUMN uuid_id UUID DEFAULT uuid_generate_v4();
ALTER TABLE messages ADD COLUMN uuid_session_id UUID;
ALTER TABLE messages ADD COLUMN uuid_user_id UUID;
ALTER TABLE messages ADD COLUMN uuid_conversation_id UUID;

-- Step 2: Create mapping for users
UPDATE users SET uuid_id = uuid_generate_v4() WHERE uuid_id IS NULL;

-- Step 3: Update chat_sessions with UUID references
UPDATE chat_sessions cs 
SET uuid_user_id = u.uuid_id 
FROM users u 
WHERE cs.user_id = u.id;

-- Step 4: Update messages with UUID references
UPDATE messages m 
SET uuid_session_id = cs.uuid_id,
    uuid_user_id = u.uuid_id,
    uuid_conversation_id = cs.uuid_id
FROM chat_sessions cs
LEFT JOIN users u ON u.id = m.user_id
WHERE m.session_id = cs.id OR m.conversation_id = cs.id;

-- Step 5: Drop old foreign key constraints
ALTER TABLE chat_sessions DROP CONSTRAINT IF EXISTS chat_sessions_user_id_fkey;
ALTER TABLE messages DROP CONSTRAINT IF EXISTS messages_session_id_fkey;
ALTER TABLE messages DROP CONSTRAINT IF EXISTS messages_conversation_id_fkey;
ALTER TABLE messages DROP CONSTRAINT IF EXISTS messages_user_id_fkey;

-- Step 6: Drop old columns and rename new ones
ALTER TABLE users DROP COLUMN id CASCADE;
ALTER TABLE users RENAME COLUMN uuid_id TO id;
ALTER TABLE users ADD PRIMARY KEY (id);

ALTER TABLE chat_sessions DROP COLUMN id CASCADE;
ALTER TABLE chat_sessions RENAME COLUMN uuid_id TO id;
ALTER TABLE chat_sessions DROP COLUMN user_id;
ALTER TABLE chat_sessions RENAME COLUMN uuid_user_id TO user_id;
ALTER TABLE chat_sessions ADD PRIMARY KEY (id);

ALTER TABLE messages DROP COLUMN id CASCADE;
ALTER TABLE messages RENAME COLUMN uuid_id TO id;
ALTER TABLE messages DROP COLUMN session_id;
ALTER TABLE messages RENAME COLUMN uuid_session_id TO session_id;
ALTER TABLE messages DROP COLUMN user_id;
ALTER TABLE messages RENAME COLUMN uuid_user_id TO user_id;
ALTER TABLE messages DROP COLUMN conversation_id;
ALTER TABLE messages RENAME COLUMN uuid_conversation_id TO session_id_alt;
ALTER TABLE messages ADD PRIMARY KEY (id);

-- Step 7: Re-add foreign key constraints
ALTER TABLE chat_sessions 
ADD CONSTRAINT chat_sessions_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE messages 
ADD CONSTRAINT messages_session_id_fkey 
FOREIGN KEY (session_id) REFERENCES chat_sessions(id) ON DELETE CASCADE;

ALTER TABLE messages 
ADD CONSTRAINT messages_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL;

-- Step 8: Recreate indexes with UUID columns
DROP INDEX IF EXISTS idx_chat_sessions_user_id;
CREATE INDEX idx_chat_sessions_user_id ON chat_sessions(user_id);

DROP INDEX IF EXISTS idx_messages_session_id;
CREATE INDEX idx_messages_session_id ON messages(session_id);

DROP INDEX IF EXISTS idx_messages_user_id;
CREATE INDEX idx_messages_user_id ON messages(user_id);

-- Step 9: Add any missing columns from new schema
ALTER TABLE users ADD COLUMN IF NOT EXISTS avatar_url TEXT;
ALTER TABLE messages ADD COLUMN IF NOT EXISTS parent_message_id UUID REFERENCES messages(id) ON DELETE SET NULL;
ALTER TABLE messages ADD COLUMN IF NOT EXISTS role VARCHAR(20);

-- Update role column with default values if needed
UPDATE messages SET role = 'user' WHERE role IS NULL;

-- Add check constraint for role
ALTER TABLE messages ADD CONSTRAINT messages_role_check CHECK (role IN ('user', 'assistant', 'system'));

-- Step 10: Create missing tables from new schema if they don't exist
CREATE TABLE IF NOT EXISTS kg_nodes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    node_type VARCHAR(50) NOT NULL,
    label VARCHAR(255) NOT NULL,
    content TEXT,
    properties JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS kg_edges (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    source_node_id UUID NOT NULL REFERENCES kg_nodes(id) ON DELETE CASCADE,
    target_node_id UUID NOT NULL REFERENCES kg_nodes(id) ON DELETE CASCADE,
    edge_type VARCHAR(50) NOT NULL,
    properties JSONB DEFAULT '{}',
    weight FLOAT DEFAULT 1.0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS kg_context (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    node_id UUID REFERENCES kg_nodes(id) ON DELETE CASCADE,
    session_id UUID REFERENCES chat_sessions(id) ON DELETE CASCADE,
    context_type VARCHAR(50) NOT NULL,
    relevance_score FLOAT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS user_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    preference_key VARCHAR(100) NOT NULL,
    preference_value JSONB NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, preference_key)
);

CREATE TABLE IF NOT EXISTS api_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    status_code INTEGER,
    response_time_ms INTEGER,
    request_size INTEGER,
    response_size INTEGER,
    error_message TEXT,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS file_uploads (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100),
    metadata JSONB DEFAULT '{}',
    is_processed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS error_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    error_type VARCHAR(50) NOT NULL,
    error_message TEXT NOT NULL,
    stack_trace TEXT,
    context JSONB DEFAULT '{}',
    severity VARCHAR(20) DEFAULT 'error',
    resolved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Final verification
SELECT 'Migration completed successfully!' AS status;