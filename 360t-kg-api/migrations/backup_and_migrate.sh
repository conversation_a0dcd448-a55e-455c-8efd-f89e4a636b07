#!/bin/bash

# ============================================
# Safe Migration Script: Integer IDs to UUIDs
# ============================================

set -e  # Exit on error

DB_NAME="360t_kg"
DB_USER="postgres"
DB_HOST="localhost"
BACKUP_FILE="360t_kg_backup_$(date +%Y%m%d_%H%M%S).sql"

echo "============================================"
echo "Starting safe migration to UUID schema"
echo "============================================"

# Step 1: Create backup
echo "Step 1: Creating database backup..."
pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME > $BACKUP_FILE
echo "Backup created: $BACKUP_FILE"

# Step 2: Check if we have data to preserve
echo "Step 2: Checking existing data..."
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "SELECT COUNT(*) as user_count FROM users;"
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "SELECT COUNT(*) as session_count FROM chat_sessions;"
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "SELECT COUNT(*) as message_count FROM messages;"

# Step 3: Ask for confirmation
echo ""
echo "WARNING: This will migrate the database schema from integer IDs to UUIDs."
echo "A backup has been created at: $BACKUP_FILE"
echo ""
read -p "Do you want to proceed with the migration? (yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    echo "Migration cancelled."
    exit 0
fi

# Step 4: Run the migration
echo "Step 4: Running migration..."
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -f 001_migrate_to_uuid.sql

echo ""
echo "============================================"
echo "Migration completed successfully!"
echo "============================================"
echo ""
echo "If you need to restore the backup, run:"
echo "psql -h $DB_HOST -U $DB_USER -d $DB_NAME < $BACKUP_FILE"
echo ""