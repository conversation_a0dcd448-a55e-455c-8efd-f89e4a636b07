<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0.0000019073486328125 1138.445556640625 3848.000732421875" style="max-width: 1138.445556640625px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-dac54a08-1231-4957-80c7-62006936d1b9"><style>#mermaid-dac54a08-1231-4957-80c7-62006936d1b9{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .error-icon{fill:#552222;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .error-text{fill:#552222;stroke:#552222;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .edge-thickness-normal{stroke-width:1px;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .marker{fill:#333333;stroke:#333333;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .marker.cross{stroke:#333333;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 p{margin:0;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .cluster-label text{fill:#333;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .cluster-label span{color:#333;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .cluster-label span p{background-color:transparent;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .label text,#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 span{fill:#333;color:#333;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .node rect,#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .node circle,#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .node ellipse,#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .node polygon,#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .rough-node .label text,#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .node .label text,#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .image-shape .label,#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .icon-shape .label{text-anchor:middle;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .rough-node .label,#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .node .label,#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .image-shape .label,#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .icon-shape .label{text-align:center;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .node.clickable{cursor:pointer;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .arrowheadPath{fill:#333333;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .edgePath .path{stroke:#333333;stroke-width:2.0px;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .flowchart-link{stroke:#333333;fill:none;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .cluster text{fill:#333;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .cluster span{color:#333;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 rect.text{fill:none;stroke-width:0;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .icon-shape,#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .icon-shape p,#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .icon-shape rect,#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaid-dac54a08-1231-4957-80c7-62006936d1b9 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M681.378,61.998L681.378,66.165C681.378,70.331,681.378,78.665,681.378,86.331C681.378,93.998,681.378,100.998,681.378,104.498L681.378,107.998"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_1" d="M681.378,165.996L681.378,170.163C681.378,174.33,681.378,182.663,681.378,190.33C681.378,197.996,681.378,204.996,681.378,208.496L681.378,211.996"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_2" d="M681.378,269.994L681.378,274.161C681.378,278.328,681.378,286.661,681.378,294.328C681.378,301.994,681.378,308.994,681.378,312.494L681.378,315.994"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E_3" d="M681.378,373.992L681.378,378.159C681.378,382.326,681.378,390.659,681.378,398.326C681.378,405.992,681.378,412.992,681.378,416.492L681.378,419.992"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_F_4" d="M681.378,477.991L681.378,482.157C681.378,486.324,681.378,494.657,681.448,502.407C681.518,510.158,681.659,517.324,681.729,520.908L681.799,524.491"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_G_5" d="M625.278,646.159L596.277,661.676C567.277,677.193,509.277,708.226,480.277,729.242C451.277,750.258,451.277,761.258,451.277,766.758L451.277,772.258"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_H_6" d="M713.238,671.4L719.56,682.71C725.883,694.019,738.528,716.639,744.85,733.449C751.173,750.258,751.173,761.258,751.173,766.758L751.173,772.258"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_I_7" d="M451.277,830.256L451.277,834.423C451.277,838.589,451.277,846.923,451.277,854.589C451.277,862.256,451.277,869.256,451.277,872.756L451.277,876.256"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_J_8" d="M751.173,830.256L751.173,834.423C751.173,838.589,751.173,846.923,751.173,854.589C751.173,862.256,751.173,869.256,751.173,872.756L751.173,876.256"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_K_9" d="M451.277,934.254L451.277,938.421C451.277,942.587,451.277,950.921,471.475,959.652C491.673,968.383,532.068,977.512,552.266,982.076L572.464,986.64"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_K_10" d="M751.173,934.254L751.173,938.421C751.173,942.587,751.173,950.921,746.115,958.856C741.057,966.791,730.941,974.327,725.883,978.096L720.825,981.864"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_L_11" d="M611.753,1038.252L601.008,1042.419C590.263,1046.585,568.773,1054.919,558.028,1062.585C547.283,1070.252,547.283,1077.252,547.283,1080.752L547.283,1084.252"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_M_12" d="M717.617,1038.252L723.21,1042.419C728.802,1046.585,739.988,1054.919,745.58,1062.585C751.173,1070.252,751.173,1077.252,751.173,1080.752L751.173,1084.252"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_N_13" d="M547.283,1142.25L547.283,1146.417C547.283,1150.584,547.283,1158.917,557.406,1167.009C567.53,1175.101,587.776,1182.953,597.9,1186.878L608.023,1190.804"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_N_14" d="M751.173,1142.25L751.173,1146.417C751.173,1150.584,751.173,1158.917,746.115,1166.852C741.057,1174.787,730.941,1182.324,725.883,1186.092L720.825,1189.86"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_N_O_15" d="M572.599,1243.455L551.782,1248.087C530.965,1252.719,489.331,1261.984,468.513,1270.116C447.696,1278.248,447.696,1285.248,447.696,1288.748L447.696,1292.248"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_N_P_16" d="M717.617,1246.248L723.21,1250.415C728.802,1254.582,739.988,1262.915,745.58,1270.582C751.173,1278.248,751.173,1285.248,751.173,1288.748L751.173,1292.248"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_O_Q_17" d="M447.696,1350.246L447.696,1354.413C447.696,1358.58,447.696,1366.913,467.326,1375.448C486.955,1383.982,526.214,1392.718,545.844,1397.086L565.474,1401.454"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_P_Q_18" d="M751.173,1350.246L751.173,1354.413C751.173,1358.58,751.173,1366.913,746.115,1374.848C741.057,1382.783,730.941,1390.32,725.883,1394.088L720.825,1397.857"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_R_19" d="M569.378,1443.539L528.472,1449.49C487.565,1455.441,405.753,1467.343,364.846,1476.794C323.94,1486.245,323.94,1493.245,323.94,1496.745L323.94,1500.245"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_S_20" d="M646.032,1454.245L640.578,1458.411C635.123,1462.578,624.214,1470.911,618.759,1478.578C613.304,1486.245,613.304,1493.245,613.304,1496.745L613.304,1500.245"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_T_21" d="M789.201,1454.245L805.842,1458.411C822.482,1462.578,855.762,1470.911,872.402,1478.578C889.042,1486.245,889.042,1493.245,889.042,1496.745L889.042,1500.245"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_R_U_22" d="M323.94,1558.243L323.94,1562.409C323.94,1566.576,323.94,1574.909,364.097,1585.094C404.254,1595.279,484.569,1607.315,524.726,1613.334L564.883,1619.352"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_S_U_23" d="M613.304,1558.243L613.304,1562.409C613.304,1566.576,613.304,1574.909,617.425,1582.796C621.546,1590.683,629.789,1598.123,633.91,1601.843L638.031,1605.562"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_T_U_24" d="M889.042,1558.243L889.042,1562.409C889.042,1566.576,889.042,1574.909,870.347,1583.533C851.652,1592.156,814.262,1601.069,795.567,1605.526L776.872,1609.982"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_U_V_25" d="M670.91,1662.241L670.91,1666.407C670.91,1670.574,670.91,1678.907,670.91,1686.574C670.91,1694.241,670.91,1701.241,670.91,1704.741L670.91,1708.241"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_V_W_26" d="M670.91,1766.239L670.91,1770.406C670.91,1774.572,670.91,1782.906,670.91,1790.572C670.91,1798.239,670.91,1805.239,670.91,1808.739L670.91,1812.239"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_W_X_27" d="M670.91,1870.237L670.91,1874.404C670.91,1878.57,670.91,1886.904,670.91,1894.57C670.91,1902.237,670.91,1909.237,670.91,1912.737L670.91,1916.237"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_Y_28" d="M553.652,1968.313L524.981,1973.467C496.31,1978.621,438.968,1988.928,410.297,1997.582C381.626,2006.235,381.626,2013.235,381.626,2016.735L381.626,2020.235"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_Z_29" d="M645.379,1974.235L641.439,1978.402C637.499,1982.569,629.619,1990.902,625.679,1998.569C621.739,2006.235,621.739,2013.235,621.739,2016.735L621.739,2020.235"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_AA_30" d="M784.754,1974.235L802.323,1978.402C819.892,1982.569,855.03,1990.902,872.599,1998.569C890.168,2006.235,890.168,2013.235,890.168,2016.735L890.168,2020.235"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Y_BB_31" d="M381.626,2078.233L381.626,2082.4C381.626,2086.567,381.626,2094.9,397.847,2103.688C414.067,2112.476,446.509,2121.72,462.73,2126.341L478.951,2130.963"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Z_BB_32" d="M621.739,2078.233L621.739,2082.4C621.739,2086.567,621.739,2094.9,617.618,2102.787C613.497,2110.673,605.255,2118.113,601.134,2121.833L597.013,2125.553"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AA_BB_33" d="M890.168,2078.233L890.168,2082.4C890.168,2086.567,890.168,2094.9,850.043,2105.466C809.918,2116.032,729.668,2128.831,689.543,2135.231L649.418,2141.63"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BB_CC_34" d="M564.133,2182.231L564.133,2186.398C564.133,2190.565,564.133,2198.898,564.133,2206.565C564.133,2214.231,564.133,2221.231,564.133,2224.731L564.133,2228.231"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CC_DD_35" d="M564.133,2286.23L564.133,2290.396C564.133,2294.563,564.133,2302.896,564.133,2310.563C564.133,2318.23,564.133,2325.23,564.133,2328.73L564.133,2332.23"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DD_EE_36" d="M564.133,2390.228L564.133,2394.394C564.133,2398.561,564.133,2406.894,564.133,2414.561C564.133,2422.228,564.133,2429.228,564.133,2432.728L564.133,2436.228"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EE_FF_37" d="M564.133,2494.226L564.133,2498.392C564.133,2502.559,564.133,2510.892,564.203,2518.643C564.274,2526.393,564.414,2533.56,564.484,2537.143L564.555,2540.727"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FF_GG_38" d="M539.735,2651.702L533.393,2661.935C527.051,2672.168,514.367,2692.634,508.024,2708.366C501.682,2724.099,501.682,2735.099,501.682,2740.598L501.682,2746.098"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FF_HH_39" d="M611.088,2630.145L644.17,2643.971C677.252,2657.797,743.417,2685.448,776.499,2704.773C809.581,2724.099,809.581,2735.099,809.581,2740.598L809.581,2746.098"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_GG_II_40" d="M501.682,2804.096L501.682,2808.263C501.682,2812.43,501.682,2820.763,501.682,2828.43C501.682,2836.096,501.682,2843.096,501.682,2846.596L501.682,2850.096"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_HH_JJ_41" d="M809.581,2804.096L809.581,2808.263C809.581,2812.43,809.581,2820.763,809.581,2828.43C809.581,2836.096,809.581,2843.096,809.581,2846.596L809.581,2850.096"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_II_KK_42" d="M501.682,2908.095L501.682,2912.261C501.682,2916.428,501.682,2924.761,501.682,2932.428C501.682,2940.095,501.682,2947.095,501.682,2950.595L501.682,2954.095"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_JJ_LL_43" d="M809.581,2908.095L809.581,2912.261C809.581,2916.428,809.581,2924.761,809.581,2932.428C809.581,2940.095,809.581,2947.095,809.581,2950.595L809.581,2954.095"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_KK_MM_44" d="M501.682,3012.093L501.682,3016.259C501.682,3020.426,501.682,3028.759,501.682,3036.426C501.682,3044.093,501.682,3051.093,501.682,3054.593L501.682,3058.093"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LL_NN_45" d="M809.581,3012.093L809.581,3016.259C809.581,3020.426,809.581,3028.759,809.581,3036.426C809.581,3044.093,809.581,3051.093,809.581,3054.593L809.581,3058.093"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MM_OO_46" d="M501.682,3116.091L501.682,3120.257C501.682,3124.424,501.682,3132.757,506.174,3140.664C510.666,3148.571,519.65,3156.051,524.142,3159.791L528.633,3163.531"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NN_OO_47" d="M809.581,3116.091L809.581,3120.257C809.581,3124.424,809.581,3132.757,788.275,3141.438C766.969,3150.118,724.358,3159.146,703.052,3163.659L681.746,3168.173"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_OO_PP_48" d="M564.133,3220.089L564.133,3224.256C564.133,3228.422,564.133,3236.756,564.133,3244.422C564.133,3252.089,564.133,3259.089,564.133,3262.589L564.133,3266.089"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PP_QQ_49" d="M564.133,3324.087L564.133,3328.254C564.133,3332.42,564.133,3340.754,564.203,3348.504C564.274,3356.254,564.414,3363.421,564.484,3367.004L564.555,3370.588"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QQ_RR_50" d="M531.353,3471.229L518.966,3482.859C506.578,3494.488,481.803,3517.748,469.416,3534.877C457.028,3552.007,457.028,3563.007,457.028,3568.507L457.028,3574.007"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QQ_SS_51" d="M617.789,3451.352L685.367,3466.295C752.945,3481.237,888.101,3511.122,955.679,3531.565C1023.256,3552.007,1023.256,3563.007,1023.256,3568.507L1023.256,3574.007"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RR_TT_52" d="M355.644,3620.634L316.32,3626.696C276.996,3632.757,198.347,3644.881,159.023,3654.443C119.699,3664.005,119.699,3671.005,119.699,3674.505L119.699,3678.005"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RR_UU_53" d="M403.631,3632.005L395.391,3636.171C387.15,3640.338,370.669,3648.671,362.429,3656.338C354.188,3664.005,354.188,3671.005,354.188,3674.505L354.188,3678.005"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RR_VV_54" d="M510.425,3632.005L518.666,3636.171C526.906,3640.338,543.387,3648.671,551.628,3656.338C559.869,3664.005,559.869,3671.005,559.869,3674.505L559.869,3678.005"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RR_WW_55" d="M558.412,3621.358L595.246,3627.299C632.079,3633.24,705.746,3645.123,742.58,3654.564C779.413,3664.005,779.413,3671.005,779.413,3674.505L779.413,3678.005"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TT_XX_56" d="M119.699,3736.003L119.699,3740.169C119.699,3744.336,119.699,3752.669,177.803,3763.524C235.907,3774.378,352.114,3787.753,410.218,3794.441L468.322,3801.129"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UU_XX_57" d="M354.188,3736.003L354.188,3740.169C354.188,3744.336,354.188,3752.669,373.224,3761.392C392.26,3770.114,430.333,3779.225,449.369,3783.78L468.405,3788.336"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_VV_XX_58" d="M559.869,3736.003L559.869,3740.169C559.869,3744.336,559.869,3752.669,560.654,3760.352C561.439,3768.035,563.008,3775.067,563.793,3778.583L564.578,3782.099"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WW_XX_59" d="M779.413,3736.003L779.413,3740.169C779.413,3744.336,779.413,3752.669,761.934,3761.207C744.456,3769.745,709.498,3778.487,692.019,3782.858L674.54,3787.229"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SS_YY_60" d="M1023.256,3632.005L1023.256,3636.171C1023.256,3640.338,1023.256,3648.671,1023.256,3656.338C1023.256,3664.005,1023.256,3671.005,1023.256,3674.505L1023.256,3678.005"></path><path marker-end="url(#mermaid-dac54a08-1231-4957-80c7-62006936d1b9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_YY_XX_61" d="M1023.256,3736.003L1023.256,3740.169C1023.256,3744.336,1023.256,3752.669,965.153,3763.524C907.049,3774.378,790.841,3787.753,732.737,3794.441L674.634,3801.129"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(451.27686309814453, 739.2586841583252)" class="edgeLabel"><g transform="translate(-11.323151588439941, -11.999059677124023)" class="label"><foreignObject height="23.998119354248047" width="22.646303176879883"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g transform="translate(751.1730575561523, 739.2586841583252)" class="edgeLabel"><g transform="translate(-9.3994140625, -11.999059677124023)" class="label"><foreignObject height="23.998119354248047" width="18.798828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(501.6823081970215, 2713.099283218384)" class="edgeLabel"><g transform="translate(-11.323151588439941, -11.999059677124023)" class="label"><foreignObject height="23.998119354248047" width="22.646303176879883"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g transform="translate(809.5808982849121, 2713.099283218384)" class="edgeLabel"><g transform="translate(-9.3994140625, -11.999059677124023)" class="label"><foreignObject height="23.998119354248047" width="18.798828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(457.0283203125, 3541.007444381714)" class="edgeLabel"><g transform="translate(-11.323151588439941, -11.999059677124023)" class="label"><foreignObject height="23.998119354248047" width="22.646303176879883"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g transform="translate(1023.2564315795898, 3541.007444381714)" class="edgeLabel"><g transform="translate(-9.3994140625, -11.999059677124023)" class="label"><foreignObject height="23.998119354248047" width="18.798828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(681.3776779174805, 34.99905967712402)" id="flowchart-A-645" class="node default"><rect height="53.99811935424805" width="182.97905731201172" y="-26.999059677124023" x="-91.48952865600586" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-61.48952865600586, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="122.97905731201172"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User Types Query</p></span></div></foreignObject></g></g><g transform="translate(681.3776779174805, 138.99717903137207)" id="flowchart-B-646" class="node default"><rect height="53.99811935424805" width="151.8963394165039" y="-26.999059677124023" x="-75.94816970825195" style="" class="basic label-container"></rect><g transform="translate(-45.94816970825195, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="91.8963394165039"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ChatView.jsx</p></span></div></foreignObject></g></g><g transform="translate(681.3776779174805, 242.99529838562012)" id="flowchart-C-648" class="node default"><rect height="53.99811935424805" width="175.61415100097656" y="-26.999059677124023" x="-87.80707550048828" style="" class="basic label-container"></rect><g transform="translate(-57.80707550048828, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="115.61415100097656"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ChatContext.jsx</p></span></div></foreignObject></g></g><g transform="translate(681.3776779174805, 346.99341773986816)" id="flowchart-D-650" class="node default"><rect height="53.99811935424805" width="211.01004028320312" y="-26.999059677124023" x="-105.50502014160156" style="" class="basic label-container"></rect><g transform="translate(-75.50502014160156, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="151.01004028320312"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Get Graphiti Settings</p></span></div></foreignObject></g></g><g transform="translate(681.3776779174805, 450.9915370941162)" id="flowchart-E-652" class="node default"><rect height="53.99811935424805" width="184.2223663330078" y="-26.999059677124023" x="-92.1111831665039" style="" class="basic label-container"></rect><g transform="translate(-62.111183166503906, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="124.22236633300781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>chatApiService.js</p></span></div></foreignObject></g></g><g transform="translate(681.3776779174805, 615.1251106262207)" id="flowchart-F-654" class="node default"><polygon transform="translate(-87.13451194763184,87.13451194763184)" class="label-container" points="87.13451194763184,0 174.26902389526367,-87.13451194763184 87.13451194763184,-174.26902389526367 0,-87.13451194763184"></polygon><g transform="translate(-60.13545227050781, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="120.27090454101562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Streaming Mode?</p></span></div></foreignObject></g></g><g transform="translate(451.27686309814453, 803.2568035125732)" id="flowchart-G-656" class="node default"><rect height="53.99811935424805" width="259.9963836669922" y="-26.999059677124023" x="-129.9981918334961" style="" class="basic label-container"></rect><g transform="translate(-99.9981918334961, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="199.9963836669922"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>POST /api/chat/message/stream</p></span></div></foreignObject></g></g><g transform="translate(751.1730575561523, 803.2568035125732)" id="flowchart-H-658" class="node default"><rect height="53.99811935424805" width="239.79600524902344" y="-26.999059677124023" x="-119.89800262451172" style="" class="basic label-container"></rect><g transform="translate(-89.89800262451172, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="179.79600524902344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>POST /api/chat/message</p></span></div></foreignObject></g></g><g transform="translate(451.27686309814453, 907.2549228668213)" id="flowchart-I-660" class="node default"><rect height="53.99811935424805" width="244.67881774902344" y="-26.999059677124023" x="-122.33940887451172" style="" class="basic label-container"></rect><g transform="translate(-92.33940887451172, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="184.67881774902344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>chatRoutes.js - Streaming</p></span></div></foreignObject></g></g><g transform="translate(751.1730575561523, 907.2549228668213)" id="flowchart-J-662" class="node default"><rect height="53.99811935424805" width="226.17840576171875" y="-26.999059677124023" x="-113.08920288085938" style="" class="basic label-container"></rect><g transform="translate(-83.08920288085938, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="166.17840576171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>chatRoutes.js - Regular</p></span></div></foreignObject></g></g><g transform="translate(681.3776779174805, 1011.2530422210693)" id="flowchart-K-664" class="node default"><rect height="53.99811935424805" width="210.0244140625" y="-26.999059677124023" x="-105.01220703125" style="" class="basic label-container"></rect><g transform="translate(-75.01220703125, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="150.0244140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Validate Connections</p></span></div></foreignObject></g></g><g transform="translate(547.2829055786133, 1115.2511615753174)" id="flowchart-L-668" class="node default"><rect height="53.99811935424805" width="149.62673950195312" y="-26.999059677124023" x="-74.81336975097656" style="" class="basic label-container"></rect><g transform="translate(-44.81336975097656, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="89.62673950195312"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Neo4j Check</p></span></div></foreignObject></g></g><g transform="translate(751.1730575561523, 1115.2511615753174)" id="flowchart-M-670" class="node default"><rect height="53.99811935424805" width="158.15357208251953" y="-26.999059677124023" x="-79.07678604125977" style="" class="basic label-container"></rect><g transform="translate(-49.076786041259766, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="98.15357208251953"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Ollama Check</p></span></div></foreignObject></g></g><g transform="translate(681.3776779174805, 1219.2492809295654)" id="flowchart-N-672" class="node default"><rect height="53.99811935424805" width="217.55662536621094" y="-26.999059677124023" x="-108.77831268310547" style="" class="basic label-container"></rect><g transform="translate(-78.77831268310547, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="157.55662536621094"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Execute Python Script</p></span></div></foreignObject></g></g><g transform="translate(447.69612884521484, 1323.2474002838135)" id="flowchart-O-676" class="node default"><rect height="53.99811935424805" width="259.9963836669922" y="-26.999059677124023" x="-129.9981918334961" style="" class="basic label-container"></rect><g transform="translate(-99.9981918334961, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="199.9963836669922"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>graphiti_hybrid_search_streaming.py</p></span></div></foreignObject></g></g><g transform="translate(751.1730575561523, 1323.2474002838135)" id="flowchart-P-678" class="node default"><rect height="53.99811935424805" width="246.95745849609375" y="-26.999059677124023" x="-123.47872924804688" style="" class="basic label-container"></rect><g transform="translate(-93.47872924804688, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="186.95745849609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>graphiti_hybrid_search.py</p></span></div></foreignObject></g></g><g transform="translate(681.3776779174805, 1427.2455196380615)" id="flowchart-Q-680" class="node default"><rect height="53.99811935424805" width="223.99920654296875" y="-26.999059677124023" x="-111.99960327148438" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-81.99960327148438, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="163.99920654296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Build Graphiti Instance</p></span></div></foreignObject></g></g><g transform="translate(323.94003677368164, 1531.2436389923096)" id="flowchart-R-684" class="node default"><rect height="53.99811935424805" width="238.3492431640625" y="-26.999059677124023" x="-119.17462158203125" style="" class="basic label-container"></rect><g transform="translate(-89.17462158203125, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="178.3492431640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>OpenAI Embedder Config</p></span></div></foreignObject></g></g><g transform="translate(613.304271697998, 1531.2436389923096)" id="flowchart-S-686" class="node default"><rect height="53.99811935424805" width="240.3792266845703" y="-26.999059677124023" x="-120.18961334228516" style="" class="basic label-container"></rect><g transform="translate(-90.18961334228516, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="180.3792266845703"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Ollama LLM Client Config</p></span></div></foreignObject></g></g><g transform="translate(889.0418434143066, 1531.2436389923096)" id="flowchart-T-688" class="node default"><rect height="53.99811935424805" width="211.09591674804688" y="-26.999059677124023" x="-105.54795837402344" style="" class="basic label-container"></rect><g transform="translate(-75.54795837402344, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="151.09591674804688"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Cross-Encoder Config</p></span></div></foreignObject></g></g><g transform="translate(670.9101486206055, 1635.2417583465576)" id="flowchart-U-690" class="node default"><rect height="53.99811935424805" width="204.14242553710938" y="-26.999059677124023" x="-102.07121276855469" style="" class="basic label-container"></rect><g transform="translate(-72.07121276855469, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="144.14242553710938"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Query Enhancement</p></span></div></foreignObject></g></g><g transform="translate(670.9101486206055, 1739.2398777008057)" id="flowchart-V-696" class="node default"><rect height="53.99811935424805" width="202.10340881347656" y="-26.999059677124023" x="-101.05170440673828" style="" class="basic label-container"></rect><g transform="translate(-71.05170440673828, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="142.10340881347656"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Center Node Search</p></span></div></foreignObject></g></g><g transform="translate(670.9101486206055, 1843.2379970550537)" id="flowchart-W-698" class="node default"><rect height="53.99811935424805" width="196.25306701660156" y="-26.999059677124023" x="-98.12653350830078" style="" class="basic label-container"></rect><g transform="translate(-68.12653350830078, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="136.25306701660156"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Neo4j Entity Query</p></span></div></foreignObject></g></g><g transform="translate(670.9101486206055, 1947.2361164093018)" id="flowchart-X-700" class="node default"><rect height="53.99811935424805" width="234.5153350830078" y="-26.999059677124023" x="-117.2576675415039" style="" class="basic label-container"></rect><g transform="translate(-87.2576675415039, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="174.5153350830078"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Hybrid Search Execution</p></span></div></foreignObject></g></g><g transform="translate(381.62561798095703, 2051.23423576355)" id="flowchart-Y-702" class="node default"><rect height="53.99811935424805" width="179.2174835205078" y="-26.999059677124023" x="-89.6087417602539" style="" class="basic label-container"></rect><g transform="translate(-59.608741760253906, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="119.21748352050781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Vector Similarity</p></span></div></foreignObject></g></g><g transform="translate(621.7389984130859, 2051.23423576355)" id="flowchart-Z-704" class="node default"><rect height="53.99811935424805" width="201.00929260253906" y="-26.999059677124023" x="-100.50464630126953" style="" class="basic label-container"></rect><g transform="translate(-70.50464630126953, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="141.00929260253906"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>BM25 Text Matching</p></span></div></foreignObject></g></g><g transform="translate(890.1681747436523, 2051.23423576355)" id="flowchart-AA-706" class="node default"><rect height="53.99811935424805" width="235.84906005859375" y="-26.999059677124023" x="-117.92453002929688" style="" class="basic label-container"></rect><g transform="translate(-87.92453002929688, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="175.84906005859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Cross-Encoder Reranking</p></span></div></foreignObject></g></g><g transform="translate(564.1331214904785, 2155.232355117798)" id="flowchart-BB-708" class="node default"><rect height="53.99811935424805" width="162.67017364501953" y="-26.999059677124023" x="-81.33508682250977" style="" class="basic label-container"></rect><g transform="translate(-51.335086822509766, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="102.67017364501953"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Search Results</p></span></div></foreignObject></g></g><g transform="translate(564.1331214904785, 2259.230474472046)" id="flowchart-CC-714" class="node default"><rect height="53.99811935424805" width="178.46697998046875" y="-26.999059677124023" x="-89.23348999023438" style="" class="basic label-container"></rect><g transform="translate(-59.233489990234375, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="118.46697998046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Context Building</p></span></div></foreignObject></g></g><g transform="translate(564.1331214904785, 2363.228593826294)" id="flowchart-DD-716" class="node default"><rect height="53.99811935424805" width="182.45008850097656" y="-26.999059677124023" x="-91.22504425048828" style="" class="basic label-container"></rect><g transform="translate(-61.22504425048828, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="122.45008850097656"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Prompt Template</p></span></div></foreignObject></g></g><g transform="translate(564.1331214904785, 2467.226713180542)" id="flowchart-EE-718" class="node default"><rect height="53.99811935424805" width="169.28910064697266" y="-26.999059677124023" x="-84.64455032348633" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-54.64455032348633, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="109.28910064697266"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Ollama API Call</p></span></div></foreignObject></g></g><g transform="translate(564.1331214904785, 2610.162998199463)" id="flowchart-FF-720" class="node default"><polygon transform="translate(-65.93722724914551,65.93722724914551)" class="label-container" points="65.93722724914551,0 131.87445449829102,-65.93722724914551 65.93722724914551,-131.87445449829102 0,-65.93722724914551"></polygon><g transform="translate(-38.938167572021484, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="77.87633514404297"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Streaming?</p></span></div></foreignObject></g></g><g transform="translate(501.6823081970215, 2777.097402572632)" id="flowchart-GG-722" class="node default"><rect height="53.99811935424805" width="235.9530487060547" y="-26.999059677124023" x="-117.97652435302734" style="" class="basic label-container"></rect><g transform="translate(-87.97652435302734, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="175.9530487060547"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Stream Response Chunks</p></span></div></foreignObject></g></g><g transform="translate(809.5808982849121, 2777.097402572632)" id="flowchart-HH-724" class="node default"><rect height="53.99811935424805" width="198.9883575439453" y="-26.999059677124023" x="-99.49417877197266" style="" class="basic label-container"></rect><g transform="translate(-69.49417877197266, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="138.9883575439453"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Complete Response</p></span></div></foreignObject></g></g><g transform="translate(501.6823081970215, 2881.09552192688)" id="flowchart-II-726" class="node default"><rect height="53.99811935424805" width="223.71888732910156" y="-26.999059677124023" x="-111.85944366455078" style="" class="basic label-container"></rect><g transform="translate(-81.85944366455078, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="163.71888732910156"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>JSON Chunk Processing</p></span></div></foreignObject></g></g><g transform="translate(809.5808982849121, 2881.09552192688)" id="flowchart-JJ-728" class="node default"><rect height="53.99811935424805" width="203.3828887939453" y="-26.999059677124023" x="-101.69144439697266" style="" class="basic label-container"></rect><g transform="translate(-71.69144439697266, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="143.3828887939453"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>v2.0 JSON Structure</p></span></div></foreignObject></g></g><g transform="translate(501.6823081970215, 2985.093641281128)" id="flowchart-KK-730" class="node default"><rect height="53.99811935424805" width="194.30899047851562" y="-26.999059677124023" x="-97.15449523925781" style="" class="basic label-container"></rect><g transform="translate(-67.15449523925781, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="134.30899047851562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Server-Sent Events</p></span></div></foreignObject></g></g><g transform="translate(809.5808982849121, 2985.093641281128)" id="flowchart-LL-732" class="node default"><rect height="53.99811935424805" width="167.80165100097656" y="-26.999059677124023" x="-83.90082550048828" style="" class="basic label-container"></rect><g transform="translate(-53.90082550048828, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="107.80165100097656"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>HTTP Response</p></span></div></foreignObject></g></g><g transform="translate(501.6823081970215, 3089.091760635376)" id="flowchart-MM-734" class="node default"><rect height="53.99811935424805" width="259.9963836669922" y="-26.999059677124023" x="-129.9981918334961" style="" class="basic label-container"></rect><g transform="translate(-99.9981918334961, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="199.9963836669922"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Frontend Streaming Handler</p></span></div></foreignObject></g></g><g transform="translate(809.5808982849121, 3089.091760635376)" id="flowchart-NN-736" class="node default"><rect height="53.99811935424805" width="255.80078125" y="-26.999059677124023" x="-127.900390625" style="" class="basic label-container"></rect><g transform="translate(-97.900390625, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="195.80078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Frontend Response Handler</p></span></div></foreignObject></g></g><g transform="translate(564.1331214904785, 3193.089879989624)" id="flowchart-OO-738" class="node default"><rect height="53.99811935424805" width="227.39910888671875" y="-26.999059677124023" x="-113.69955444335938" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-83.69955444335938, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="167.39910888671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>StructuredResponse.jsx</p></span></div></foreignObject></g></g><g transform="translate(564.1331214904785, 3297.087999343872)" id="flowchart-PP-742" class="node default"><rect height="53.99811935424805" width="201.50209045410156" y="-26.999059677124023" x="-100.75104522705078" style="" class="basic label-container"></rect><g transform="translate(-70.75104522705078, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="141.50209045410156"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Response Validation</p></span></div></foreignObject></g></g><g transform="translate(564.1331214904785, 3439.047721862793)" id="flowchart-QQ-744" class="node default"><polygon transform="translate(-64.96066474914551,64.96066474914551)" class="label-container" points="64.96066474914551,0 129.92132949829102,-64.96066474914551 64.96066474914551,-129.92132949829102 0,-64.96066474914551"></polygon><g transform="translate(-37.961605072021484, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="75.92321014404297"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Valid v2.0?</p></span></div></foreignObject></g></g><g transform="translate(457.0283203125, 3605.005563735962)" id="flowchart-RR-746" class="node default"><rect height="53.99811935424805" width="202.76800537109375" y="-26.999059677124023" x="-101.38400268554688" style="" class="basic label-container"></rect><g transform="translate(-71.38400268554688, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="142.76800537109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Render Components</p></span></div></foreignObject></g></g><g transform="translate(1023.2564315795898, 3605.005563735962)" id="flowchart-SS-748" class="node default"><rect height="53.99811935424805" width="167.177734375" y="-26.999059677124023" x="-83.5888671875" style="" class="basic label-container"></rect><g transform="translate(-53.5888671875, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="107.177734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Error Boundary</p></span></div></foreignObject></g></g><g transform="translate(119.69894409179688, 3709.00368309021)" id="flowchart-TT-750" class="node default"><rect height="53.99811935424805" width="223.39788818359375" y="-26.999059677124023" x="-111.69894409179688" style="" class="basic label-container"></rect><g transform="translate(-81.69894409179688, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="163.39788818359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>AnswerWithReferences</p></span></div></foreignObject></g></g><g transform="translate(354.1880569458008, 3709.00368309021)" id="flowchart-UU-752" class="node default"><rect height="53.99811935424805" width="145.58032989501953" y="-26.999059677124023" x="-72.79016494750977" style="" class="basic label-container"></rect><g transform="translate(-42.790164947509766, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="85.58032989501953"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>EntityBadge</p></span></div></foreignObject></g></g><g transform="translate(559.8685836791992, 3709.00368309021)" id="flowchart-VV-754" class="node default"><rect height="53.99811935424805" width="165.78070831298828" y="-26.999059677124023" x="-82.89035415649414" style="" class="basic label-container"></rect><g transform="translate(-52.89035415649414, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="105.78070831298828"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FollowUpCards</p></span></div></foreignObject></g></g><g transform="translate(779.4131240844727, 3709.00368309021)" id="flowchart-WW-756" class="node default"><rect height="53.99811935424805" width="173.3083724975586" y="-26.999059677124023" x="-86.6541862487793" style="" class="basic label-container"></rect><g transform="translate(-56.6541862487793, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="113.3083724975586"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ThinkingSection</p></span></div></foreignObject></g></g><g transform="translate(571.4776878356934, 3813.001802444458)" id="flowchart-XX-758" class="node default"><rect height="53.99811935424805" width="198.36444091796875" y="-26.999059677124023" x="-99.18222045898438" style="fill:#c8e6c9 !important" class="basic label-container"></rect><g transform="translate(-69.18222045898438, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="138.36444091796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User Sees Response</p></span></div></foreignObject></g></g><g transform="translate(1023.2564315795898, 3709.00368309021)" id="flowchart-YY-766" class="node default"><rect height="53.99811935424805" width="214.3782501220703" y="-26.999059677124023" x="-107.18912506103516" style="" class="basic label-container"></rect><g transform="translate(-77.18912506103516, -11.999059677124023)" style="" class="label"><rect></rect><foreignObject height="23.998119354248047" width="154.3782501220703"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Fallback to Plain Text</p></span></div></foreignObject></g></g></g></g></g></svg>