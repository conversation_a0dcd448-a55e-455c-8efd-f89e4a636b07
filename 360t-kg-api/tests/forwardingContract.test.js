/*
 Contract test (API): Post shared fixtures to the real chat route handler and
 ensure the forwarded payload to FastAPI (mocked via global.fetch) matches
 exactly the inbound SearchConfig fixture.

 Constraints:
 - No production code changes
 - All mocking is local to this test
 - Use native http to hit an ephemeral Express server to avoid extra deps
*/

const http = require('http');
const assert = require('assert');
const path = require('path');
const fs = require('fs');
const express = require('express');

// Utility: start a tiny express app hosting the chat router
async function startServer(router) {
  const app = express();
  app.use(express.json());
  app.use('/api/chat', router);
  return await new Promise((resolve) => {
    const server = app.listen(0, () => {
      const { port } = server.address();
      resolve({ server, port });
    });
  });
}

// Utility: simple POST via http module (so we can stub global.fetch separately)
async function postJson(port, pathName, body) {
  const payload = JSON.stringify(body);
  const options = {
    hostname: '127.0.0.1',
    port,
    path: pathName,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(payload)
    }
  };

  return await new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      res.setEncoding('utf8');
      res.on('data', chunk => data += chunk);
      res.on('end', () => resolve({ status: res.statusCode, body: data }));
    });
    req.on('error', reject);
    req.write(payload);
    req.end();
  });
}

// Monkeypatch helpers (restore originals in afterEach)
const saved = {
  fetch: global.fetch,
  authCache: null,
  pgCache: null
};

function loadFixture(name) {
  const p = path.resolve(__dirname, '../../config/search-config/fixtures', `${name}.json`);
  return JSON.parse(fs.readFileSync(p, 'utf8'));
}

describe('API contract: forwards SearchConfig to FastAPI unchanged', () => {
  let capturedRequestBodies = [];

  beforeEach(() => {
    capturedRequestBodies = [];

    // 1) Stub auth middleware to allow requests and attach a test user
    const authPath = path.resolve(__dirname, '../middleware/auth.js');
    saved.authCache = require.cache[authPath];
    require.cache[authPath] = {
      id: authPath,
      filename: authPath,
      loaded: true,
      exports: {
        authenticateToken: (req, res, next) => {
          // Valid looking UUIDv4, satisfies ::uuid casts when stubbed
          req.user = { id: 'aaaaaaaa-aaaa-4aaa-8aaa-aaaaaaaaaaaa' };
          next();
        },
        optionalAuth: (req, _res, next) => { req.user = { id: 'aaaaaaaa-aaaa-4aaa-8aaa-aaaaaaaaaaaa' }; next(); }
      }
    };

    // 2) Replace 'pg' module entirely with a shim to avoid timers/open handles from Pool
    const pgPath = require.resolve('pg');
    saved.pgCache = require.cache[pgPath];
    class FakePool {
      constructor() {}
      async query(sql, params) {
        const text = String(sql);
        if (text.includes('SELECT id FROM chat_sessions') && text.includes('user_id')) {
          return { rows: [] }; // force create new conversation path
        }
        if (text.includes('INSERT INTO chat_sessions')) {
          return { rows: [{ id: '11111111-1111-4111-8111-111111111111' }] };
        }
        if (text.includes('INSERT INTO messages')) {
          return { rows: [{ id: '*************-4222-8222-************' }] };
        }
        return { rows: [] };
      }
      async end() {}
    }
    require.cache[pgPath] = {
      id: pgPath,
      filename: pgPath,
      loaded: true,
      exports: { Pool: FakePool }
    };

    // 3) Stub global.fetch to capture outbound call to FastAPI
    global.fetch = async (url, options = {}) => {
      const body = options && options.body ? JSON.parse(options.body) : null;
      capturedRequestBodies.push({ url, body });
      // Return a minimal, valid structured response so route continues
      return {
        ok: true,
        status: 200,
        async json() {
          return {
            structured_response: {
              version: '3.0',
              format: 'C',
              core: {
                summary: 'ok',
                results: [],
                source_count: 0,
                confidence_score: 0.99,
                processing_time_ms: 1
              },
              extensions: {}
            }
          };
        },
        async text() { return JSON.stringify({ ok: true }); }
      };
    };
  });

  afterEach(() => {
    // Restore fetch
    global.fetch = saved.fetch;

    // Restore auth module cache
    const authPath = path.resolve(__dirname, '../middleware/auth.js');
    if (saved.authCache) {
      require.cache[authPath] = saved.authCache;
      saved.authCache = null;
    } else {
      delete require.cache[authPath];
    }

    // Restore 'pg' module
    const pgPath = require.resolve('pg');
    if (saved.pgCache) {
      require.cache[pgPath] = saved.pgCache;
      saved.pgCache = null;
    } else {
      delete require.cache[pgPath];
    }
  });

  async function runCase(fixtureName) {
    // Load router after stubs so it picks up our overrides
    const routerFactory = require('../routes/chatRoutes');
    const { server, port } = await startServer(routerFactory(null));

    const fixture = loadFixture(fixtureName);

    try {
      const res = await postJson(port, '/api/chat/message', {
        message: 'Test question',
        history: [{ role: 'user', content: 'prev' }],
        searchConfig: fixture
      });

      assert.strictEqual(res.status, 200, `Expected 200, got ${res.status}. Body: ${res.body}`);
      // Ensure one outbound call was made and body captured
      assert.ok(capturedRequestBodies.length >= 1, 'No FastAPI call captured');
      const last = capturedRequestBodies[capturedRequestBodies.length - 1];
      assert.ok(last.url.includes('/chat'), 'Unexpected FastAPI URL');

      // The forwarded config should match the expected format
      // With unified architecture, both modes send search_config
      assert.deepStrictEqual(last.body.search_config, fixture, 'search_config forwarded differs from fixture');

      // Ensure NO graphitiSettings is sent (unified architecture)
      assert.strictEqual(last.body.graphitiSettings, undefined, 'graphitiSettings should not be sent in unified architecture');
    } finally {
      server.close();
    }
  }

  it('forwards graphiti fixture unchanged', async () => {
    await runCase('graphiti');
  });

  it('forwards atlasrag fixture unchanged', async () => {
    await runCase('atlasrag');
  });
});

