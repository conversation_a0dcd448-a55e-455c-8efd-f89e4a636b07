const { validator, validate, validateAndRepair, getSchemaInfo } = require('../utils/schema-validator');

describe('Schema Validator', () => {
  describe('Valid Response Validation', () => {
    test('should validate a complete valid v2.0 response', () => {
      const validResponse = {
        version: '2.0',
        answer: 'This is a test answer about the knowledge graph.',
        sections: [
          {
            title: 'Test Section',
            content: 'This is test content for the section.',
            entity_refs: ['entity-1', 'entity-2']
          }
        ],
        sources: [
          {
            id: 'source-1',
            title: 'Test Document',
            url: 'https://example.com/test.pdf',
            document_type: 'PDF',
            snippet: 'This is a test snippet from the document.',
            category: 'EMS'
          }
        ],
        entities: [
          {
            id: 'entity-1',
            name: 'Test Entity',
            description: 'This is a test entity description.',
            category: 'EMS',
            relevance_score: 0.95,
            properties: {
              type: 'concept',
              domain: 'trading'
            }
          }
        ],
        badges: [
          {
            label: 'EMS',
            tooltip: 'Energy Management System',
            color: '#40C4FF',
            count: 3
          }
        ],
        follow_up: [
          {
            question: 'How does the EMS system work?',
            context_hint: 'Technical details'
          }
        ],
        metadata: {
          processing_time_ms: 1500,
          token_usage: 250,
          confidence_score: 0.92,
          fallback_used: false,
          search_type: 'COMBINED_HYBRID_SEARCH_CROSS_ENCODER',
          entity_count: 5,
          source_count: 3
        }
      };

      const result = validate(validResponse);
      
      expect(result.success).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.metadata.version).toBe('2.0');
    });

    test('should validate minimal valid response', () => {
      const minimalResponse = {
        version: '2.0',
        answer: 'Minimal answer',
        sections: [],
        sources: [],
        entities: [],
        badges: [],
        follow_up: [],
        metadata: {}
      };

      const result = validate(minimalResponse);
      
      expect(result.success).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('Invalid Response Validation', () => {
    test('should reject response missing required fields', () => {
      const invalidResponse = {
        version: '2.0',
        answer: 'Test answer'
        // Missing required fields: sections, sources, entities, badges, follow_up, metadata
      };

      const result = validate(invalidResponse);
      
      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      
      const missingFields = result.errors.filter(e => e.keyword === 'required');
      expect(missingFields.length).toBeGreaterThan(0);
    });

    test('should reject response with invalid types', () => {
      const invalidResponse = {
        version: '2.0',
        answer: 123, // Should be string
        sections: 'not an array', // Should be array
        sources: [],
        entities: [],
        badges: [],
        follow_up: [],
        metadata: {}
      };

      const result = validate(invalidResponse);
      
      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      
      const typeErrors = result.errors.filter(e => e.keyword === 'type');
      expect(typeErrors.length).toBeGreaterThan(0);
    });

    test('should reject response with invalid URL format', () => {
      const invalidResponse = {
        version: '2.0',
        answer: 'Test answer',
        sections: [],
        sources: [
          {
            id: 'source-1',
            title: 'Test Document',
            url: 'not-a-valid-url', // Invalid URL format
            document_type: 'PDF'
          }
        ],
        entities: [],
        badges: [],
        follow_up: [],
        metadata: {}
      };

      const result = validate(invalidResponse);
      
      expect(result.success).toBe(false);
      expect(result.errors.some(e => e.keyword === 'format')).toBe(true);
    });

    test('should reject response with oversized arrays', () => {
      const invalidResponse = {
        version: '2.0',
        answer: 'Test answer',
        sections: new Array(10).fill({ // Max is 5
          title: 'Test',
          content: 'Test content',
          entity_refs: []
        }),
        sources: [],
        entities: [],
        badges: [],
        follow_up: [],
        metadata: {}
      };

      const result = validate(invalidResponse);
      
      expect(result.success).toBe(false);
      expect(result.errors.some(e => e.keyword === 'maxItems')).toBe(true);
    });
  });

  describe('Response Repair', () => {
    test('should repair response with missing required fields', () => {
      const incompleteResponse = {
        answer: 'Test answer'
        // Missing most required fields
      };

      const result = validateAndRepair(incompleteResponse);
      
      expect(result.wasRepaired).toBe(true);
      expect(result.repairs.length).toBeGreaterThan(0);
      expect(result.response.version).toBe('2.0');
      expect(Array.isArray(result.response.sections)).toBe(true);
      expect(Array.isArray(result.response.sources)).toBe(true);
      expect(result.validation.success).toBe(true);
    });

    test('should repair oversized text fields', () => {
      const oversizedResponse = {
        version: '2.0',
        answer: 'A'.repeat(6000), // Exceeds 5000 char limit
        sections: [],
        sources: [],
        entities: [],
        badges: [],
        follow_up: [],
        metadata: {}
      };

      const result = validateAndRepair(oversizedResponse);
      
      expect(result.wasRepaired).toBe(true);
      expect(result.response.answer.length).toBeLessThanOrEqual(5000);
      expect(result.response.answer.endsWith('...')).toBe(true);
      expect(result.validation.success).toBe(true);
    });

    test('should repair oversized arrays', () => {
      const oversizedResponse = {
        version: '2.0',
        answer: 'Test answer',
        sections: new Array(10).fill({ // Max is 5
          title: 'Test',
          content: 'Test content',
          entity_refs: []
        }),
        sources: [],
        entities: [],
        badges: [],
        follow_up: [],
        metadata: {}
      };

      const result = validateAndRepair(oversizedResponse);
      
      expect(result.wasRepaired).toBe(true);
      expect(result.response.sections.length).toBe(5);
      expect(result.validation.success).toBe(true);
    });
  });

  describe('Schema Information', () => {
    test('should return schema information for v2.0', () => {
      const schemaInfo = getSchemaInfo('2.0');
      
      expect(schemaInfo).toBeDefined();
      expect(schemaInfo.version).toBe('2.0');
      expect(schemaInfo.title).toBe('Graphiti Chat Response v2.0');
      expect(Array.isArray(schemaInfo.requiredFields)).toBe(true);
      expect(Array.isArray(schemaInfo.properties)).toBe(true);
    });

    test('should return null for unknown schema version', () => {
      const schemaInfo = getSchemaInfo('999.0');
      
      expect(schemaInfo).toBeNull();
    });
  });

  describe('Error Formatting', () => {
    test('should format validation errors with user-friendly messages', () => {
      const invalidResponse = {
        version: '2.0',
        answer: '', // Too short
        sections: [],
        sources: [],
        entities: [],
        badges: [],
        follow_up: [],
        metadata: {}
      };

      const result = validate(invalidResponse);
      
      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      
      const error = result.errors[0];
      expect(error).toHaveProperty('path');
      expect(error).toHaveProperty('message');
      expect(error).toHaveProperty('severity');
    });
  });
});
