const { spawn } = require('child_process');
const path = require('path');

describe('Response Builder', () => {
  const runPythonScript = (scriptContent) => {
    return new Promise((resolve, reject) => {
      const python = spawn('python3', ['-c', scriptContent]);
      let stdout = '';
      let stderr = '';

      python.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      python.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      python.on('close', (code) => {
        if (code !== 0) {
          reject(new Error(`Python script failed with code ${code}: ${stderr}`));
        } else {
          try {
            const result = JSON.parse(stdout.trim());
            resolve(result);
          } catch (e) {
            reject(new Error(`Failed to parse JSON output: ${stdout}`));
          }
        }
      });
    });
  };

  test('should build structured response with mock data', async () => {
    const scriptContent = `
import sys
import os
sys.path.append('${path.join(__dirname, '..')}')

from services.response_builder import ResponseBuilder
import json

# Mock search results
class MockNode:
    def __init__(self, uuid, name, summary, score, labels=None, group_id=None):
        self.uuid = uuid
        self.name = name
        self.summary = summary
        self.score = score
        self.labels = labels or []
        self.group_id = group_id

class MockEdge:
    def __init__(self, fact, score, group_id=None):
        self.fact = fact
        self.score = score
        self.group_id = group_id

class MockSearchResults:
    def __init__(self, nodes, edges):
        self.nodes = nodes
        self.edges = edges

# Create mock data
nodes = [
    MockNode("node-1", "Limit Monitor", "System for monitoring trading limits", 0.95, ["Entity"], "user_guides"),
    MockNode("node-2", "Risk Engine", "Core risk calculation engine", 0.88, ["Entity"], "ems_docs")
]

edges = [
    MockEdge("The Limit Monitor provides real-time risk monitoring", 0.92, "user_guides_https://example.com/limits.pdf"),
    MockEdge("Risk Engine calculates exposure limits", 0.85, "ems_docs_https://example.com/risk.pdf")
]

search_results = MockSearchResults(nodes, edges)

# Build response
builder = ResponseBuilder()
response = builder.build_structured_response(
    answer="The Limit Monitor offers comprehensive risk management capabilities.",
    search_results=search_results,
    citations=["[1]", "[2]"],
    question="What is the Limit Monitor?",
    search_metadata={"search_type": "COMBINED_HYBRID_SEARCH_CROSS_ENCODER"}
)

print(json.dumps(response, indent=2))
`;

    const result = await runPythonScript(scriptContent);

    // Validate response structure
    expect(result).toHaveProperty('version', '2.0');
    expect(result).toHaveProperty('answer');
    expect(result).toHaveProperty('sections');
    expect(result).toHaveProperty('sources');
    expect(result).toHaveProperty('entities');
    expect(result).toHaveProperty('badges');
    expect(result).toHaveProperty('follow_up');
    expect(result).toHaveProperty('metadata');

    // Validate entities
    expect(Array.isArray(result.entities)).toBe(true);
    expect(result.entities.length).toBeGreaterThan(0);
    expect(result.entities[0]).toHaveProperty('id');
    expect(result.entities[0]).toHaveProperty('name');
    expect(result.entities[0]).toHaveProperty('relevance_score');

    // Validate sources
    expect(Array.isArray(result.sources)).toBe(true);
    if (result.sources.length > 0) {
      expect(result.sources[0]).toHaveProperty('id');
      expect(result.sources[0]).toHaveProperty('title');
      expect(result.sources[0]).toHaveProperty('url');
      expect(result.sources[0]).toHaveProperty('document_type');
    }

    // Validate metadata
    expect(result.metadata).toHaveProperty('processing_time_ms');
    expect(result.metadata).toHaveProperty('entity_count');
    expect(result.metadata).toHaveProperty('source_count');
    expect(result.metadata.fallback_used).toBe(false);
  });

  test('should handle empty search results gracefully', async () => {
    const scriptContent = `
import sys
import os
sys.path.append('${path.join(__dirname, '..')}')

from services.response_builder import ResponseBuilder
import json

class MockSearchResults:
    def __init__(self):
        self.nodes = []
        self.edges = []

search_results = MockSearchResults()

builder = ResponseBuilder()
response = builder.build_structured_response(
    answer="No specific information found.",
    search_results=search_results,
    citations=[],
    question="What is unknown?",
    search_metadata={}
)

print(json.dumps(response, indent=2))
`;

    const result = await runPythonScript(scriptContent);

    expect(result.version).toBe('2.0');
    expect(result.entities).toHaveLength(0);
    expect(result.sources).toHaveLength(0);
    expect(result.badges).toHaveLength(0);
    expect(result.metadata.entity_count).toBe(0);
    expect(result.metadata.source_count).toBe(0);
  });

  test('should extract URLs from group_id correctly', async () => {
    const scriptContent = `
import sys
import os
sys.path.append('${path.join(__dirname, '..')}')

from services.response_builder import ResponseBuilder
import json

class MockEdge:
    def __init__(self, fact, group_id):
        self.fact = fact
        self.group_id = group_id
        self.score = 0.9

class MockSearchResults:
    def __init__(self, edges):
        self.nodes = []
        self.edges = edges

# Test URL extraction
edges = [
    MockEdge("Test fact 1", "user_guides_https://example.com/document1.pdf"),
    MockEdge("Test fact 2", "ems_docs_https://example.com/document2.pdf"),
    MockEdge("Test fact 3", "no_url_here")
]

search_results = MockSearchResults(edges)

builder = ResponseBuilder()
response = builder.build_structured_response(
    answer="Test answer",
    search_results=search_results,
    citations=[],
    question="Test question"
)

print(json.dumps(response, indent=2))
`;

    const result = await runPythonScript(scriptContent);

    expect(result.sources.length).toBeGreaterThan(0);
    
    // Check that URLs were extracted correctly
    const urls = result.sources.map(s => s.url);
    expect(urls).toContain('https://example.com/document1.pdf');
    expect(urls).toContain('https://example.com/document2.pdf');
    
    // Check document types
    result.sources.forEach(source => {
      expect(source.document_type).toBe('PDF');
    });
  });

  test('should generate appropriate badges from categories', async () => {
    const scriptContent = `
import sys
import os
sys.path.append('${path.join(__dirname, '..')}')

from services.response_builder import ResponseBuilder
import json

class MockNode:
    def __init__(self, uuid, name, summary, score, group_id):
        self.uuid = uuid
        self.name = name
        self.summary = summary
        self.score = score
        self.group_id = group_id

class MockEdge:
    def __init__(self, fact, group_id):
        self.fact = fact
        self.group_id = group_id
        self.score = 0.9

class MockSearchResults:
    def __init__(self, nodes, edges):
        self.nodes = nodes
        self.edges = edges

# Create nodes and edges with different categories
nodes = [
    MockNode("1", "EMS System", "Energy management", 0.9, "ems_system"),
    MockNode("2", "Trading Platform", "Trading system", 0.8, "trading_platform")
]

edges = [
    MockEdge("EMS fact", "ems_docs"),
    MockEdge("Trading fact", "trading_docs")
]

search_results = MockSearchResults(nodes, edges)

builder = ResponseBuilder()
response = builder.build_structured_response(
    answer="Test answer",
    search_results=search_results,
    citations=[],
    question="Test question"
)

print(json.dumps(response, indent=2))
`;

    const result = await runPythonScript(scriptContent);

    expect(Array.isArray(result.badges)).toBe(true);
    
    // Should have badges for detected categories
    if (result.badges.length > 0) {
      result.badges.forEach(badge => {
        expect(badge).toHaveProperty('label');
        expect(badge).toHaveProperty('tooltip');
        expect(badge).toHaveProperty('count');
        expect(typeof badge.count).toBe('number');
        expect(badge.count).toBeGreaterThan(0);
      });
    }
  });

  test('should handle fallback response on error', async () => {
    const scriptContent = `
import sys
import os
sys.path.append('${path.join(__dirname, '..')}')

from services.response_builder import ResponseBuilder
import json

# Create invalid search results that will cause an error
class InvalidSearchResults:
    def __init__(self):
        # Missing nodes and edges properties
        pass

search_results = InvalidSearchResults()

builder = ResponseBuilder()
response = builder.build_structured_response(
    answer="Test answer",
    search_results=search_results,
    citations=[],
    question="Test question"
)

print(json.dumps(response, indent=2))
`;

    const result = await runPythonScript(scriptContent);

    expect(result.version).toBe('2.0');
    expect(result.metadata.fallback_used).toBe(true);
    expect(result.metadata).toHaveProperty('error');
    expect(result.entities).toHaveLength(0);
    expect(result.sources).toHaveLength(0);
  });
});
