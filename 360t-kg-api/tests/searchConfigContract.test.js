const { expect } = require('chai');
const path = require('path');
const fs = require('fs');

const {
  validateSearchConfig,
  ensureMode
} = require('../utils/searchConfigValidator');

const loadFixture = (name) => {
  const filePath = path.resolve(__dirname, '../../config/search-config/fixtures', `${name}.json`);
  return JSON.parse(fs.readFileSync(filePath, 'utf8'));
};

describe('Search Config Contract', () => {
  const graphitiFixture = loadFixture('graphiti');
  const atlasFixture = loadFixture('atlasrag');

  it('validates graphiti union fixture', () => {
    const result = validateSearchConfig(graphitiFixture);
    expect(result.valid).to.equal(true, result.errors.join('; '));
    expect(result.config.mode).to.equal('graphiti');
    expect(result.config.graphiti.edgeCount).to.equal(graphitiFixture.graphiti.edgeCount);
  });

  it('validates atlasrag union fixture', () => {
    const result = validateSearchConfig(atlasFixture);
    expect(result.valid).to.equal(true, result.errors.join('; '));
    expect(result.config.mode).to.equal('atlasrag');
    expect(result.config.atlasrag.topN).to.equal(atlasFixture.atlasrag.topN);
  });

  });
