/**
 * Test script to verify Format C and v2.0 response validation
 * Tests both formats to ensure backward compatibility and new format support
 */

// Import the validator functions
import { parseAndValidateResponse, isValidV2Response } from '../../360t-kg-ui/src/utils/responseValidator.js';

// Mock Format C response
const formatCResponse = {
  version: "3.0",
  format: "C",
  timestamp: "2024-08-23T10:30:00Z",
  query: {
    original: "What is FOREX?",
    processed: "What is FOREX?"
  },
  core: {
    summary: "FOREX (Foreign Exchange) is the global marketplace for trading national currencies against one another.",
    results: [
      {
        content: "FOREX is the largest financial market in the world",
        relevance_score: 0.95,
        source: {
          type: "document",
          title: "FOREX Trading Guide",
          url: "https://example.com/forex-guide"
        }
      },
      {
        content: "Daily trading volume exceeds $6 trillion",
        relevance_score: 0.88,
        source: {
          type: "article",
          title: "FOREX Market Statistics",
          url: "https://example.com/forex-stats"
        }
      }
    ],
    source_count: 2,
    confidence_score: 0.92,
    processing_time_ms: 150,
    follow_up_questions: [
      "What are the major currency pairs?",
      "How does FOREX trading work?"
    ]
  },
  extensions: {
    graphiti: {
      version: "1.0",
      rrf_score: 0.85,
      nodes: [
        { id: "forex-1", name: "FOREX", type: "concept" },
        { id: "currency-1", name: "Currency", type: "concept" }
      ],
      relationships: [
        { from: "forex-1", to: "currency-1", type: "involves" }
      ]
    },
    atlas_rag: {
      total_passages: 25,
      passage_metadata: [
        { id: "p1", score: 0.95, section: "introduction" },
        { id: "p2", score: 0.88, section: "market-overview" }
      ]
    }
  }
};

// Mock v2.0 response
const v2Response = {
  version: "2.0",
  answer: "FOREX (Foreign Exchange) is the global marketplace for trading currencies.",
  sections: [
    {
      title: "What is FOREX?",
      content: "FOREX is the largest financial market in the world with daily volume exceeding $6 trillion.",
      entity_refs: ["forex-1", "currency-1"]
    }
  ],
  sources: [
    {
      id: "source-1",
      title: "FOREX Trading Guide",
      url: "https://example.com/forex-guide"
    }
  ],
  entities: [
    {
      id: "forex-1",
      name: "FOREX",
      type: "concept",
      relevance_score: 0.95
    },
    {
      id: "currency-1",
      name: "Currency",
      type: "concept",
      relevance_score: 0.88
    }
  ],
  badges: [
    {
      label: "Financial",
      tooltip: "Financial market information"
    }
  ],
  follow_up: [
    "What are major currency pairs?",
    "How does FOREX trading work?"
  ]
};

// Test functions
async function runValidationTests() {
  console.log('🧪 Testing Response Validation...\n');

  // Test Format C response
  console.log('1. Testing Format C Response:');
  const formatCResult = parseAndValidateResponse(JSON.stringify(formatCResponse));
  console.log('✅ Format C Result:', {
    success: formatCResult.success,
    isValid: formatCResult.validationResult?.isValid,
    errors: formatCResult.validationResult?.errors || [],
    fieldCounts: formatCResult.validationResult?.fieldCounts
  });

  if (!formatCResult.success) {
    console.error('❌ Format C validation failed:', formatCResult.error);
  }

  console.log();

  // Test v2.0 response
  console.log('2. Testing v2.0 Response:');
  const v2Result = parseAndValidateResponse(JSON.stringify(v2Response));
  console.log('✅ v2.0 Result:', {
    success: v2Result.success,
    isValid: v2Result.validationResult?.isValid,
    errors: v2Result.validationResult?.errors || [],
    fieldCounts: v2Result.validationResult?.fieldCounts
  });

  if (!v2Result.success) {
    console.error('❌ v2.0 validation failed:', v2Result.error);
  }

  console.log();

  // Test malformed response
  console.log('3. Testing Malformed Response:');
  const malformedResult = parseAndValidateResponse('{"invalid": "response"}');
  console.log('✅ Malformed Result (should fail):', {
    success: malformedResult.success,
    error: malformedResult.error
  });

  console.log();

  // Test quick validation functions
  console.log('4. Testing Quick Validation Functions:');
  const formatCQuick = isValidV2Response(JSON.stringify(formatCResponse));
  const v2Quick = isValidV2Response(JSON.stringify(v2Response));
  
  console.log('✅ Quick validation results:');
  console.log('   Format C:', formatCQuick);
  console.log('   v2.0:', v2Quick);

  console.log();

  // Summary
  console.log('📋 Test Summary:');
  console.log(`   Format C Response: ${formatCResult.success ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   v2.0 Response: ${v2Result.success ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   Malformed Response: ${!malformedResult.success ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   Quick Validation: ${formatCQuick && v2Quick ? '✅ PASS' : '❌ FAIL'}`);

  const allTestsPassed = formatCResult.success && v2Result.success && !malformedResult.success && formatCQuick && v2Quick;
  console.log(`\n🎯 Overall Result: ${allTestsPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

  return allTestsPassed;
}

// Run tests if this is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runValidationTests().catch(console.error);
}

export default runValidationTests;