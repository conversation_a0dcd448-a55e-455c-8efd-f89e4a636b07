const express = require('express');
const app = express();
const port = 3009;

app.get('/', (req, res) => {
  res.send('Test server is working!');
});

const server = app.listen(port, '0.0.0.0', () => {
  console.log(`Test server running on port ${port}`);
});

server.on('listening', () => {
  const address = server.address();
  console.log(`Test server is actually listening on ${address.address}:${address.port}`);
});

server.on('error', (error) => {
  console.error('Test server error:', error);
});