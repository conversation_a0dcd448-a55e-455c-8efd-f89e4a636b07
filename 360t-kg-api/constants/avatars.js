/**
 * Professional Avatar System Constants for 360T Knowledge Graph Visualizer Backend
 * 
 * This module provides backend validation constants for the avatar system,
 * synchronized with the frontend avatar configuration.
 * 
 * @module avatarConstants
 * @version 1.0.0
 * <AUTHOR> Knowledge Graph Visualizer Team
 */

/**
 * Valid avatar IDs that match the frontend avatar system
 * These IDs correspond to the keys in AVATAR_ICONS from frontend
 * @type {string[]}
 */
const VALID_AVATAR_IDS = [
  'user',       // Professional - Standard professional user profile
  'userCheck',  // Verified Professional - Authenticated user  
  'users',      // Team Lead - Team leader or group manager
  'crown',      // Executive - Senior executive or leadership
  'star',       // Expert - Subject matter expert or top performer
  'heart',      // Mentor - Advisor or supportive professional
  'zap',        // Innovator - Innovation-focused professional
  'shield',     // Guardian - Security/compliance professional
  'award',      // Achiever - High achiever or award recipient
  'smile'       // Ambassador - Customer-facing professional
];

/**
 * Default avatar ID for new users
 * @type {string}
 */
const DEFAULT_AVATAR_ID = 'user';

/**
 * Avatar categories for validation
 * @type {string[]}
 */
const VALID_AVATAR_CATEGORIES = [
  'standard',
  'status', 
  'leadership',
  'expertise',
  'supportive',
  'dynamic',
  'security',
  'achievement',
  'relationship'
];

/**
 * Validate if an avatar ID is valid
 * @param {string} avatarId - Avatar identifier to validate
 * @returns {boolean} True if avatar ID is valid
 */
const isValidAvatarId = (avatarId) => {
  return avatarId && typeof avatarId === 'string' && VALID_AVATAR_IDS.includes(avatarId);
};

/**
 * Validate avatar selection in user preferences
 * @param {*} selectedAvatar - Avatar selection to validate
 * @returns {Object} Validation result with valid flag and normalized value
 */
const validateAvatarSelection = (selectedAvatar) => {
  if (!selectedAvatar) {
    return {
      valid: true,
      value: DEFAULT_AVATAR_ID,
      message: 'No avatar selected, using default'
    };
  }

  if (typeof selectedAvatar !== 'string') {
    return {
      valid: false,
      value: null,
      message: 'Avatar selection must be a string'
    };
  }

  if (!isValidAvatarId(selectedAvatar)) {
    return {
      valid: false,
      value: null,
      message: `Invalid avatar ID: ${selectedAvatar}. Valid options are: ${VALID_AVATAR_IDS.join(', ')}`
    };
  }

  return {
    valid: true,
    value: selectedAvatar,
    message: 'Valid avatar selection'
  };
};

/**
 * Get avatar information for API responses
 * @param {string} avatarId - Avatar identifier
 * @returns {Object} Avatar information
 */
const getAvatarInfo = (avatarId) => {
  const avatarMap = {
    user: { name: 'Professional', category: 'standard' },
    userCheck: { name: 'Verified Professional', category: 'status' },
    users: { name: 'Team Lead', category: 'leadership' },
    crown: { name: 'Executive', category: 'leadership' },
    star: { name: 'Expert', category: 'expertise' },
    heart: { name: 'Mentor', category: 'supportive' },
    zap: { name: 'Innovator', category: 'dynamic' },
    shield: { name: 'Guardian', category: 'security' },
    award: { name: 'Achiever', category: 'achievement' },
    smile: { name: 'Ambassador', category: 'relationship' }
  };

  return avatarMap[avatarId] || avatarMap[DEFAULT_AVATAR_ID];
};

module.exports = {
  VALID_AVATAR_IDS,
  DEFAULT_AVATAR_ID,
  VALID_AVATAR_CATEGORIES,
  isValidAvatarId,
  validateAvatarSelection,
  getAvatarInfo
};