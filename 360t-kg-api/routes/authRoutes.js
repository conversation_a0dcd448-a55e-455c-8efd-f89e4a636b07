// ============================================
// Authentication Routes
// ============================================

const express = require('express');
const { body, validationResult } = require('express-validator');
const { 
  hashPassword, 
  comparePassword, 
  generateToken, 
  validateRegistrationData 
} = require('../services/authService');
const { authenticateToken, optionalAuth } = require('../middleware/auth');
const { query, withTransaction } = require('../database/config');
const { VALID_AVATAR_IDS, isValidAvatarId } = require('../constants/avatars');
const { validateUserPreferences, validatePartialUserPreferences, createDefaultPreferences, mergePreferences } = require('../utils/preferenceValidation');

const router = express.Router();

/**
 * POST /api/auth/register
 * Register a new user account
 */
router.post('/register', [
  body('username')
    .trim()
    .isLength({ min: 3, max: 50 })
    .withMessage('Username must be between 3 and 50 characters')
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Username can only contain letters, numbers, underscores, and hyphens'),
  body('email')
    .trim()
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email address is required'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long'),
  body('display_name')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Display name cannot exceed 100 characters')
], async (req, res, next) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        code: 'VALIDATION_ERROR',
        details: errors.array()
      });
    }

    const { username, email, password, display_name } = req.body;

    // Additional validation
    const validation = validateRegistrationData(req.body);
    if (!validation.valid) {
      return res.status(400).json({
        error: 'Registration data validation failed',
        code: 'VALIDATION_ERROR',
        details: validation.errors
      });
    }

    // Use transaction to ensure data integrity
    const result = await withTransaction(async (client) => {
      // Check if username already exists
      const usernameCheck = await client.query(
        'SELECT id FROM users WHERE username = $1',
        [username]
      );

      if (usernameCheck.rows.length > 0) {
        throw new Error('Username already exists');
      }

      // Check if email already exists
      const emailCheck = await client.query(
        'SELECT id FROM users WHERE email = $1',
        [email]
      );

      if (emailCheck.rows.length > 0) {
        throw new Error('Email already registered');
      }

      // Hash the password
      const passwordHash = await hashPassword(password);

      // Create new user (let database auto-generate INTEGER id)
      const insertQuery = `
        INSERT INTO users (username, email, password_hash, display_name, preferences, is_active, is_verified, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
        RETURNING id, username, email, display_name, is_active, is_verified, created_at, updated_at
      `;

      const insertResult = await client.query(insertQuery, [
        username,
        email,
        passwordHash,
        display_name || username,
        JSON.stringify(createDefaultPreferences()), // Default preferences with avatar
        true, // is_active
        true  // is_verified (for simple implementation)
      ]);

      return insertResult.rows[0];
    });

    // Generate token for the new user
    const token = generateToken(result);

    res.status(201).json({
      message: 'User registered successfully',
      user: {
        id: result.id,
        username: result.username,
        email: result.email,
        display_name: result.display_name,
        is_active: result.is_active,
        is_verified: result.is_verified,
        created_at: result.created_at,
        updated_at: result.updated_at
      },
      token
    });

  } catch (error) {
    console.error('Registration error:', error);
    
    // Handle specific database errors
    if (error.message.includes('already exists') || error.message.includes('already registered')) {
      return res.status(409).json({
        error: error.message,
        code: 'DUPLICATE_USER'
      });
    }

    if (error.constraint) {
      return res.status(409).json({
        error: 'User registration failed due to constraint violation',
        code: 'CONSTRAINT_ERROR'
      });
    }

    next(error);
  }
});

/**
 * POST /api/auth/login
 * Authenticate user and return JWT token
 */
router.post('/login', [
  body('login')
    .trim()
    .notEmpty()
    .withMessage('Username or email is required'),
  body('password')
    .notEmpty()
    .withMessage('Password is required')
], async (req, res, next) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        code: 'VALIDATION_ERROR',
        details: errors.array()
      });
    }

    const { login, password } = req.body;

    // Find user by username or email
    const userQuery = `
      SELECT id, username, email, password_hash, display_name, preferences, is_active, is_verified, created_at, updated_at
      FROM users 
      WHERE (username = $1 OR email = $1) AND is_active = true
    `;

    const result = await query(userQuery, [login]);

    if (result.rows.length === 0) {
      return res.status(401).json({
        error: 'Invalid credentials',
        code: 'INVALID_CREDENTIALS'
      });
    }

    const user = result.rows[0];

    // Compare password
    const isValidPassword = await comparePassword(password, user.password_hash);

    if (!isValidPassword) {
      return res.status(401).json({
        error: 'Invalid credentials',
        code: 'INVALID_CREDENTIALS'
      });
    }

    // Update last login timestamp (optional)
    await query(
      'UPDATE users SET updated_at = NOW() WHERE id = $1',
      [user.id]
    );

    // Generate token
    const token = generateToken(user);

    // Ensure preferences are properly parsed
    let preferences = user.preferences;
    if (typeof preferences === 'string') {
      try {
        preferences = JSON.parse(preferences);
      } catch (e) {
        console.warn('Failed to parse user preferences during login:', e);
        preferences = createDefaultPreferences();
      }
    }

    // Ensure preferences have required structure
    if (!preferences || typeof preferences !== 'object') {
      preferences = createDefaultPreferences();
    }

    res.json({
      message: 'Login successful',
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        display_name: user.display_name,
        preferences: preferences,
        is_active: user.is_active,
        is_verified: user.is_verified,
        created_at: user.created_at,
        updated_at: user.updated_at
      },
      token
    });

  } catch (error) {
    console.error('Login error:', error);
    next(error);
  }
});

/**
 * POST /api/auth/logout
 * Logout endpoint (JWT is stateless, so this is mainly for client-side cleanup)
 */
router.post('/logout', optionalAuth, (req, res) => {
  res.json({
    message: 'Logout successful',
    timestamp: new Date().toISOString()
  });
});

/**
 * GET /api/auth/me
 * Get current user information with properly parsed preferences
 */
router.get('/me', authenticateToken, (req, res) => {
  // Ensure preferences are properly parsed
  let preferences = req.user.preferences;
  if (typeof preferences === 'string') {
    try {
      preferences = JSON.parse(preferences);
    } catch (e) {
      console.warn('Failed to parse user preferences:', e);
      preferences = createDefaultPreferences();
    }
  }

  // Ensure preferences have required structure
  if (!preferences || typeof preferences !== 'object') {
    preferences = createDefaultPreferences();
  }

  res.json({
    user: {
      id: req.user.id,
      username: req.user.username,
      email: req.user.email,
      display_name: req.user.display_name,
      preferences: preferences,
      is_active: req.user.is_active,
      is_verified: req.user.is_verified,
      created_at: req.user.created_at,
      updated_at: req.user.updated_at
    }
  });
});

/**
 * PUT /api/auth/me
 * Update current user profile with enhanced avatar and preferences validation
 */
router.put('/me', [
  authenticateToken,
  body('display_name')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Display name cannot exceed 100 characters'),
  body('preferences')
    .optional()
    .isObject()
    .withMessage('Preferences must be an object'),
  body('preferences.selectedAvatar')
    .optional()
    .custom((value) => {
      if (value && !isValidAvatarId(value)) {
        throw new Error(`Invalid avatar ID. Valid options: ${VALID_AVATAR_IDS.join(', ')}`);
      }
      return true;
    }),
  body('preferences.legendColors')
    .optional()
    .isObject()
    .withMessage('Legend colors must be an object'),
  body('preferences.graphSettings')
    .optional()
    .isObject()
    .withMessage('Graph settings must be an object')
], async (req, res, next) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        code: 'VALIDATION_ERROR',
        details: errors.array()
      });
    }

    const { display_name, preferences } = req.body;
    const userId = req.user.id;
    
    console.log('🔄 PUT /api/auth/me - Profile update request:');
    console.log('  - User ID:', userId);
    console.log('  - Display name:', display_name);
    console.log('  - Preferences:', JSON.stringify(preferences, null, 2));
    if (preferences?.selectedAvatar) {
      console.log('  - Avatar update requested:', preferences.selectedAvatar);
    }

    // Get current user data to merge preferences
    const currentUserQuery = `
      SELECT preferences FROM users WHERE id = $1
    `;
    const currentUserResult = await query(currentUserQuery, [userId]);
    
    if (currentUserResult.rows.length === 0) {
      return res.status(404).json({
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    let finalPreferences = currentUserResult.rows[0].preferences || {};

    // If preferences are provided, validate and merge them
    if (preferences) {
      // Validate only the partial preferences provided
      const preferencesValidation = validatePartialUserPreferences(preferences);
      
      if (!preferencesValidation.valid) {
        return res.status(400).json({
          error: 'Invalid preferences',
          code: 'PREFERENCES_VALIDATION_ERROR',
          details: preferencesValidation.message
        });
      }

      // Merge with existing preferences (only updates provided fields)
      finalPreferences = mergePreferences(finalPreferences, preferencesValidation.value);
      console.log('  - Merged preferences:', JSON.stringify(finalPreferences, null, 2));
    }

    const updateQuery = `
      UPDATE users 
      SET display_name = COALESCE($1, display_name),
          preferences = $2,
          updated_at = NOW()
      WHERE id = $3
      RETURNING id, username, email, display_name, preferences, is_active, is_verified, created_at, updated_at
    `;

    const result = await query(updateQuery, [
      display_name,
      JSON.stringify(finalPreferences),
      userId
    ]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    const updatedUser = result.rows[0];
    
    // Parse preferences for the response
    updatedUser.preferences = typeof updatedUser.preferences === 'string' 
      ? JSON.parse(updatedUser.preferences) 
      : updatedUser.preferences;
    
    console.log('✅ Profile update successful:');
    console.log('  - Updated avatar:', updatedUser.preferences?.selectedAvatar);
    console.log('  - Updated display name:', updatedUser.display_name);

    res.json({
      message: 'Profile updated successfully',
      user: updatedUser,
      validationInfo: preferences ? 'Preferences validated and merged with existing settings' : 'Display name updated'
    });

  } catch (error) {
    console.error('Profile update error:', error);
    
    // Handle JSON parsing errors
    if (error.message && error.message.includes('JSON')) {
      return res.status(400).json({
        error: 'Invalid preferences format',
        code: 'PREFERENCES_FORMAT_ERROR',
        details: error.message
      });
    }

    next(error);
  }
});

/**
 * GET /api/auth/verify-token
 * Verify if the current token is valid
 */
router.get('/verify-token', authenticateToken, (req, res) => {
  res.json({
    valid: true,
    user: {
      id: req.user.id,
      username: req.user.username,
      email: req.user.email
    }
  });
});

/**
 * GET /api/auth/avatars
 * Get available avatar options for user selection
 */
router.get('/avatars', (req, res) => {
  const { getAvatarInfo, VALID_AVATAR_IDS, DEFAULT_AVATAR_ID } = require('../constants/avatars');
  
  const avatarOptions = VALID_AVATAR_IDS.map(avatarId => ({
    id: avatarId,
    ...getAvatarInfo(avatarId)
  }));

  res.json({
    avatars: avatarOptions,
    defaultAvatar: DEFAULT_AVATAR_ID,
    totalCount: VALID_AVATAR_IDS.length
  });
});

module.exports = router;