const express = require('express');
const { generateTitleFromMessage, isValidForTitle } = require('../utils/titleGenerator');
const {
  validateSearchConfig
} = require('../utils/searchConfigValidator');
const { authenticateToken, optionalAuth } = require('../middleware/auth');
const router = express.Router();

// PostgreSQL client
const { Pool } = require('pg');
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'kg_qa_db',
  password: process.env.DB_PASSWORD || '',
  port: process.env.DB_PORT || 5432,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

module.exports = function(driver) {
  // PostgreSQL-based chat storage layer
  
  // Helper to generate a unique ID compatible with PostgreSQL UUID type
  const generateId = () => require('crypto').randomUUID();

  // POST /api/chat/message
  // Receives a message from the user, processes it, and returns a response.
  // Requires authentication to link messages to user
  router.post('/message', authenticateToken, async (req, res, next) => {
    // Destructure request body
    const { message, history, conversationId, searchConfig } = req.body;

    // Debug logging for GraphitiSettings troubleshooting
    console.log('🔧 DEBUG chatRoutes.js - Received POST /api/chat/message');
    console.log('🔧 DEBUG chatRoutes.js - Full request body:', JSON.stringify(req.body, null, 2));
    console.log('🔧 DEBUG chatRoutes.js - Request body keys:', Object.keys(req.body));

    // Handle the unified configuration - frontend now sends only one config type
    console.log('🔧 DEBUG chatRoutes.js - Configuration input:', {
      hasSearchConfig: !!searchConfig,
      mode: searchConfig?.mode
    });

    // Validate required fields
    if (!message) {
      console.log('Error: Message is required');
      return res.status(400).json({ error: 'Message is required' });
    }
    
    // Validate message is a string
    if (typeof message !== 'string') {
      console.log('Error: Message must be a string');
      return res.status(400).json({ error: 'Message must be a string' });
    }
    
    // Validate message is not empty after trimming
    if (message.trim().length === 0) {
      console.log('Error: Message cannot be empty');
      return res.status(400).json({ error: 'Message cannot be empty' });
    }
    
    // Validate history if provided
    if (history && !Array.isArray(history)) {
      console.log('Error: History must be an array');
      return res.status(400).json({ error: 'History must be an array' });
    }

    // Handle conversation ID logic
    let currentConversationId = conversationId;
    let isNewConversation = !conversationId;

    // Verify conversation exists and belongs to user if provided
    if (conversationId) {
      try {
        const checkQuery = `
          SELECT id FROM chat_sessions 
          WHERE id = $1::uuid AND user_id = $2::uuid
        `;
        const checkResult = await pool.query(checkQuery, [conversationId, req.user.id]);
        
        if (checkResult.rows.length === 0) {
          console.log(`🔍 Conversation ${conversationId} doesn't exist or doesn't belong to user ${req.user.id}, creating new conversation`);
          isNewConversation = true;
          currentConversationId = null;
        } else {
          console.log(`✅ Using existing conversation ${conversationId}`);
        }
      } catch (error) {
        console.log(`❌ Error checking conversation ${conversationId}:`, error.message);
        isNewConversation = true;
        currentConversationId = null;
      }
    } else {
      console.log('🆕 No conversationId provided, creating new conversation');
    }

    const configInput = searchConfig || {};
    const validation = validateSearchConfig(configInput);

    if (!validation.valid) {
      const errorMessage = validation.errors.join('; ');
      console.error('❌ Settings validation failed:', errorMessage);
      return res.status(400).json({ error: `Configuration validation failed: ${errorMessage}` });
    }

    const normalizedSearchConfig = validation.config;

    // Send unified search config - let Python service handle any needed conversions
    try {
      // Call the Python QA pipeline - send only unified search config
      const result = await callPythonQAPipeline(
        message,
        history,
        normalizedSearchConfig,
        null  // Never send legacy settings - Python handles conversion internally
      );

      // 🔍 ===== PYTHON RESPONSE DEBUG =====
      console.log('🔍 ===== PYTHON RESPONSE DEBUG =====');
      console.log('Full result object:', JSON.stringify(result, null, 2));
      console.log('structured_response exists:', !!result.structured_response);
      console.log('structured_response type:', typeof result.structured_response);

      if (result.structured_response) {
        console.log('Version:', result.structured_response.version);
        console.log('Format:', result.structured_response.format);
        console.log('Has core:', !!result.structured_response.core);
        console.log('Has core.summary:', result.structured_response.core?.summary ? 'YES' : 'NO');
        console.log('Core structure:', JSON.stringify(result.structured_response.core, null, 2));
        console.log('Extensions:', JSON.stringify(result.structured_response.extensions, null, 2));
      }
      console.log('🔍 ===== END PYTHON RESPONSE DEBUG =====');

      // Simplified Format C only logic
      let formatCResponse;

      // Python now always returns Format C - simply pass through
      if (result.structured_response) {
        formatCResponse = result.structured_response;
        
        // Basic validation for Format C responses
        if (!formatCResponse.core || !formatCResponse.core.summary) {
          throw new Error('Invalid Format C response: missing core.summary field');
        }
        
        console.log('✅ Format C response received and validated');
      } else {
        throw new Error('No structured response received from Python service');
      }
      
      // Debug logging - uncomment for development
      // console.log('✅ Transformed v2.0 to Format C response');

      // Only create new conversation if needed
      if (isNewConversation) {
        // Generate smart title from user message
        const smartTitle = isValidForTitle(message) 
          ? generateTitleFromMessage(message)
          : 'New Conversation';

        // Save the conversation to PostgreSQL (let database auto-generate INTEGER ID)
        const newConversation = {
          title: smartTitle,
          description: `Chat about: ${message.substring(0, 100)}...`,
          is_active: true,
          is_archived: false,
          metadata: {
            searchConfig: normalizedSearchConfig,
            source: 'chat-api',
            original_message: message // Store original message for reference
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        // Insert conversation into PostgreSQL using authenticated user ID
        const insertQuery = `
          INSERT INTO chat_sessions (user_id, title, description, is_active, is_archived, metadata, created_at, updated_at)
          VALUES ($1::uuid, $2, $3, $4, $5, $6, $7, $8)
          RETURNING *;
        `;
        
        const insertedConversation = await pool.query(insertQuery, [
          req.user.id, // Use authenticated user's ID
          newConversation.title,
          newConversation.description,
          newConversation.is_active,
          newConversation.is_archived,
          JSON.stringify(newConversation.metadata),
          newConversation.created_at,
          newConversation.updated_at
        ]);

        // Get the inserted conversation ID
        currentConversationId = insertedConversation.rows[0].id;
        console.log(`✅ Created new conversation with ID: ${currentConversationId}`);
      }

      // Insert messages into PostgreSQL with proper UUID format
      const messagesToInsert = [
        { role: 'user', content: message, timestamp: new Date().toISOString() },
        { role: 'assistant', content: JSON.stringify(formatCResponse), timestamp: new Date().toISOString() }
      ];
      
      for (const msg of messagesToInsert) {
        const messageQuery = `
          INSERT INTO messages (session_id, user_id, role, content, metadata, created_at)
          VALUES ($1::uuid, $2::uuid, $3, $4, $5, $6);
        `;
        
        await pool.query(messageQuery, [
          currentConversationId,
          req.user.id, // Use authenticated user's ID
          msg.role,
          msg.content,
          JSON.stringify({ source: 'chat-api' }),
          msg.timestamp
        ]);
      }

      res.json({
        response: {
          role: 'assistant',
          content: JSON.stringify(formatCResponse),
          timestamp: new Date().toISOString(),
          sourceDocuments: result.source_documents || [],
          sourceNodes: result.source_nodes || []
        },
        conversationId: currentConversationId,
        updatedHistory: messagesToInsert,
        format: formatCResponse.format || 'C',
        version: formatCResponse.version || '3.0',
        // Include structured response for frontend access
        structured_response: formatCResponse
      });
    } catch (error) {
      console.error('❌ ===== CHAT ERROR DEBUG =====');
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
      console.error('Request body:', JSON.stringify(req.body, null, 2));
      console.error('Request body keys:', Object.keys(req.body || {}));
      console.error('searchConfig keys:', req.body?.searchConfig ? Object.keys(req.body.searchConfig) : 'null');
      console.error('❌ ===== END CHAT ERROR DEBUG =====');

      // Continue with error handling
      next(error);
    }
  });

  // ========================================================================
  // DEPRECATED: Streaming implementation with subprocess spawning
  // ========================================================================
  // This streaming implementation has been DEPRECATED as of architectural unification.
  // Streaming requests are now routed through FastAPI (port 8000) instead of this
  // subprocess-based approach. The FastAPI streaming endpoint provides:
  // - Consistent v2.0 structured responses
  // - Proper entity extraction and metadata
  // - Unified architecture with non-streaming endpoint
  // 
  // This code is kept for reference but should NOT be used.
  // ========================================================================
  
  // End of deprecated streaming implementation


  // Function to call the FastAPI service for QA pipeline
  async function callPythonQAPipeline(question, history = [], searchConfig = {}) {
    const startTime = Date.now();
    const requestId = `chat-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    try {
      const fastApiUrl = process.env.FASTAPI_URL || 'http://localhost:8000';
      
      // Transform complex history objects to simple {role, content} format for FastAPI
      const cleanHistory = (history || []).map(msg => {
        // Handle different message formats
        if (typeof msg === 'string') {
          try {
            msg = JSON.parse(msg);
          } catch (e) {
            return { role: 'user', content: msg };
          }
        }
        
        // Extract role and content, handling nested structures
        let role = msg.role || 'user';
        let content = msg.content || '';
        
        // If content is a complex object (like structured response), extract the actual text
        if (typeof content === 'object') {
          content = JSON.stringify(content);
        }
        
        return { role, content };
      }).filter(msg => msg.content.trim().length > 0); // Filter out empty messages
      
      // Build request body based on mode to avoid conflicts
      const requestBody = {
        question: question,
        conversation_history: cleanHistory,
        // Send unified search config for both modes
        search_config: searchConfig
      };

      console.log(`📡 Calling FastAPI service at ${fastApiUrl}/chat`);
      console.log(`📝 Original history length: ${(history || []).length}, Cleaned history length: ${cleanHistory.length}`);
      console.log('🔧 DEBUG - Configuration being sent to FastAPI:', JSON.stringify(requestBody, null, 2));

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 180000); // 180 second timeout (3 minutes) for AtlasRAG

      const response = await fetch(`${fastApiUrl}/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-Request-ID': requestId
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`FastAPI request failed with status ${response.status}: ${errorText}`);
      }

      const result = await response.json();
      const executionTime = Date.now() - startTime;

      console.log(`✅ FastAPI response received in ${executionTime}ms`);
      
      return {
        ...result,
        execution_time_ms: executionTime,
        request_id: requestId,
        structured_response: result.structured_response || result
      };

    } catch (error) {
      console.error(`❌ FastAPI call failed:`, error);
      if (error.name === 'AbortError') {
        throw new Error(`FastAPI service request timed out after 180 seconds`);
      }
      throw new Error(`FastAPI service error: ${error.message}`);
    }
  }

  // --- PostgreSQL-based conversation management endpoints ---

  /**
   * GET /api/chat/conversations
   * List all saved conversations from PostgreSQL for the authenticated user
   */
  router.get('/conversations', authenticateToken, async (req, res, next) => {
    try {
      const { page = 1, limit = 20, archived = false, search = '' } = req.query;
      
      let query = `
        SELECT cs.*, u.username, COUNT(m.id) as message_count
        FROM chat_sessions cs
        LEFT JOIN users u ON cs.user_id = u.id
        LEFT JOIN messages m ON cs.id = m.session_id
        WHERE cs.is_archived = $1 AND cs.user_id = $2
      `;
      
      const params = [archived === 'true', req.user.id];
      
      if (search) {
        query += ` AND (cs.title ILIKE $${params.length + 1} OR cs.description ILIKE $${params.length + 1})`;
        params.push(`%${search}%`);
      }
      
      query += `
        GROUP BY cs.id, u.username
        ORDER BY cs.created_at DESC
        LIMIT $${params.length + 1} OFFSET $${params.length + 2}
      `;
      
      params.push(parseInt(limit), (parseInt(page) - 1) * parseInt(limit));
      
      const result = await pool.query(query, params);
      
      res.json({
        conversations: result.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: parseInt(result.rows[0]?.total_count || 0)
        }
      });
    } catch (error) {
      next(error);
    }
  });

  /**
   * POST /api/chat/conversations
   * Create a new conversation in PostgreSQL for the authenticated user
   */
  router.post('/conversations', authenticateToken, async (req, res, next) => {
    try {
      const { title, description } = req.body;
      
      const newConversation = {
        user_id: req.user.id, // Use authenticated user's ID
        title: title || 'New Conversation',
        description: description || 'New conversation',
        is_active: true,
        is_archived: false,
        metadata: {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const insertQuery = `
        INSERT INTO chat_sessions (user_id, title, description, is_active, is_archived, metadata, created_at, updated_at)
        VALUES ($1::uuid, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *;
      `;
      
      const result = await pool.query(insertQuery, [
        newConversation.user_id,
        newConversation.title,
        newConversation.description,
        newConversation.is_active,
        newConversation.is_archived,
        JSON.stringify(newConversation.metadata),
        newConversation.created_at,
        newConversation.updated_at
      ]);

      res.status(201).json(result.rows[0]);
    } catch (error) {
      next(error);
    }
  });

  /**
   * GET /api/chat/conversations/:id
   * Retrieve a specific conversation from PostgreSQL (only if owned by user)
   */
  router.get('/conversations/:id', authenticateToken, async (req, res, next) => {
    try {
      const { id } = req.params;
      
      const conversationQuery = `
        SELECT cs.*, u.username, COUNT(m.id) as message_count
        FROM chat_sessions cs
        LEFT JOIN users u ON cs.user_id = u.id
        LEFT JOIN messages m ON cs.id = m.session_id
        WHERE cs.id = $1 AND cs.user_id = $2
        GROUP BY cs.id, u.username;
      `;
      
      const conversationResult = await pool.query(conversationQuery, [id, req.user.id]);
      
      if (conversationResult.rows.length === 0) {
        return res.status(404).json({ error: 'Conversation not found' });
      }
      
      const messagesQuery = `
        SELECT * FROM messages
        WHERE session_id = $1
        ORDER BY created_at ASC;
      `;
      
      const messagesResult = await pool.query(messagesQuery, [id]);
      
      const conversation = {
        ...conversationResult.rows[0],
        messages: messagesResult.rows
      };
      
      res.json(conversation);
    } catch (error) {
      next(error);
    }
  });

  /**
   * PUT /api/chat/conversations/:id
   * Update a conversation in PostgreSQL (only if owned by user)
   */
  router.put('/conversations/:id', authenticateToken, async (req, res, next) => {
    try {
      const { id } = req.params;
      const { title, description, is_active, is_archived, metadata } = req.body;
      
      const updateQuery = `
        UPDATE chat_sessions
        SET title = COALESCE($1, title),
            description = COALESCE($2, description),
            is_active = COALESCE($3, is_active),
            is_archived = COALESCE($4, is_archived),
            metadata = COALESCE($5, metadata),
            updated_at = NOW()
        WHERE id = $6 AND user_id = $7
        RETURNING *;
      `;
      
      const result = await pool.query(updateQuery, [
        title,
        description,
        is_active,
        is_archived,
        JSON.stringify(metadata || {}),
        id,
        req.user.id
      ]);
      
      if (result.rows.length === 0) {
        return res.status(404).json({ error: 'Conversation not found' });
      }
      
      res.json(result.rows[0]);
    } catch (error) {
      next(error);
    }
  });

  /**
   * DELETE /api/chat/conversations/:id
   * Delete a conversation from PostgreSQL (only if owned by user)
   */
  router.delete('/conversations/:id', authenticateToken, async (req, res, next) => {
    try {
      const { id } = req.params;
      
      const deleteQuery = `
        DELETE FROM chat_sessions
        WHERE id = $1 AND user_id = $2
        RETURNING *;
      `;
      
      const result = await pool.query(deleteQuery, [id, req.user.id]);
      
      if (result.rows.length === 0) {
        return res.status(404).json({ error: 'Conversation not found' });
      }
      
      res.json({ message: 'Conversation deleted successfully' });
    } catch (error) {
      next(error);
    }
  });

  /**
   * GET /api/chat/conversations/:id/messages
   * Get messages for a specific conversation (only if owned by user)
   */
  router.get('/conversations/:id/messages', authenticateToken, async (req, res, next) => {
    try {
      const { id } = req.params;
      const { page = 1, limit = 50 } = req.query;
      
      // Validate UUID format
      const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidPattern.test(id)) {
        return res.status(400).json({
          error: 'Invalid conversation ID format. Expected UUID format.',
          code: 'INVALID_CONVERSATION_ID',
          provided: id,
          timestamp: new Date().toISOString()
        });
      }
      
      // First verify the user owns this conversation
      const verifyQuery = `
        SELECT id FROM chat_sessions WHERE id = $1 AND user_id = $2;
      `;
      
      console.log(`🔍 GET messages - Verifying conversation access: conversationId=${id}, userId=${req.user.id}`);
      const verifyResult = await pool.query(verifyQuery, [id, req.user.id]);
      
      if (verifyResult.rows.length === 0) {
        // Additional debug: check if conversation exists at all
        const existsQuery = `SELECT id, user_id FROM chat_sessions WHERE id = $1;`;
        const existsResult = await pool.query(existsQuery, [id]);
        
        console.error(`❌ GET messages - Conversation access denied:`, {
          conversationId: id,
          requestUserId: req.user.id,
          conversationExists: existsResult.rows.length > 0,
          actualOwner: existsResult.rows[0]?.user_id || 'N/A'
        });
        
        return res.status(404).json({
          error: 'Conversation not found or access denied',
          code: 'CONVERSATION_NOT_FOUND'
        });
      }
      
      const messagesQuery = `
        SELECT * FROM messages
        WHERE session_id = $1
        ORDER BY created_at ASC
        LIMIT $2 OFFSET $3;
      `;
      
      const messagesResult = await pool.query(messagesQuery, [
        id,
        parseInt(limit),
        (parseInt(page) - 1) * parseInt(limit)
      ]);
      
      res.json(messagesResult.rows);
    } catch (error) {
      next(error);
    }
  });

  /**
   * POST /api/chat/conversations/:id/messages
   * Add a message to a conversation (only if owned by user)
   */
  router.post('/conversations/:id/messages', authenticateToken, async (req, res, next) => {
    try {
      const { id } = req.params;
      const { content, role = 'user', parent_message_id = null } = req.body;
      
      // First verify the user owns this conversation
      const verifyQuery = `
        SELECT id FROM chat_sessions WHERE id = $1 AND user_id = $2;
      `;
      const verifyResult = await pool.query(verifyQuery, [id, req.user.id]);
      
      if (verifyResult.rows.length === 0) {
        return res.status(404).json({
          error: 'Conversation not found',
          code: 'CONVERSATION_NOT_FOUND'
        });
      }
      
      // If this is a user message, check if we need to update the conversation title
      if (role === 'user' && content && typeof content === 'string') {
        // Check if this conversation has a default title that should be updated
        const conversationQuery = `
          SELECT title, created_at, 
                 (SELECT COUNT(*) FROM messages WHERE session_id = $1 AND role = 'user') as user_message_count
          FROM chat_sessions WHERE id = $1;
        `;
        
        const conversationResult = await pool.query(conversationQuery, [id]);
        
        if (conversationResult.rows.length > 0) {
          const conversation = conversationResult.rows[0];
          const isFirstUserMessage = conversation.user_message_count === 0;
          const hasDefaultTitle = conversation.title === 'New Conversation' || 
                                 conversation.title.startsWith('Conversation from');
          
          // Update title if this is the first user message or if it has a default title
          if (isFirstUserMessage || hasDefaultTitle) {
            if (isValidForTitle(content)) {
              const smartTitle = generateTitleFromMessage(content);
              
              const updateTitleQuery = `
                UPDATE chat_sessions 
                SET title = $1, updated_at = NOW() 
                WHERE id = $2;
              `;
              
              await pool.query(updateTitleQuery, [smartTitle, id]);
              console.log(`✅ Updated conversation ${id} title to: ${smartTitle}`);
            }
          }
        }
      }
      
      const messageQuery = `
        INSERT INTO messages (session_id, user_id, role, content, parent_message_id, created_at)
        VALUES ($1, $2, $3, $4, $5, NOW())
        RETURNING *;
      `;
      
      const result = await pool.query(messageQuery, [
        id,
        req.user.id, // Use authenticated user's ID
        role,
        content,
        parent_message_id
      ]);
      
      res.status(201).json(result.rows[0]);
    } catch (error) {
      next(error);
    }
  });

  return router;
};
