const express = require('express');
const router = express.Router();

/**
 * GRAPH TRANSFORMATION ARCHITECTURE DOCUMENTATION
 * 
 * This file contains 3 similar but distinct graph data transformation endpoints:
 * 
 * 1. GET /initial (lines 60-210)
 *    - Purpose: Complete graph dataset with full node and relationship properties
 *    - Node properties: All properties including summary, group_id, category, url, created_at
 *    - Relationship properties: Both 'name' and 'fact' properties included
 *    - Used by: Main graph loading, detailed relationship inspection, full-featured UI
 *    - Performance: Highest memory usage, complete data fidelity
 * 
 * 2. GET /visualization (lines 212-384)
 *    - Purpose: Performance-optimized dataset with selective property inclusion
 *    - Node properties: Essential properties with truncated summaries (200 chars)
 *    - Relationship properties: Both 'name' and 'fact' properties included (same as /initial)
 *    - Used by: High-performance graph rendering, production environments with bandwidth constraints
 *    - Performance: Balanced memory usage, optimized for rendering performance
 * 
 * 3. GET /minimal (lines 386-558)
 *    - Purpose: Memory-optimized dataset with only essential fields for basic rendering
 *    - Node properties: Minimal set with truncated summaries (100 chars), excludes timestamps/metadata
 *    - Relationship properties: Both 'name' and 'fact' properties included (consistent with others)
 *    - Used by: Memory-constrained environments, mobile clients, basic graph visualization
 *    - Performance: Lowest memory usage, essential data only
 * 
 * RELATIONSHIP PROPERTY CONSISTENCY:
 * All 6 relationship processing instances use identical property extraction logic:
 * 
 * MAIN ENDPOINTS (3 instances):
 * - GET /initial (lines ~170)
 * - GET /visualization (lines ~355) 
 * - GET /minimal (lines ~527)
 * 
 * EXPANSION ENDPOINTS (2 instances):
 * - GET /expand 1-hop relationships (lines ~826)
 * - GET /expand 2-hop relationships (lines ~872)
 * 
 * FILTERING ENDPOINTS (1 instance):
 * - POST /filter (lines ~967)
 * 
 * PROPERTY EXTRACTION LOGIC (identical across all 6):
 * - Include: relationship.properties.name, relationship.properties.fact
 * - Exclude: fact_embedding, created_at, uuid, episodes, valid_at (memory-heavy properties)
 * 
 * ARCHITECTURAL RATIONALE:
 * These 3 endpoints exist as separate implementations rather than a single parameterized endpoint to:
 * - Optimize performance for specific use cases without runtime overhead
 * - Provide explicit data contracts for different client requirements
 * - Enable independent caching strategies and optimization paths
 * - Avoid complex conditional logic that could impact performance
 * 
 * CONSOLIDATION RISKS (analyzed in architectural review):
 * - Critical D3.js force simulation dependencies on link object structure
 * - WebGL/Three.js rendering dependencies on specific property formats
 * - Performance caching that relies on predictable object structures
 * - 16+ components with dependencies on current transformation format
 * 
 * See CLAUDE.md for detailed architectural documentation and usage guidelines.
 */

// Helper to determine node label from its properties
const getLabel = (properties, nodeLabels = []) => {
    // For Document nodes, return empty label since text snippets are too long
    if (nodeLabels && nodeLabels.includes('Document')) {
        return '';
    }

    // For Entity nodes, prioritize name property
    if (nodeLabels && nodeLabels.includes('Entity')) {
        if (properties.name) return properties.name;

        // If no name, use a snippet of summary
        if (properties.summary) {
            const snippet = properties.summary.substring(0, 50);
            return properties.summary.length > 50 ? `${snippet}...` : snippet;
        }

        // Fallback to uuid if available
        if (properties.uuid) return `Entity-${properties.uuid.substring(0, 8)}`;

        return 'Unnamed Entity';
    }

    // Legacy handling for other node types
    if (properties.name) return properties.name;
    if (properties.test_case_id) return properties.test_case_id;

    // Use 'id' if it's a descriptive string and not a UUID.
    if (properties.id && typeof properties.id === 'string' && !/^[0-9a-f]{8}-?[0-9a-f]{4}-?[0-9a-f]{4}-?[0-9a-f]{4}-?[0-9a-f]{12}$/i.test(properties.id)) {
        return properties.id;
    }

    // Use a snippet of 'text' if it exists.
    if (properties.text) {
        const snippet = properties.text.substring(0, 50);
        return properties.text.length > 50 ? `${snippet}...` : snippet;
    }

    return 'Unnamed';
};

// Graph routes will require access to the Neo4j driver
module.exports = (driver) => {
    // Use the database specified via environment variable (defaults to 'neo4j')
    const neo4jDatabase = process.env.NEO4J_DATABASE || 'neo4j';

    // Helper for creating sessions that automatically target the configured database
    const getSession = () => driver.session({ database: neo4jDatabase });

    // Get initial graph data
    // NOTE: This is one of 3 similar transformation endpoints in this file:
    // 1. /initial - Full dataset with complete node properties and relationship metadata
    // 2. /visualization - Optimized dataset with reduced property sets for performance
    // 3. /minimal - Minimal dataset with only essential fields for memory-constrained environments
    // Each serves different use cases and client requirements - see documentation below for details
    router.get('/initial', async (req, res, next) => {
        const { group_id, limit } = req.query;
        const nodeLimit = limit ? Math.floor(parseInt(limit, 10)) : null; // No default limit - use frontend setting
        const session = getSession();

        // Set caching headers for performance optimization
        res.set({
            'Cache-Control': 'public, max-age=300, s-maxage=600', // 5 min client, 10 min CDN
            'Vary': 'Accept-Encoding, Accept'
        });
        try {
            let query = `
                MATCH (n:Entity)-[r]->(m:Entity)
                WHERE 1=1
            `;

            const params = {};

            // Add group_id filtering if provided
            if (group_id) {
                query += ` AND n.group_id CONTAINS $group_id AND m.group_id CONTAINS $group_id`;
                params.group_id = group_id;
            }

            query += `
                RETURN n, r, m
            `;

            // Add limit for performance (Neo4j requires integer, not parameter for LIMIT)
            if (nodeLimit) {
                query += ` LIMIT ${nodeLimit}`;
            }

            const result = await session.run(query, params);
            
            // Transform Neo4j results to vis-network format
            const nodes = new Map();
            const edges = [];
            
            result.records.forEach(record => {
                const sourceNode = record.get('n');
                const targetNode = record.get('m');
                const relationship = record.get('r');
                
                // Process source node
                if (!nodes.has(sourceNode.identity.toString())) {
                    const nodeProps = sourceNode.properties;
                    const nodeLabel = sourceNode.labels[0];
                    const displayLabel = getLabel(nodeProps, sourceNode.labels);
                    
                    const optimizedNode = {
                        id: sourceNode.identity.toString(),
                        label: displayLabel,
                        title: `${nodeLabel}: ${displayLabel}`,
                        group: nodeLabel,
                        labels: sourceNode.labels,
                        properties: {
                            name: nodeProps.name,
                            summary: nodeProps.summary ? (nodeProps.summary.length > 200 ? nodeProps.summary.substring(0, 200) + '...' : nodeProps.summary) : undefined,
                            group_id: nodeProps.group_id,
                            category: nodeProps.category,
                            url: nodeProps.url,
                            created_at: nodeProps.created_at ? (nodeProps.created_at.toString ? nodeProps.created_at.toString() : nodeProps.created_at) : undefined
                        }
                    };

                    // Remove undefined properties to reduce payload size
                    Object.keys(optimizedNode.properties).forEach(key => {
                        if (optimizedNode.properties[key] === undefined) {
                            delete optimizedNode.properties[key];
                        }
                    });
                    
                    nodes.set(sourceNode.identity.toString(), optimizedNode);
                }
                
                // Process target node
                if (!nodes.has(targetNode.identity.toString())) {
                    const nodeProps = targetNode.properties;
                    const nodeLabel = targetNode.labels[0];
                    const displayLabel = getLabel(nodeProps, targetNode.labels);
                    
                    const optimizedNode = {
                        id: targetNode.identity.toString(),
                        label: displayLabel,
                        title: `${nodeLabel}: ${displayLabel}`,
                        group: nodeLabel,
                        labels: targetNode.labels,
                        properties: {
                            name: nodeProps.name,
                            summary: nodeProps.summary ? (nodeProps.summary.length > 200 ? nodeProps.summary.substring(0, 200) + '...' : nodeProps.summary) : undefined,
                            group_id: nodeProps.group_id,
                            category: nodeProps.category,
                            url: nodeProps.url,
                            created_at: nodeProps.created_at ? (nodeProps.created_at.toString ? nodeProps.created_at.toString() : nodeProps.created_at) : undefined
                        }
                    };

                    // Remove undefined properties to reduce payload size
                    Object.keys(optimizedNode.properties).forEach(key => {
                        if (optimizedNode.properties[key] === undefined) {
                            delete optimizedNode.properties[key];
                        }
                    });
                    
                    nodes.set(targetNode.identity.toString(), optimizedNode);
                }
                
                // Process relationship - INITIAL ENDPOINT VERSION
                // This version includes both 'name' and 'fact' properties for complete relationship data
                // Compare with /visualization (lines 323-338) and /minimal (lines 484-511) endpoints
                // Used by: Main graph loading, detailed relationship inspection
                edges.push({
                    id: relationship.identity.toString(),
                    from: sourceNode.identity.toString(),
                    to: targetNode.identity.toString(),
                    label: relationship.type,
                    arrows: 'to',
                    title: relationship.type,
                    properties: (() => {
                        // Exclude memory-heavy properties from relationship data
                        const props = {};
                        if (relationship.properties?.name) props.name = relationship.properties.name;
                        if (relationship.properties?.fact) props.fact = relationship.properties.fact;
                        return props;
                    })()
                });
            });
            
            const response = {
                nodes: Array.from(nodes.values()),
                edges: edges
            };

            // Generate ETag for conditional requests
            const crypto = require('crypto');
            const responseString = JSON.stringify(response);
            const etag = crypto.createHash('md5').update(responseString).digest('hex');

            res.set('ETag', `"${etag}"`);

            // Check if client has cached version
            if (req.headers['if-none-match'] === `"${etag}"`) {
                return res.status(304).end();
            }

            res.json(response);
        } catch (error) {
            next(error);
        } finally {
            await session.close();
        }
    });

    // Optimized visualization endpoint - excludes embeddings and unnecessary data
    // NOTE: This is the 2nd of 3 similar transformation endpoints:
    // 1. /initial (lines 54-197) - Complete dataset with full properties
    // 2. /visualization (this endpoint) - Performance-optimized with selective property inclusion
    // 3. /minimal (lines 375-545) - Memory-optimized with minimal essential data only
    // Used by: High-performance graph rendering, production environments with bandwidth constraints
    router.get('/visualization', async (req, res, next) => {
        const { group_id, limit } = req.query;
        const nodeLimit = limit ? Math.floor(parseInt(limit, 10)) : null; // No default limit - use frontend setting
        const session = getSession();

        // Set caching headers for performance optimization
        res.set({
            'Cache-Control': 'public, max-age=300, s-maxage=600', // 5 min client, 10 min CDN
            'Vary': 'Accept-Encoding, Accept'
        });

        try {
            let query = `
                MATCH (n:Entity)-[r]->(m:Entity)
                WHERE 1=1
            `;

            const params = {};

            // Add group_id filtering if provided
            if (group_id) {
                query += ` AND n.group_id CONTAINS $group_id AND m.group_id CONTAINS $group_id`;
                params.group_id = group_id;
            }

            query += `
                RETURN n, r, m
            `;

            // Add limit if specified (Neo4j requires integer, not parameter for LIMIT)
            if (nodeLimit) {
                query += ` LIMIT ${nodeLimit}`;
            }

            const result = await session.run(query, params);

            // Transform Neo4j results to optimized format
            const nodes = new Map();
            const edges = [];

            result.records.forEach(record => {
                const sourceNode = record.get('n');
                const targetNode = record.get('m');
                const relationship = record.get('r');

                // Process source node with optimized data
                if (!nodes.has(sourceNode.identity.toString())) {
                    const nodeProps = sourceNode.properties;
                    const nodeLabel = sourceNode.labels[0];
                    const displayLabel = getLabel(nodeProps, sourceNode.labels);

                    // Create optimized node object - exclude embeddings and unnecessary fields
                    const optimizedNode = {
                        id: sourceNode.identity.toString(),
                        label: displayLabel,
                        title: `${nodeLabel}: ${displayLabel}`,
                        group: nodeLabel,
                        labels: sourceNode.labels,
                        properties: {
                            name: nodeProps.name,
                            summary: nodeProps.summary ?
                                (nodeProps.summary.length > 200 ?
                                    nodeProps.summary.substring(0, 200) + '...' :
                                    nodeProps.summary) : undefined,
                            group_id: nodeProps.group_id,
                            category: nodeProps.category,
                            url: nodeProps.url,
                            // Convert Neo4j DateTime to simple ISO string
                            created_at: nodeProps.created_at ?
                                (nodeProps.created_at.toString ? nodeProps.created_at.toString() : nodeProps.created_at) : undefined
                            // Exclude: name_embedding, communityId, uuid
                        }
                    };

                    // Remove undefined properties to reduce payload size
                    Object.keys(optimizedNode.properties).forEach(key => {
                        if (optimizedNode.properties[key] === undefined) {
                            delete optimizedNode.properties[key];
                        }
                    });

                    nodes.set(sourceNode.identity.toString(), optimizedNode);
                }

                // Process target node with optimized data
                if (!nodes.has(targetNode.identity.toString())) {
                    const nodeProps = targetNode.properties;
                    const nodeLabel = targetNode.labels[0];
                    const displayLabel = getLabel(nodeProps, targetNode.labels);

                    // Create optimized node object - exclude embeddings and unnecessary fields
                    const optimizedNode = {
                        id: targetNode.identity.toString(),
                        label: displayLabel,
                        title: `${nodeLabel}: ${displayLabel}`,
                        group: nodeLabel,
                        labels: targetNode.labels,
                        properties: {
                            name: nodeProps.name,
                            summary: nodeProps.summary ?
                                (nodeProps.summary.length > 200 ?
                                    nodeProps.summary.substring(0, 200) + '...' :
                                    nodeProps.summary) : undefined,
                            group_id: nodeProps.group_id,
                            category: nodeProps.category,
                            url: nodeProps.url,
                            // Convert Neo4j DateTime to simple ISO string
                            created_at: nodeProps.created_at ?
                                (nodeProps.created_at.toString ? nodeProps.created_at.toString() : nodeProps.created_at) : undefined
                            // Exclude: name_embedding, communityId, uuid
                        }
                    };

                    // Remove undefined properties to reduce payload size
                    Object.keys(optimizedNode.properties).forEach(key => {
                        if (optimizedNode.properties[key] === undefined) {
                            delete optimizedNode.properties[key];
                        }
                    });

                    nodes.set(targetNode.identity.toString(), optimizedNode);
                }

                // Process relationship with minimal data - VISUALIZATION ENDPOINT VERSION
                // This version includes both 'name' and 'fact' properties, same as /initial endpoint
                // Compare with /initial (lines 156-171) and /minimal (lines 484-511) endpoints
                // Used by: Performance-optimized graph rendering, bandwidth-constrained environments
                edges.push({
                    id: relationship.identity.toString(),
                    from: sourceNode.identity.toString(),
                    to: targetNode.identity.toString(),
                    label: relationship.type,
                    arrows: 'to',
                    title: relationship.type,
                    properties: (() => {
                        // Exclude memory-heavy properties from relationship data
                        const props = {};
                        if (relationship.properties?.name) props.name = relationship.properties.name;
                        if (relationship.properties?.fact) props.fact = relationship.properties.fact;
                        return props;
                    })()
                });
            });

            const response = {
                nodes: Array.from(nodes.values()),
                edges: edges,
                metadata: {
                    optimized: true,
                    excluded_fields: ['name_embedding', 'communityId', 'uuid'],
                    included_fields: ['name', 'summary', 'group_id', 'category', 'url', 'created_at'],
                    summary_truncated: true,
                    compression_enabled: true
                }
            };

            // Generate ETag for conditional requests
            const crypto = require('crypto');
            const responseString = JSON.stringify(response);
            const etag = crypto.createHash('md5').update(responseString).digest('hex');

            res.set('ETag', `"${etag}"`);

            // Check if client has cached version
            if (req.headers['if-none-match'] === `"${etag}"`) {
                return res.status(304).end();
            }

            res.json(response);
        } catch (error) {
            next(error);
        } finally {
            await session.close();
        }
    });

    // Minimal visualization endpoint - only essential fields for graph rendering
    // Returns complete dataset without artificial limits for optimal performance with reduced payload
    // NOTE: This is the 3rd of 3 similar transformation endpoints:
    // 1. /initial (lines 54-197) - Complete dataset with full node and relationship properties
    // 2. /visualization (lines 199-371) - Performance-optimized with selective property sets
    // 3. /minimal (this endpoint) - Memory-optimized with only essential fields for basic rendering
    // Used by: Memory-constrained environments, mobile clients, basic graph visualization
    router.get('/minimal', async (req, res, next) => {
        const { group_id, limit } = req.query;
        const nodeLimit = limit ? Math.floor(parseInt(limit, 10)) : null; // No default limit - use frontend setting
        const session = getSession();

        // Set caching headers for performance optimization
        res.set({
            'Cache-Control': 'public, max-age=300, s-maxage=600', // 5 min client, 10 min CDN
            'Vary': 'Accept-Encoding, Accept'
        });

        try {
            let query = `
                MATCH (n:Entity)-[r]->(m:Entity)
                WHERE 1=1
            `;

            const params = {};

            // Add group_id filtering if provided
            if (group_id) {
                query += ` AND n.group_id CONTAINS $group_id AND m.group_id CONTAINS $group_id`;
                params.group_id = group_id;
            }

            query += `
                RETURN n, r, m
            `;

            // Add limit for performance (Neo4j requires integer, not parameter for LIMIT)
            if (nodeLimit) {
                query += ` LIMIT ${nodeLimit}`;
            }

            const result = await session.run(query, params);

            // Transform Neo4j results to minimal format
            const nodes = new Map();
            const edges = [];

            result.records.forEach(record => {
                const sourceNode = record.get('n');
                const targetNode = record.get('m');
                const relationship = record.get('r');

                // Process source node with minimal data
                if (!nodes.has(sourceNode.identity.toString())) {
                    const nodeProps = sourceNode.properties;
                    const nodeLabel = sourceNode.labels[0];
                    const displayLabel = getLabel(nodeProps, sourceNode.labels);

                    // Create minimal node object - only essential fields
                    const minimalNode = {
                        id: sourceNode.identity.toString(),
                        label: displayLabel,
                        title: `${nodeLabel}: ${displayLabel}`,
                        group: nodeLabel,
                        labels: sourceNode.labels,
                        properties: {
                            name: nodeProps.name,
                            summary: nodeProps.summary ?
                                (nodeProps.summary.length > 100 ?
                                    nodeProps.summary.substring(0, 100) + '...' :
                                    nodeProps.summary) : undefined,
                            category: nodeProps.category,
                            url: nodeProps.url
                            // Exclude: embeddings, timestamps, UUIDs, group_ids, metadata
                        }
                    };

                    // Remove undefined properties to reduce payload size
                    Object.keys(minimalNode.properties).forEach(key => {
                        if (minimalNode.properties[key] === undefined) {
                            delete minimalNode.properties[key];
                        }
                    });

                    nodes.set(sourceNode.identity.toString(), minimalNode);
                }

                // Process target node with minimal data
                if (!nodes.has(targetNode.identity.toString())) {
                    const nodeProps = targetNode.properties;
                    const nodeLabel = targetNode.labels[0];
                    const displayLabel = getLabel(nodeProps, targetNode.labels);

                    // Create minimal node object - only essential fields
                    const minimalNode = {
                        id: targetNode.identity.toString(),
                        label: displayLabel,
                        title: `${nodeLabel}: ${displayLabel}`,
                        group: nodeLabel,
                        labels: targetNode.labels,
                        properties: {
                            name: nodeProps.name,
                            summary: nodeProps.summary ?
                                (nodeProps.summary.length > 100 ?
                                    nodeProps.summary.substring(0, 100) + '...' :
                                    nodeProps.summary) : undefined,
                            category: nodeProps.category,
                            url: nodeProps.url
                            // Exclude: embeddings, timestamps, UUIDs, group_ids, metadata
                        }
                    };

                    // Remove undefined properties to reduce payload size
                    Object.keys(minimalNode.properties).forEach(key => {
                        if (minimalNode.properties[key] === undefined) {
                            delete minimalNode.properties[key];
                        }
                    });

                    nodes.set(targetNode.identity.toString(), minimalNode);
                }

                // Process relationship with minimal data - MINIMAL ENDPOINT VERSION
                // This version includes both 'name' and 'fact' properties, same as other endpoints
                // Compare with /initial (lines 156-171) and /visualization (lines 323-338) endpoints
                // Used by: Memory-constrained environments, mobile clients, basic visualization
                const minimalEdge = {
                    id: relationship.identity.toString(),
                    from: sourceNode.identity.toString(),
                    to: targetNode.identity.toString(),
                    label: relationship.type,
                    arrows: 'to',
                    title: relationship.type,
                    properties: {}
                };

                // Include only essential relationship properties for graph visualization
                // Exclude large embedding and metadata properties to save memory
                // NOTE: Property inclusion logic is identical across all 3 endpoints for consistency
                if (relationship.properties.name) {
                    minimalEdge.properties.name = relationship.properties.name;
                }
                if (relationship.properties.fact) {
                    minimalEdge.properties.fact = relationship.properties.fact;
                }
                
                // Explicitly exclude memory-heavy properties:
                // - fact_embedding (largest memory consumer)
                // - created_at (Neo4j date objects)
                // - uuid (not needed for visualization)
                // - episodes (array data)
                // - valid_at (Neo4j date objects)

                edges.push(minimalEdge);
            });

            const response = {
                nodes: Array.from(nodes.values()),
                edges: edges,
                metadata: {
                    minimal: true,
                    included_node_fields: ['name', 'summary', 'category', 'url', 'labels'],
                    included_edge_fields: ['name', 'fact'],
                    excluded_fields: ['fact_embedding', 'name_embedding', 'created_at', 'valid_at', 'uuid', 'episodes', 'timestamps', 'group_ids', 'metadata'],
                    summary_truncated: true,
                    compression_enabled: true
                }
            };

            // Generate ETag for conditional requests
            const crypto = require('crypto');
            const responseString = JSON.stringify(response);
            const etag = crypto.createHash('md5').update(responseString).digest('hex');

            res.set('ETag', `"${etag}"`);

            // Check if client has cached version
            if (req.headers['if-none-match'] === `"${etag}"`) {
                return res.status(304).end();
            }

            res.json(response);
        } catch (error) {
            next(error);
        } finally {
            await session.close();
        }
    });

    // Search nodes by name or properties
    router.get('/search', async (req, res, next) => {
        const { term, group_id } = req.query;
        const rawCategoryFilters = req.query.categoryFilters;
        if (!term) {
            return res.status(400).json({ error: 'Search term is required' });
        }

        const session = getSession();
        try {
            let query = `
                MATCH (n:Entity)
                WHERE (
                    (n.name IS NOT NULL AND toLower(n.name) CONTAINS toLower($term))
                    OR (n.summary IS NOT NULL AND toLower(n.summary) CONTAINS toLower($term))
                    OR (n.uuid IS NOT NULL AND toLower(n.uuid) CONTAINS toLower($term))
                )
            `;

            const params = { term };

            // Normalize optional category filter query parameter (supports repeat or comma-separated values)
            let categoryFilters = [];
            if (Array.isArray(rawCategoryFilters)) {
                categoryFilters = rawCategoryFilters.flatMap((value) => value.split(',').map((item) => item.trim()));
            } else if (typeof rawCategoryFilters === 'string') {
                categoryFilters = rawCategoryFilters.split(',').map((item) => item.trim());
            }
            categoryFilters = categoryFilters.filter((category) => category.length > 0);

            if (categoryFilters.length > 0) {
                query += ` AND n.category IN $categoryFilters`;
                params.categoryFilters = categoryFilters;
            }

            // Add group_id filtering if provided
            if (group_id) {
                query += ` AND n.group_id CONTAINS $group_id`;
                params.group_id = group_id;
            }

            query += `
                RETURN n
                LIMIT 20
            `;

            const result = await session.run(query, params);
            
            const nodes = [];
            
            result.records.forEach(record => {
                const node = record.get('n');
                const nodeProps = node.properties;
                const nodeLabel = node.labels[0];
                const displayLabel = getLabel(nodeProps, node.labels);
                
                nodes.push({
                    id: node.identity.toString(),
                    label: displayLabel,
                    title: `${nodeLabel}: ${displayLabel}`,
                    group: nodeLabel,
                    properties: nodeProps
                });
            });
            
            res.json({ nodes });
        } catch (error) {
            next(error);
        } finally {
            await session.close();
        }
    });

    // Get all unique group_id values from Entity nodes
    router.get('/group-ids', async (req, res, next) => {
        const session = getSession();
        try {
            const query = `
                MATCH (n:Entity)
                WHERE n.group_id IS NOT NULL
                RETURN DISTINCT n.group_id as group_id
                ORDER BY n.group_id
            `;

            const result = await session.run(query);

            const groupIds = result.records.map(record => record.get('group_id'));

            res.json({
                groupIds,
                count: groupIds.length,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            next(error);
        } finally {
            await session.close();
        }
    });

    // Expand a node (get connected nodes with 2-hop expansion for better discovery)
    router.get('/expand', async (req, res, next) => {
        const { nodeId, group_id } = req.query;
        if (!nodeId) {
            return res.status(400).json({ error: 'Node ID is required' });
        }

        const session = getSession();
        try {
            // Enhanced query to get both 1-hop and 2-hop connections for better node discovery
            let query = `
                MATCH (center:Entity)
                WHERE (id(center) = toInteger($nodeId) OR center.name = $nodeId OR center.uuid = $nodeId)

                // Get 1-hop connections
                OPTIONAL MATCH (center)-[r1]-(hop1:Entity)

                // Get 2-hop connections for richer expansion
                OPTIONAL MATCH (hop1)-[r2]-(hop2:Entity)
                WHERE hop2 <> center
            `;

            const params = { nodeId };

            // Add group_id filtering if provided
            if (group_id) {
                query += `
                    AND (hop1 IS NULL OR hop1.group_id CONTAINS $group_id)
                    AND (hop2 IS NULL OR hop2.group_id CONTAINS $group_id)
                    AND center.group_id CONTAINS $group_id`;
                params.group_id = group_id;
            }

            query += `
                RETURN center, r1, hop1, r2, hop2
                LIMIT 200
            `;

            const result = await session.run(query, params);

            // Transform Neo4j results to vis-network format with enhanced 2-hop processing
            const nodes = new Map();
            const edges = [];

            result.records.forEach(record => {
                const centerNode = record.get('center');
                const hop1Node = record.get('hop1');
                const hop2Node = record.get('hop2');
                const r1 = record.get('r1');
                const r2 = record.get('r2');

                // Always include the center node
                if (centerNode && !nodes.has(centerNode.identity.toString())) {
                    const nodeProps = centerNode.properties;
                    const nodeLabel = centerNode.labels[0];
                    const displayLabel = getLabel(nodeProps, centerNode.labels);

                    nodes.set(centerNode.identity.toString(), {
                        id: centerNode.identity.toString(),
                        label: displayLabel,
                        title: `${nodeLabel}: ${displayLabel}`,
                        group: nodeLabel,
                        properties: nodeProps
                    });
                }

                // Process 1-hop connections
                if (hop1Node && r1) {
                    if (!nodes.has(hop1Node.identity.toString())) {
                        const nodeProps = hop1Node.properties;
                        const nodeLabel = hop1Node.labels[0];
                        const displayLabel = getLabel(nodeProps, hop1Node.labels);

                        nodes.set(hop1Node.identity.toString(), {
                            id: hop1Node.identity.toString(),
                            label: displayLabel,
                            title: `${nodeLabel}: ${displayLabel}`,
                            group: nodeLabel,
                            properties: nodeProps
                        });
                    }

                    // Add 1-hop relationship - EXPAND ENDPOINT VERSION (1st of 2 in this endpoint)
                    // This version includes both 'name' and 'fact' properties, consistent with main endpoints
                    // Compare with /initial (lines 156-171), /visualization (lines 339-354), /minimal (lines 500-527)
                    // Used by: Node expansion feature, relationship discovery, connected node exploration
                    const r1Id = r1.identity.toString();
                    if (!edges.find(e => e.id === r1Id)) {
                        let from, to;
                        if (r1.startNodeElementId === centerNode.elementId) {
                            from = centerNode.identity.toString();
                            to = hop1Node.identity.toString();
                        } else {
                            from = hop1Node.identity.toString();
                            to = centerNode.identity.toString();
                        }

                        edges.push({
                            id: r1Id,
                            from,
                            to,
                            label: r1.type,
                            arrows: 'to',
                            title: r1.type,
                            properties: (() => {
                                // Exclude memory-heavy properties from relationship data
                                const props = {};
                                if (r1.properties?.name) props.name = r1.properties.name;
                                if (r1.properties?.fact) props.fact = r1.properties.fact;
                                return props;
                            })()
                        });
                    }
                }

                // Process 2-hop connections for expanded discovery
                if (hop2Node && r2 && hop1Node) {
                    if (!nodes.has(hop2Node.identity.toString())) {
                        const nodeProps = hop2Node.properties;
                        const nodeLabel = hop2Node.labels[0];
                        const displayLabel = getLabel(nodeProps, hop2Node.labels);

                        nodes.set(hop2Node.identity.toString(), {
                            id: hop2Node.identity.toString(),
                            label: displayLabel,
                            title: `${nodeLabel}: ${displayLabel}`,
                            group: nodeLabel,
                            properties: nodeProps
                        });
                    }

                    // Add 2-hop relationship
                    const r2Id = r2.identity.toString();
                    if (!edges.find(e => e.id === r2Id)) {
                        let from, to;
                        if (r2.startNodeElementId === hop1Node.elementId) {
                            from = hop1Node.identity.toString();
                            to = hop2Node.identity.toString();
                        } else {
                            from = hop2Node.identity.toString();
                            to = hop1Node.identity.toString();
                        }

                        edges.push({
                            id: r2Id,
                            from,
                            to,
                            label: r2.type,
                            arrows: 'to',
                            title: r2.type,
                            properties: (() => {
                                // Exclude memory-heavy properties from relationship data - EXPAND ENDPOINT VERSION (2nd of 2)
                                // This version includes both 'name' and 'fact' properties, consistent with main endpoints
                                // Compare with /initial (lines 156-171), /visualization (lines 339-354), /minimal (lines 500-527)
                                // Used by: 2-hop relationship discovery, expanded node exploration
                                const props = {};
                                if (r2.properties?.name) props.name = r2.properties.name;
                                if (r2.properties?.fact) props.fact = r2.properties.fact;
                                return props;
                            })()
                        });
                    }
                }
            });
            
            res.json({
                nodes: Array.from(nodes.values()),
                edges: edges
            });
        } catch (error) {
            next(error);
        } finally {
            await session.close();
        }
    });
    
    // Get filtered graph data
    router.post('/filter', async (req, res, next) => {
        const { nodeLabels = [], relationshipTypes = [], group_id } = req.body;

        const session = getSession();
        try {
            let query = `
                MATCH (n:Entity)-[r]->(m:Entity)
                WHERE 1=1
            `;

            const params = {};

            // For Entity nodes, we can filter by relationship types
            if (relationshipTypes.length > 0) {
                query += `
                    AND type(r) IN $relationshipTypes
                `;
                params.relationshipTypes = relationshipTypes;
            }

            // Add group_id filtering if provided
            if (group_id) {
                query += ` AND n.group_id CONTAINS $group_id AND m.group_id CONTAINS $group_id`;
                params.group_id = group_id;
            }

            query += `
                RETURN n, r, m
                LIMIT 100
            `;

            const result = await session.run(query, params);
            
            // Transform Neo4j results to vis-network format
            const nodes = new Map();
            const edges = [];
            
            result.records.forEach(record => {
                const sourceNode = record.get('n');
                const targetNode = record.get('m');
                const relationship = record.get('r');
                
                // Process source node
                if (!nodes.has(sourceNode.identity.toString())) {
                    const nodeProps = sourceNode.properties;
                    const nodeLabel = sourceNode.labels[0];
                    const displayLabel = getLabel(nodeProps, sourceNode.labels);
                    
                    nodes.set(sourceNode.identity.toString(), {
                        id: sourceNode.identity.toString(),
                        label: displayLabel,
                        title: `${nodeLabel}: ${displayLabel}`,
                        group: nodeLabel,
                        properties: nodeProps
                    });
                }
                
                // Process target node
                if (!nodes.has(targetNode.identity.toString())) {
                    const nodeProps = targetNode.properties;
                    const nodeLabel = targetNode.labels[0];
                    const displayLabel = getLabel(nodeProps, targetNode.labels);
                    
                    nodes.set(targetNode.identity.toString(), {
                        id: targetNode.identity.toString(),
                        label: displayLabel,
                        title: `${nodeLabel}: ${displayLabel}`,
                        group: nodeLabel,
                        properties: nodeProps
                    });
                }
                
                // Process relationship - FILTER ENDPOINT VERSION
                // This version includes both 'name' and 'fact' properties, consistent with main endpoints
                // Compare with /initial (lines 156-171), /visualization (lines 339-354), /minimal (lines 500-527), /expand (lines 812-826 & 860-872)
                // Used by: Filtered graph views, relationship type filtering, custom graph queries
                edges.push({
                    id: relationship.identity.toString(),
                    from: sourceNode.identity.toString(),
                    to: targetNode.identity.toString(),
                    label: relationship.type,
                    arrows: 'to',
                    title: relationship.type,
                    properties: (() => {
                        // Exclude memory-heavy properties from relationship data
                        const props = {};
                        if (relationship.properties?.name) props.name = relationship.properties.name;
                        if (relationship.properties?.fact) props.fact = relationship.properties.fact;
                        return props;
                    })()
                });
            });
            
            res.json({
                nodes: Array.from(nodes.values()),
                edges: edges
            });
        } catch (error) {
            next(error);
        } finally {
            await session.close();
        }
    });
    
    // Execute arbitrary Cypher query (for Python proxy)
    router.post('/query', async (req, res) => {
        try {
            const { query, parameters = {} } = req.body;
            
            if (!query) {
                return res.status(400).json({ error: 'Query is required' });
            }
            
            console.log('Executing Cypher query:', query);
            console.log('Parameters:', parameters);
            
            const session = getSession();
            const result = await session.run(query, parameters);
            
            // Convert Neo4j result to a format compatible with Python
            const data = result.records.map(record => {
                const obj = {};
                record.keys.forEach(key => {
                    const value = record.get(key);
                    // Handle Neo4j types
                    if (value && typeof value === 'object' && value.constructor.name === 'Integer') {
                        obj[key] = value.toNumber();
                    } else if (value && typeof value === 'object' && value.properties) {
                        // Node or relationship
                        obj[key] = {
                            ...value.properties,
                            labels: value.labels || [],
                            type: value.type || null
                        };
                    } else {
                        obj[key] = value;
                    }
                });
                return obj;
            });
            
            await session.close();
            
            res.json({
                data: data,
                summary: {
                    queryType: result.summary.queryType,
                    counters: result.summary.counters,
                    resultAvailableAfter: result.summary.resultAvailableAfter,
                    resultConsumedAfter: result.summary.resultConsumedAfter
                }
            });
            
        } catch (error) {
            console.error('Query execution error:', error);
            res.status(500).json({ 
                error: 'Query execution failed', 
                details: error.message 
            });
        }
    });

    return router;
}; 
