const express = require('express');
const router = express.Router();
const axios = require('axios');
const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

module.exports = function() {
  
  /**
   * GET /api/ollama/models
   * Fetch available Ollama models, excluding embedding models
   */
  router.get('/models', async (req, res) => {
    try {
      const ollamaUrl = process.env.OLLAMA_URL || 'http://localhost:11434';
      
      // Fetch models from Ollama API
      const response = await axios.get(`${ollamaUrl}/api/tags`, { timeout: 5000 });
      const data = response.data;
      
      // Filter out embedding models and extract model info
      const chatModels = data.models
        .filter(model => {
          const name = model.name.toLowerCase();
          return !name.includes('embed') && 
                 !name.includes('embedding') && 
                 !name.includes('nomic') &&
                 !name.includes('bge-') &&
                 !name.includes('e5-');
        })
        .map(model => ({
          name: model.name,
          size: model.size || 'Unknown',
          modified_at: model.modified_at
        }))
        .sort((a, b) => a.name.localeCompare(b.name)); // Sort alphabetically
      
      res.json(chatModels);
      
    } catch (error) {
      console.error('Error fetching Ollama models:', error);
      
      // Return fallback models on error
      res.json([
        { name: 'deepseek-r1:8b', size: 'Unknown' },
        { name: 'llama3.2:3b', size: 'Unknown' },
        { name: 'llama3.1:8b', size: 'Unknown' },
        { name: 'codestral:22b', size: 'Unknown' }
      ]);
    }
  });

  /**
   * GET /api/ollama/status
   * Check if Ollama server is available
   */
  router.get('/status', async (req, res) => {
    try {
      const ollamaUrl = process.env.OLLAMA_URL || 'http://localhost:11434';
      console.log('Ollama status check - URL:', ollamaUrl);
      const fullUrl = `${ollamaUrl}/api/version`;
      console.log('Full URL:', fullUrl);
      const response = await axios.get(fullUrl, { timeout: 5000 });
      
      res.json({ 
        available: true, 
        version: response.data.version || 'Unknown',
        url: ollamaUrl 
      });
      
    } catch (error) {
      console.error('Ollama status error:', error.message);
      console.error('Error config:', error.config?.url);
      res.json({ 
        available: false, 
        error: error.message,
        url: process.env.OLLAMA_URL || 'http://localhost:11434'
      });
    }
  });

  return router;
};