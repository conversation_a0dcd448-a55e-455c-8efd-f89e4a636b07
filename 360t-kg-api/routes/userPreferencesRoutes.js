const express = require('express');
const { query } = require('../database/config');
const { authenticateToken, optionalAuth } = require('../middleware/auth');
const { validateSettings } = require('../utils/preferenceValidation');

/**
 * Middleware to validate UUID format for user ID parameter
 */
const validateUserIdUUID = (req, res, next) => {
  const { userId } = req.params;
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  
  if (!uuidRegex.test(userId)) {
    return res.status(400).json({
      error: 'Invalid user ID format. Must be a valid UUID.',
      code: 'INVALID_USER_ID'
    });
  }
  
  next();
};

/**
 * User-specific preferences API routes
 * Provides database-backed storage for user legend colors and settings
 */
const router = express.Router();

/**
 * GET /api/users/:userId/preferences
 * Get all preferences for a specific user
 */
router.get('/:userId/preferences', validateUserIdUUID, authenticateToken, async (req, res, next) => {
  try {
    const { userId } = req.params;
    
    // Ensure user can only access their own preferences
    if (req.user.id !== userId) {
      return res.status(403).json({
        error: 'Access denied. Cannot access other user preferences.',
        code: 'FORBIDDEN'
      });
    }

    const preferencesQuery = `
      SELECT preference_key, preference_value, created_at, updated_at
      FROM user_preferences 
      WHERE user_id = $1
      ORDER BY preference_key
    `;

    const result = await query(preferencesQuery, [userId]);
    
    // Transform rows into key-value object
    const preferences = {};
    result.rows.forEach(row => {
      preferences[row.preference_key] = {
        value: row.preference_value,
        created_at: row.created_at,
        updated_at: row.updated_at
      };
    });

    res.json({
      userId,
      preferences,
      count: result.rows.length
    });

  } catch (error) {
    console.error('Get user preferences error:', error);
    next(error);
  }
});

/**
 * GET /api/users/:userId/preferences/:preferenceKey
 * Get a specific preference for a user
 */
router.get('/:userId/preferences/:preferenceKey', validateUserIdUUID, authenticateToken, async (req, res, next) => {
  try {
    const { userId, preferenceKey } = req.params;
    
    // Ensure user can only access their own preferences
    if (req.user.id !== userId) {
      return res.status(403).json({
        error: 'Access denied. Cannot access other user preferences.',
        code: 'FORBIDDEN'
      });
    }

    const preferenceQuery = `
      SELECT preference_key, preference_value, created_at, updated_at
      FROM user_preferences 
      WHERE user_id = $1 AND preference_key = $2
    `;

    const result = await query(preferenceQuery, [userId, preferenceKey]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Preference not found',
        code: 'PREFERENCE_NOT_FOUND'
      });
    }

    const preference = result.rows[0];
    res.json({
      userId,
      preferenceKey,
      value: preference.preference_value,
      created_at: preference.created_at,
      updated_at: preference.updated_at
    });

  } catch (error) {
    console.error('Get user preference error:', error);
    next(error);
  }
});

/**
 * PUT /api/users/:userId/preferences/:preferenceKey
 * Set/update a specific preference for a user
 */
router.put('/:userId/preferences/:preferenceKey', validateUserIdUUID, authenticateToken, async (req, res, next) => {
  try {
    const { userId, preferenceKey } = req.params;
    const { value } = req.body;
    
    // Ensure user can only modify their own preferences
    if (req.user.id !== userId) {
      return res.status(403).json({
        error: 'Access denied. Cannot modify other user preferences.',
        code: 'FORBIDDEN'
      });
    }

    if (value === undefined) {
      return res.status(400).json({
        error: 'Preference value is required',
        code: 'MISSING_VALUE'
      });
    }

    // Validate legend color preferences
    if (preferenceKey === 'legend_colors' && !validateSettings({ nodeColors: value.nodeColors, relationshipColors: value.relationshipColors })) {
      return res.status(400).json({
        error: 'Invalid legend colors format',
        code: 'INVALID_LEGEND_COLORS'
      });
    }

    const upsertQuery = `
      INSERT INTO user_preferences (user_id, preference_key, preference_value, created_at, updated_at)
      VALUES ($1, $2, $3, NOW(), NOW())
      ON CONFLICT (user_id, preference_key)
      DO UPDATE SET 
        preference_value = EXCLUDED.preference_value,
        updated_at = NOW()
      RETURNING preference_key, preference_value, created_at, updated_at
    `;

    const result = await query(upsertQuery, [userId, preferenceKey, JSON.stringify(value)]);
    const savedPreference = result.rows[0];

    res.json({
      userId,
      preferenceKey,
      value: savedPreference.preference_value,
      created_at: savedPreference.created_at,
      updated_at: savedPreference.updated_at,
      message: 'Preference saved successfully'
    });

  } catch (error) {
    console.error('Set user preference error:', error);
    next(error);
  }
});

/**
 * DELETE /api/users/:userId/preferences/:preferenceKey
 * Delete a specific preference for a user
 */
router.delete('/:userId/preferences/:preferenceKey', validateUserIdUUID, authenticateToken, async (req, res, next) => {
  try {
    const { userId, preferenceKey } = req.params;
    
    // Ensure user can only delete their own preferences
    if (req.user.id !== userId) {
      return res.status(403).json({
        error: 'Access denied. Cannot delete other user preferences.',
        code: 'FORBIDDEN'
      });
    }

    const deleteQuery = `
      DELETE FROM user_preferences 
      WHERE user_id = $1 AND preference_key = $2
      RETURNING preference_key
    `;

    const result = await query(deleteQuery, [userId, preferenceKey]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Preference not found',
        code: 'PREFERENCE_NOT_FOUND'
      });
    }

    res.json({
      userId,
      preferenceKey,
      message: 'Preference deleted successfully'
    });

  } catch (error) {
    console.error('Delete user preference error:', error);
    next(error);
  }
});

/**
 * POST /api/users/:userId/preferences/legend-colors
 * Specialized endpoint for saving legend colors
 */
router.post('/:userId/preferences/legend-colors', validateUserIdUUID, authenticateToken, async (req, res, next) => {
  try {
    const { userId } = req.params;
    const { nodeColors, relationshipColors } = req.body;
    
    // Ensure user can only modify their own preferences
    if (req.user.id !== userId) {
      return res.status(403).json({
        error: 'Access denied. Cannot modify other user preferences.',
        code: 'FORBIDDEN'
      });
    }

    // Validate legend colors format
    if (!validateSettings({ nodeColors, relationshipColors })) {
      return res.status(400).json({
        error: 'Invalid legend colors format',
        code: 'INVALID_LEGEND_COLORS'
      });
    }

    const legendColorsData = {
      nodeColors: nodeColors || {},
      relationshipColors: relationshipColors || {},
      lastModified: new Date().toISOString(),
      version: '1.0'
    };

    const upsertQuery = `
      INSERT INTO user_preferences (user_id, preference_key, preference_value, created_at, updated_at)
      VALUES ($1, 'legend_colors', $2, NOW(), NOW())
      ON CONFLICT (user_id, preference_key)
      DO UPDATE SET 
        preference_value = EXCLUDED.preference_value,
        updated_at = NOW()
      RETURNING preference_key, preference_value, created_at, updated_at
    `;

    const result = await query(upsertQuery, [userId, JSON.stringify(legendColorsData)]);
    const savedPreference = result.rows[0];

    res.json({
      userId,
      legendColors: savedPreference.preference_value,
      created_at: savedPreference.created_at,
      updated_at: savedPreference.updated_at,
      message: 'Legend colors saved successfully'
    });

  } catch (error) {
    console.error('Save legend colors error:', error);
    next(error);
  }
});

/**
 * GET /api/users/:userId/preferences/legend-colors
 * Specialized endpoint for getting legend colors with defaults
 */
router.get('/:userId/preferences/legend-colors', validateUserIdUUID, optionalAuth, async (req, res, next) => {
  try {
    const { userId } = req.params;
    
    // If user is authenticated, ensure they can only access their own preferences
    if (req.user && req.user.id !== userId) {
      return res.status(403).json({
        error: 'Access denied. Cannot access other user preferences.',
        code: 'FORBIDDEN'
      });
    }

    // Default green gradient colors matching frontend settings
    const defaultColors = {
      nodeColors: {
        'Module': '#0D4F2B',         // Darkest green - most common system modules
        'Product': '#1A5F39',        // Dark green - products
        'Workflow': '#267A47',       // Mid-dark green - workflows
        'UI_Area': '#339455',        // Mid green - UI areas
        'ConfigurationItem': '#2D8B47', // Mid-light green - configuration items (WCAG AA: 4.6:1)
        'TestCase': '#1F7A35',       // Light green - test cases (WCAG AA: 4.8:1)
        'Document': '#0F6B2A',       // Lighter green - documents (WCAG AA: 5.2:1)
        'Default': '#0D5A26',        // Accessible fallback green (WCAG AA: 5.5:1)
        // Legacy/additional types for backward compatibility
        'Entity': '#0D4F2B',         // Darkest green - entities (common)
        'Parameter': '#1A5F39',      // Dark green - parameters
        'Configuration': '#339455',  // Mid green - configuration
        'Feature': '#2D8B47',        // Mid-light green - features (WCAG AA)
        'Role': '#1F7A35'            // Light green - roles (WCAG AA)
      },
      relationshipColors: {
        'USES': '#0D4F2B',          // Darkest green - most common relationships
        'CONTAINS': '#1A5F39',      // Dark green - containment relationships
        'NAVIGATES_TO': '#267A47',  // Mid-dark green - navigation relationships
        'VALIDATES': '#339455',     // Mid green - validation relationships
        'REQUIRES': '#2D8B47',      // Mid-light green - dependency relationships (WCAG AA: 4.6:1)
        'CONFIGURES_IN': '#1F7A35', // Light green - configuration relationships (WCAG AA: 4.8:1)
        'DISPLAYS': '#0F6B2A',      // Lighter green - display relationships (WCAG AA: 5.2:1)
        'Default': '#0D5A26'        // Accessible fallback green (WCAG AA: 5.5:1)
      }
    };

    const preferenceQuery = `
      SELECT preference_value, created_at, updated_at
      FROM user_preferences 
      WHERE user_id = $1 AND preference_key = 'legend_colors'
    `;

    const result = await query(preferenceQuery, [userId]);

    if (result.rows.length === 0) {
      // Return default colors for new users
      res.json({
        userId,
        legendColors: defaultColors,
        isDefault: true,
        message: 'Using default green gradient colors'
      });
    } else {
      const userColors = result.rows[0];
      res.json({
        userId,
        legendColors: userColors.preference_value,
        isDefault: false,
        created_at: userColors.created_at,
        updated_at: userColors.updated_at
      });
    }

  } catch (error) {
    console.error('Get legend colors error:', error);
    next(error);
  }
});

module.exports = router;