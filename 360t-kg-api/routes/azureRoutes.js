const express = require('express');
const router = express.Router();
const axios = require('axios');

module.exports = function() {
  
  /**
   * POST /api/azure/test-azure-connection
   * Test Azure OpenAI connection with provided configuration
   */
  router.post('/test-azure-connection', async (req, res) => {
    try {
      const { endpoint, deploymentName, apiVersion } = req.body;
      
      // Validate required parameters
      if (!endpoint || !deploymentName || !apiVersion) {
        return res.status(400).json({
          success: false,
          message: 'Missing required parameters: endpoint, deploymentName, and apiVersion are required'
        });
      }
      
      // Validate API key from environment
      const apiKey = process.env.AZURE_OPENAI_API_KEY;
      if (!apiKey) {
        return res.status(400).json({
          success: false,
          message: 'Azure OpenAI API key not configured on server'
        });
      }
      
      // Clean up endpoint URL (remove trailing slash if present)
      const cleanEndpoint = endpoint.replace(/\/$/, '');
      
      // Construct Azure OpenAI API URL for chat completions (GPT-4 models)
      const testUrl = `${cleanEndpoint}/openai/deployments/${deploymentName}/chat/completions?api-version=${apiVersion}`;
      
      console.log(`Testing Azure OpenAI connection to: ${testUrl}`);
      
      // Prepare test request with minimal token usage for chat completions
      const testRequest = {
        messages: [
          {
            role: "user",
            content: "Hi"
          }
        ],
        max_tokens: 1,
        temperature: 0
      };
      
      // Make test request to Azure OpenAI
      const response = await axios.post(testUrl, testRequest, {
        headers: {
          'Content-Type': 'application/json',
          'api-key': apiKey
        },
        timeout: 10000 // 10 second timeout
      });
      
      // If we get here, the connection is successful
      console.log('Azure OpenAI connection test successful');
      return res.json({
        success: true,
        message: `Successfully connected to Azure OpenAI deployment '${deploymentName}'`,
        responseStatus: response.status,
        endpoint: cleanEndpoint,
        deployment: deploymentName,
        apiVersion: apiVersion
      });
      
    } catch (error) {
      console.error('Azure OpenAI connection test error:', error);
      
      // Handle different types of errors
      if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        return res.json({
          success: false,
          message: `Could not connect to endpoint. Please verify the endpoint URL: ${error.message}`
        });
      }
      
      if (error.code === 'ETIMEDOUT' || error.code === 'ECONNABORTED') {
        return res.json({
          success: false,
          message: 'Connection timeout. The endpoint might be slow to respond or unreachable.'
        });
      }
      
      // Handle HTTP response errors
      if (error.response) {
        const status = error.response.status;
        const statusText = error.response.statusText;
        
        // Handle authentication errors
        if (status === 401) {
          return res.json({
            success: false,
            message: 'Authentication failed. Please verify the API key is correct and has access to this deployment.'
          });
        }
        
        // Handle forbidden access
        if (status === 403) {
          return res.json({
            success: false,
            message: 'Access forbidden. The API key may not have permission to access this deployment.'
          });
        }
        
        // Handle not found errors (wrong deployment name or endpoint)
        if (status === 404) {
          return res.json({
            success: false,
            message: `Deployment '${req.body.deploymentName}' not found. Please verify the deployment name and endpoint are correct.`
          });
        }
        
        // Handle rate limiting
        if (status === 429) {
          return res.json({
            success: false,
            message: 'Rate limit exceeded. Please try again later.'
          });
        }
        
        // Handle bad request (wrong API version or malformed request)
        if (status === 400) {
          return res.json({
            success: false,
            message: `Bad request (${statusText}). Please verify the API version and deployment configuration.`
          });
        }
        
        // Generic HTTP error
        return res.json({
          success: false,
          message: `HTTP ${status} ${statusText}: ${error.response.data?.error?.message || 'Unknown error occurred'}`
        });
      }
      
      // Handle request setup errors
      if (error.request) {
        return res.json({
          success: false,
          message: 'No response received from the server. Please check the endpoint URL and network connectivity.'
        });
      }
      
      // Generic error
      return res.json({
        success: false,
        message: `Connection test failed: ${error.message}`
      });
    }
  });

  return router;
};