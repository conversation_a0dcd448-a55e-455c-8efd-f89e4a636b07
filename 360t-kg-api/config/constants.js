/**
 * Backend API Server Constants
 * Centralizes configuration values and API endpoints for the 360T Knowledge Graph API
 */

// Server configuration
const SERVER_CONFIG = {
  PORT: process.env.PORT || 3002,
  HOST: process.env.HOST || '0.0.0.0',
  NODE_ENV: process.env.NODE_ENV || 'development',
  CORS_ORIGIN: process.env.CORS_ORIGIN || 'http://localhost:5177'
};

// Service URLs and endpoints
const SERVICE_URLS = {
  // Proxy server (main API gateway)
  PROXY_SERVER: process.env.PROXY_SERVER_URL || 'http://localhost:3003',
  
  // Python AI service
  PYTHON_AI_SERVICE: process.env.PYTHON_AI_URL || 'http://localhost:8000',
  
  // Neo4j database
  NEO4J_URI: process.env.NEO4J_URI || 'bolt://localhost:7687',
  NEO4J_USER: process.env.NEO4J_USER || 'neo4j',
  NEO4J_PASSWORD: process.env.NEO4J_PASSWORD || 'password',
  
  // Ollama local LLM
  OLLAMA_URL: process.env.OLLAMA_URL || 'http://localhost:11434'
};

// Database configuration
const DB_CONFIG = {
  HOST: process.env.DB_HOST || 'localhost',
  PORT: process.env.DB_PORT || 5432,
  DATABASE: process.env.DB_NAME || '360t_kg',
  USER: process.env.DB_USER || 'postgres',
  PASSWORD: process.env.DB_PASSWORD || 'postgres',
  SSL: process.env.DB_SSL === 'true',
  MAX_CONNECTIONS: parseInt(process.env.DB_MAX_CONNECTIONS) || 20,
  IDLE_TIMEOUT_MS: parseInt(process.env.DB_IDLE_TIMEOUT_MS) || 30000,
  CONNECTION_TIMEOUT_MS: parseInt(process.env.DB_CONNECTION_TIMEOUT_MS) || 2000
};

// API endpoints  
const API_ENDPOINTS = {
  // Graph endpoints
  GRAPH_INITIAL: '/api/graph/initial',
  GRAPH_MINIMAL: '/api/graph/minimal',
  GRAPH_VISUALIZATION: '/api/graph/visualization',
  GRAPH_SEARCH: '/api/graph/search',
  GRAPH_EXPAND: '/api/graph/expand',
  GRAPH_FILTER: '/api/graph/filter',
  
  
  // Chat endpoints
  CHAT_MESSAGE: '/api/chat/message',
  CHAT_CONVERSATIONS: '/api/chat/conversations',
  
  // Settings endpoints
  SETTINGS: '/api/settings',
  SETTINGS_RESET: '/api/settings/reset',
  
  // Ollama endpoints
  OLLAMA_MODELS: '/api/ollama/models',
  OLLAMA_STATUS: '/api/ollama/status',
  
  // Metadata and health
  METADATA: '/api/metadata',
  HEALTH: '/api/health'
};

// Rate limiting configuration
const RATE_LIMITING = {
  WINDOW_MS: 15 * 60 * 1000, // 15 minutes
  MAX_REQUESTS: 100, // requests per window
  MESSAGE: 'Too many requests from this IP, please try again later.',
  STANDARD_HEADERS: true,
  LEGACY_HEADERS: false
};

// Cache configuration
const CACHE_CONFIG = {
  TTL: 60 * 5, // 5 minutes default TTL
  CHECK_PERIOD: 60 * 2, // 2 minutes cleanup interval
  MAX_KEYS: 1000, // maximum number of keys
  USE_CLONES: false // disable cloning for performance
};

// Timeout configurations (in milliseconds)
const TIMEOUTS = {
  API_REQUEST: 30000, // 30 seconds
  DATABASE_QUERY: 20000, // 20 seconds
  PYTHON_AI_REQUEST: 180000, // 3 minutes for AI processing
  NEO4J_QUERY: 30000, // 30 seconds for graph queries
  OLLAMA_REQUEST: 60000 // 1 minute for local LLM
};

// Logging configuration
const LOGGING = {
  LEVEL: process.env.LOG_LEVEL || 'info',
  FORMAT: process.env.LOG_FORMAT || 'combined',
  FILE: process.env.LOG_FILE || null,
  MAX_SIZE: process.env.LOG_MAX_SIZE || '10MB',
  MAX_FILES: process.env.LOG_MAX_FILES || '5'
};


module.exports = {
  SERVER_CONFIG,
  SERVICE_URLS,
  DB_CONFIG,
  API_ENDPOINTS,
  RATE_LIMITING,
  CACHE_CONFIG,
  TIMEOUTS,
  LOGGING
};