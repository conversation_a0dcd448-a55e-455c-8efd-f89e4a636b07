/**
 * Winston Logging Configuration for API Server
 * 
 * This module provides centralized logging configuration with file rotation,
 * console output for development, and structured JSON logging.
 * 
 * Features:
 * - Daily log rotation with size limits
 * - Separate error log file for critical issues
 * - Console output for development
 * - Structured logging with metadata
 * - Request/response logging middleware
 */

const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');
const fs = require('fs');

// Create centralized logs directory at project root
const getLogsDirectory = () => {
    // Navigate up from 360t-kg-api/config/ to project root
    const projectRoot = path.resolve(__dirname, '..', '..');
    const logsDir = path.join(projectRoot, 'logs');
    
    // Ensure logs directory exists with proper error handling for file system operations
    try {
        if (!fs.existsSync(logsDir)) {
            fs.mkdirSync(logsDir, { recursive: true });
        }
    } catch (error) {
        console.error('Failed to create logs directory:', error.message);
        // Fallback to local logs directory if centralized creation fails
        const fallbackLogsDir = path.join(__dirname, '..', 'logs');
        try {
            if (!fs.existsSync(fallbackLogsDir)) {
                fs.mkdirSync(fallbackLogsDir, { recursive: true });
            }
            return fallbackLogsDir;
        } catch (fallbackError) {
            console.error('Failed to create fallback logs directory:', fallbackError.message);
            throw new Error('Unable to create any logs directory');
        }
    }
    
    return logsDir;
};

// Custom format for structured logging with service information
const customFormat = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
    winston.format.errors({ stack: true }),
    winston.format.printf(({ level, message, timestamp, stack, service, ...meta }) => {
        // Build structured log entry with service context
        const logEntry = {
            timestamp,
            level: level.toUpperCase(),
            service: service || 'api-server',
            message,
            ...(Object.keys(meta).length > 0 && { meta }),
            ...(stack && { stack })
        };
        
        return JSON.stringify(logEntry);
    })
);

// Console format for development (more readable)
const consoleFormat = winston.format.combine(
    winston.format.colorize(),
    winston.format.timestamp({ format: 'HH:mm:ss.SSS' }),
    winston.format.printf(({ level, message, timestamp, service, ...meta }) => {
        const metaString = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
        return `${timestamp} [${service || 'api-server'}] ${level}: ${message}${metaString}`;
    })
);

// Create logger configuration
const createLogger = () => {
    const logsDir = getLogsDirectory();
    
    // File transport for all logs with daily rotation (50MB max, 10 files as per requirements)
    const fileTransport = new DailyRotateFile({
        filename: path.join(logsDir, 'api-server.log'),
        datePattern: 'YYYY-MM-DD',
        maxSize: '50m',
        maxFiles: '10d',
        format: customFormat,
        level: process.env.LOG_LEVEL || 'info'
    });
    
    // Centralized error log for critical errors only (as per requirements)
    const errorTransport = new DailyRotateFile({
        filename: path.join(logsDir, 'error.log'),
        datePattern: 'YYYY-MM-DD',
        maxSize: '50m',
        maxFiles: '10d',
        format: customFormat,
        level: 'error'
    });
    
    // Console transport for development with enhanced readability
    const consoleTransport = new winston.transports.Console({
        format: consoleFormat,
        level: process.env.LOG_LEVEL || 'info'
    });
    
    const logger = winston.createLogger({
        level: process.env.LOG_LEVEL || 'info',
        transports: [
            fileTransport,
            errorTransport,
            consoleTransport
        ],
        // Default metadata for all log entries
        defaultMeta: {
            service: 'api-server',
            version: '1.0.0',
            environment: process.env.NODE_ENV || 'development'
        },
        // Handle unhandled exceptions and rejections
        exceptionHandlers: [
            new winston.transports.File({ 
                filename: path.join(logsDir, 'exceptions.log'),
                format: customFormat
            })
        ],
        rejectionHandlers: [
            new winston.transports.File({ 
                filename: path.join(logsDir, 'rejections.log'),
                format: customFormat
            })
        ]
    });
    
    // Log successful logger initialization
    logger.info('Winston logging initialized successfully', {
        logsDirectory: logsDir,
        logLevel: logger.level,
        transportsCount: logger.transports.length
    });
    
    return logger;
};

// Express middleware for request/response logging
const createRequestLogger = (logger) => {
    return (req, res, next) => {
        const startTime = Date.now();
        
        // Generate unique request ID for correlation
        const requestId = require('uuid').v4().substr(0, 8);
        req.requestId = requestId;
        
        // Log incoming request with essential details
        logger.info('HTTP request received', {
            requestId,
            method: req.method,
            url: req.originalUrl,
            userAgent: req.get('User-Agent'),
            ip: req.ip || req.connection.remoteAddress,
            contentType: req.get('Content-Type'),
            contentLength: req.get('Content-Length')
        });
        
        // Override res.end to log response
        const originalEnd = res.end;
        res.end = function(chunk, encoding) {
            const duration = Date.now() - startTime;
            const statusCode = res.statusCode;
            
            // Determine log level based on status code
            const logLevel = statusCode >= 500 ? 'error' : statusCode >= 400 ? 'warn' : 'info';
            
            logger[logLevel]('HTTP request completed', {
                requestId,
                method: req.method,
                url: req.originalUrl,
                statusCode,
                duration: `${duration}ms`,
                contentLength: res.get('Content-Length'),
                slow: duration > 1000 // Flag slow requests over 1 second
            });
            
            originalEnd.call(this, chunk, encoding);
        };
        
        next();
    };
};

// Error logging middleware for Express
const createErrorLogger = (logger) => {
    return (err, req, res, next) => {
        // Log error with full context for debugging
        logger.error('Express error handler', {
            requestId: req.requestId,
            error: {
                name: err.name,
                message: err.message,
                stack: err.stack,
                status: err.status || err.statusCode
            },
            request: {
                method: req.method,
                url: req.originalUrl,
                headers: req.headers,
                body: req.body ? JSON.stringify(req.body).substring(0, 1000) : undefined
            }
        });
        
        next(err);
    };
};

// Initialize and export logger
const logger = createLogger();

module.exports = {
    logger,
    requestLogger: createRequestLogger(logger),
    errorLogger: createErrorLogger(logger)
};