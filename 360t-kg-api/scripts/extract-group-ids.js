const axios = require('axios');
const fs = require('fs');
const path = require('path');

/**
 * <PERSON><PERSON>t to extract all unique group_id values from the Neo4j database
 * and create a mapping template for product names, file paths, and URLs
 */

const API_URL = 'http://localhost:3002/api';

async function extractGroupIds() {
    try {
        console.log('🔍 Fetching unique group_id values from Neo4j database...');
        
        const response = await axios.get(`${API_URL}/graph/group-ids`);
        const { groupIds, count, timestamp } = response.data;
        
        console.log(`✅ Found ${count} unique group_id values:`);
        groupIds.forEach((groupId, index) => {
            console.log(`  ${index + 1}. ${groupId}`);
        });
        
        // Create mapping template
        const mappingTemplate = {
            metadata: {
                extractedAt: timestamp,
                totalGroupIds: count,
                description: "Mapping template for group_id values to product information"
            },
            mappings: {}
        };
        
        // Generate template structure for each group_id
        groupIds.forEach(groupId => {
            // Try to infer product name from group_id pattern
            let inferredProductName = groupId;
            let inferredSourceFile = '';
            let inferredCategory = 'Unknown';
            
            // Pattern analysis for common group_id formats
            if (groupId.includes('user_guides_')) {
                inferredCategory = 'User Guide';
                inferredProductName = groupId.replace('user_guides_', '').replace(/_/g, ' ');
                inferredSourceFile = `${groupId}.pdf`;
            } else if (groupId.includes('api_')) {
                inferredCategory = 'API Documentation';
                inferredProductName = groupId.replace('api_', '').replace(/_/g, ' ');
                inferredSourceFile = `${groupId}.md`;
            } else if (groupId.includes('manual_')) {
                inferredCategory = 'Manual';
                inferredProductName = groupId.replace('manual_', '').replace(/_/g, ' ');
                inferredSourceFile = `${groupId}.pdf`;
            } else if (groupId.includes('spec_')) {
                inferredCategory = 'Specification';
                inferredProductName = groupId.replace('spec_', '').replace(/_/g, ' ');
                inferredSourceFile = `${groupId}.docx`;
            }
            
            mappingTemplate.mappings[groupId] = {
                productName: inferredProductName,
                category: inferredCategory,
                sourceFilePath: inferredSourceFile,
                urlReference: `https://docs.company.com/${groupId}`,
                description: `Documentation for ${inferredProductName}`,
                lastUpdated: null,
                version: null,
                tags: [],
                // Additional metadata that can be filled in manually
                metadata: {
                    documentType: inferredCategory,
                    language: 'en',
                    format: inferredSourceFile.split('.').pop() || 'unknown',
                    size: null,
                    pageCount: null
                }
            };
        });
        
        // Save to JSON file
        const outputPath = path.join(__dirname, '../data/group-id-mappings.json');
        fs.writeFileSync(outputPath, JSON.stringify(mappingTemplate, null, 2));
        
        console.log(`\n📄 Mapping template saved to: ${outputPath}`);
        
        // Also create a TypeScript interface definition
        const tsInterface = `
/**
 * TypeScript interface for Group ID mappings
 * Generated on ${timestamp}
 */

export interface GroupIdMapping {
  productName: string;
  category: string;
  sourceFilePath: string;
  urlReference: string;
  description: string;
  lastUpdated: string | null;
  version: string | null;
  tags: string[];
  metadata: {
    documentType: string;
    language: string;
    format: string;
    size: number | null;
    pageCount: number | null;
  };
}

export interface GroupIdMappings {
  metadata: {
    extractedAt: string;
    totalGroupIds: number;
    description: string;
  };
  mappings: Record<string, GroupIdMapping>;
}

// Available group IDs in the system
export const AVAILABLE_GROUP_IDS = [
${groupIds.map(id => `  '${id}'`).join(',\n')}
] as const;

export type GroupIdType = typeof AVAILABLE_GROUP_IDS[number];
`;
        
        const tsPath = path.join(__dirname, '../types/group-id-mappings.ts');
        fs.writeFileSync(tsPath, tsInterface);
        
        console.log(`📝 TypeScript definitions saved to: ${tsPath}`);
        
        // Create a JavaScript configuration file for the frontend
        const jsConfig = `
/**
 * Group ID mappings configuration
 * Generated on ${timestamp}
 */

export const GROUP_ID_MAPPINGS = ${JSON.stringify(mappingTemplate.mappings, null, 2)};

export const GROUP_ID_METADATA = ${JSON.stringify(mappingTemplate.metadata, null, 2)};

export const AVAILABLE_GROUP_IDS = [
${groupIds.map(id => `  '${id}'`).join(',\n')}
];

/**
 * Get product name for a group_id
 */
export function getProductName(groupId) {
  return GROUP_ID_MAPPINGS[groupId]?.productName || groupId;
}

/**
 * Get source file path for a group_id
 */
export function getSourceFilePath(groupId) {
  return GROUP_ID_MAPPINGS[groupId]?.sourceFilePath || '';
}

/**
 * Get URL reference for a group_id
 */
export function getUrlReference(groupId) {
  return GROUP_ID_MAPPINGS[groupId]?.urlReference || '';
}

/**
 * Get category for a group_id
 */
export function getCategory(groupId) {
  return GROUP_ID_MAPPINGS[groupId]?.category || 'Unknown';
}
`;
        
        const jsPath = path.join(__dirname, '../../360t-kg-ui/src/config/group-id-mappings.js');
        fs.writeFileSync(jsPath, jsConfig);
        
        console.log(`⚙️  JavaScript config saved to: ${jsPath}`);
        
        // Print summary
        console.log('\n📊 Summary:');
        console.log(`   Total group IDs: ${count}`);
        console.log(`   Categories found:`);
        
        const categories = {};
        Object.values(mappingTemplate.mappings).forEach(mapping => {
            categories[mapping.category] = (categories[mapping.category] || 0) + 1;
        });
        
        Object.entries(categories).forEach(([category, count]) => {
            console.log(`     - ${category}: ${count}`);
        });
        
        console.log('\n✅ Group ID extraction and mapping template creation completed!');
        
        return mappingTemplate;
        
    } catch (error) {
        console.error('❌ Error extracting group IDs:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
        process.exit(1);
    }
}

// Run the script if called directly
if (require.main === module) {
    extractGroupIds();
}

module.exports = { extractGroupIds };
