[{"module": "RFS", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "SEP", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "OMT", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "MidMatch", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "HST Orderbook", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "HST Engine", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "GTX CLOB", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Open Bridge Administration", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "CRM tool", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Time Period Groups", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "EMS", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "<PERSON>t Log", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Bridge administration tool", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "user details", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "ExecutionMethod", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Risk Portfolio module", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Bridge Administration tool", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "active risk portfolio rules", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Risk Portfolio Rules Upload/Download", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "administration panel on HTML", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "administration panels", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "TWS", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Limits Monitor", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Order Book", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Global settings", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "General", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Cancelled Trades", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "module", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Order Management", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Add New Boards", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Supersonic Trader Resting Orders", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Trade Blotter", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "starter applet", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Gross Settlement methodology", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Auto Dealer Suite", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Auto Dealer Control for RFS, Orders and SEP Configuration", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "6.2.9 FIX Upload", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Trade Exporter Schnittstelle", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "TI / Trade Importer", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "compliance module", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Pentaho Ticket Report", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Data Disclosure Configuration},{", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Bridge Administration Tool", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "MTF", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Algo1", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Counterpart Relationship Management", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Bridge Administration", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Risk Portfolios", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Company SEF Details tab", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Company SEF Overview tab", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Trader SEF Overview tab", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Trader SEF Details tab", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Counterpart Relationships tool", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "view", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Resting Orders view", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "SUPERSONIC TRADER EXECUTION TYPES", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "360T Trader Worksheet", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Company Admin", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Bridge RFS", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "FX Option Plain Vanilla product definition", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Executed Deals - Net Position Summary", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "EMS administrator", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "360T Bridge", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "SuPersonic Trader Resting Orders", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "TRADER WORKSHEET", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Deal Tracking table", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "TRADER WORK SHEET", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Bullet Swap", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Energy Option Strategies", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "tenor netting view", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Risk Portfolio", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Information packages", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "ANNA / DSB ISIN Interface", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Change Request admin tool", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Deal Tracking tab", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Auto Dealing Suite Rules tool", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Bridge Administration Counterparty Relationship tool", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Activation period groups", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "RFS Algorithm Groups", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "margin rules", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "GUI", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "ADS RFS Rules", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Update Rates", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Overview", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Pricing Controller", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "PTMM", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Pricing Controller tab", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Pricing Monitor tab", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "BLOTTERS", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Currency Pair Position Blotter", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "individual requester blotter", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "hedge order blotter", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Client Activity Blotter", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "All orders blotter", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Negotiation", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Chapter 3.8", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "HTML ADS Audit Log", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "RFS Auto Dealer", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "Orders Auto Dealer", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}, {"module": "SEP Auto Dealer", "version": null, "workflowCount": 0, "uiAreaCount": 0, "dependencyCount": 0}]