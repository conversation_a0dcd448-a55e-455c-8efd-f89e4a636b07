{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://api.360t.com/schemas/format-c/v3.0", "title": "Format C - Hybrid Core + Extensions RAG Response", "description": "Standardized response format for both Graphiti and Atlas RAG systems with core consistency and system-specific extensions", "type": "object", "required": ["version", "format", "timestamp", "query", "core", "extensions"], "properties": {"version": {"type": "string", "const": "3.0", "description": "Format C schema version"}, "format": {"type": "string", "const": "C", "description": "Response format identifier"}, "timestamp": {"type": "string", "format": "date-time", "description": "Response generation timestamp in ISO 8601 format"}, "query": {"type": "object", "required": ["original", "processed"], "properties": {"original": {"type": "string", "description": "Original user query", "minLength": 1, "maxLength": 500}, "processed": {"type": "string", "description": "Processed/normalized query for search", "minLength": 1, "maxLength": 500}}}, "core": {"type": "object", "required": ["summary", "results", "source_count", "confidence_score", "processing_time_ms"], "properties": {"summary": {"type": "string", "description": "Core answer summary from RAG system", "minLength": 10, "maxLength": 1000}, "results": {"type": "array", "description": "Standardized result entries", "maxItems": 20, "items": {"type": "object", "required": ["content", "relevance_score", "source"], "properties": {"content": {"type": "string", "description": "Main content/passage text", "minLength": 10, "maxLength": 2000}, "relevance_score": {"type": "number", "description": "Relevance score 0-1", "minimum": 0, "maximum": 1}, "source": {"type": "object", "required": ["type", "title"], "properties": {"type": {"type": "string", "enum": ["document", "node", "edge", "passage"], "description": "Source type"}, "title": {"type": "string", "description": "Source title/name", "minLength": 1, "maxLength": 200}, "url": {"type": "string", "format": "uri", "description": "Optional source URL"}, "page": {"type": "integer", "description": "Optional page number", "minimum": 1}, "node_id": {"type": "string", "description": "Optional Neo4j node ID"}}}}}}, "source_count": {"type": "integer", "description": "Total number of sources found", "minimum": 0}, "confidence_score": {"type": "number", "description": "Overall confidence in response", "minimum": 0, "maximum": 1}, "processing_time_ms": {"type": "integer", "description": "Total processing time in milliseconds", "minimum": 0}, "error": {"type": "object", "description": "Error information if applicable", "properties": {"type": {"type": "string", "description": "Error type"}, "code": {"type": "string", "description": "Error code"}, "message": {"type": "string", "description": "Human-readable error message"}}}}}, "extensions": {"type": "object", "description": "System-specific extension data", "properties": {"atlas_rag": {"type": "object", "description": "Atlas RAG specific data", "properties": {"search_method": {"type": "string", "enum": ["semantic_similarity", "personalized_pagerank", "hybrid"]}, "embedding_model": {"type": "string", "description": "Embedding model used"}, "total_passages": {"type": "integer", "minimum": 0}, "search_time_ms": {"type": "integer", "minimum": 0}, "passage_metadata": {"type": "array", "items": {"type": "object", "properties": {"passage_id": {"type": "integer"}, "source_metadata": {"type": "object", "properties": {"document": {"type": "string"}, "vector_similarity": {"type": "number", "minimum": 0, "maximum": 1}}}}}}}}, "graphiti": {"type": "object", "description": "Graphiti specific data", "properties": {"version": {"type": "string", "description": "Graphiti version used"}, "search_algorithm": {"type": "string", "enum": ["rrf_fusion", "mmr_reranking", "hybrid_search", "node_search", "edge_search"]}, "rrf_score": {"type": "number", "minimum": 0, "maximum": 1}, "mmr_diversity": {"type": "number", "minimum": 0, "maximum": 1}, "semantic_similarity": {"type": "number", "minimum": 0, "maximum": 1}, "graph_traversal_depth": {"type": "integer", "minimum": 1}, "total_nodes_explored": {"type": "integer", "minimum": 0}, "graph_structure": {"type": "object", "properties": {"node_count": {"type": "integer", "minimum": 0}, "edge_count": {"type": "integer", "minimum": 0}, "node_types": {"type": "array", "items": {"type": "string"}}, "relationship_types": {"type": "array", "items": {"type": "string"}}}}, "nodes": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "properties": {"type": "object", "additionalProperties": true}}}}, "relationships": {"type": "array", "items": {"type": "object", "properties": {"source": {"type": "string"}, "target": {"type": "string"}, "type": {"type": "string"}, "properties": {"type": "object", "additionalProperties": true}}}}}}}}}, "additionalProperties": false}