{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://api.360t.com/schemas/chat-response/v2.0", "title": "Graphiti Chat Response v2.0", "description": "Enhanced structured response format for Graphiti-powered chat with entity nodes, PDF sources, badges, and follow-up questions", "type": "object", "required": ["version", "answer", "sections", "sources", "entities", "badges", "follow_up", "metadata"], "properties": {"version": {"type": "string", "enum": ["2.0"], "description": "Schema version for compatibility and evolution tracking"}, "answer": {"type": "string", "description": "Primary answer text from the LLM", "minLength": 1, "maxLength": 5000}, "sections": {"type": "array", "description": "Structured content sections for detailed explanations", "maxItems": 5, "items": {"type": "object", "required": ["title", "content", "entity_refs"], "properties": {"title": {"type": "string", "description": "Section title", "minLength": 1, "maxLength": 200}, "content": {"type": "string", "description": "Section content with markdown support", "minLength": 1, "maxLength": 2000}, "entity_refs": {"type": "array", "description": "References to entities mentioned in this section", "items": {"type": "string", "description": "Entity ID reference"}}}}}, "sources": {"type": "array", "description": "Source documents with PDF URLs and metadata", "maxItems": 20, "items": {"type": "object", "required": ["id", "title", "url", "document_type"], "properties": {"id": {"type": "string", "description": "Unique source identifier"}, "title": {"type": "string", "description": "Document title for display", "minLength": 1, "maxLength": 200}, "url": {"type": "string", "format": "uri", "description": "Direct URL to the source document"}, "document_type": {"type": "string", "enum": ["PDF", "DOC", "WEB", "OTHER"], "description": "Type of document for icon selection"}, "snippet": {"type": "string", "description": "Relevant excerpt from the document", "maxLength": 120}, "category": {"type": "string", "description": "Document category (EMS, SEP, MMC, etc.)", "maxLength": 50}}}}, "entities": {"type": "array", "description": "Knowledge graph entities ranked by relevance", "maxItems": 15, "items": {"type": "object", "required": ["id", "name", "relevance_score"], "properties": {"id": {"type": "string", "description": "Unique entity identifier from Neo4j"}, "name": {"type": "string", "description": "Entity display name", "minLength": 1, "maxLength": 200}, "description": {"type": "string", "description": "Entity description for tooltips", "maxLength": 500}, "category": {"type": "string", "description": "Entity category for grouping and styling", "maxLength": 50}, "relevance_score": {"type": "number", "description": "Relevance score from 0.0 to 1.0", "minimum": 0, "maximum": 1}, "properties": {"type": "object", "description": "Additional entity properties from Neo4j", "additionalProperties": true}}}}, "badges": {"type": "array", "description": "Category badges with tooltips and colors", "maxItems": 10, "items": {"type": "object", "required": ["label", "tooltip"], "properties": {"label": {"type": "string", "description": "Badge display text", "minLength": 1, "maxLength": 20}, "tooltip": {"type": "string", "description": "Tooltip text for badge hover", "minLength": 1, "maxLength": 200}, "color": {"type": "string", "pattern": "^#[0-9A-Fa-f]{6}$", "description": "Hex color code for badge styling"}, "count": {"type": "integer", "description": "Number of items in this category", "minimum": 1}}}}, "follow_up": {"type": "array", "description": "Suggested follow-up questions", "maxItems": 3, "items": {"type": "object", "required": ["question", "context_hint"], "properties": {"question": {"type": "string", "description": "Follow-up question text", "minLength": 5, "maxLength": 100}, "context_hint": {"type": "string", "description": "Brief context for the question", "minLength": 1, "maxLength": 50}}}}, "metadata": {"type": "object", "description": "Response metadata for monitoring and debugging", "properties": {"processing_time_ms": {"type": "integer", "description": "Total processing time in milliseconds", "minimum": 0}, "token_usage": {"type": "integer", "description": "Total tokens used in LLM generation", "minimum": 0}, "confidence_score": {"type": "number", "description": "Overall confidence in the response", "minimum": 0, "maximum": 1}, "fallback_used": {"type": "boolean", "description": "Whether fallback mechanisms were triggered"}, "search_type": {"type": "string", "description": "Graphiti search type used"}, "entity_count": {"type": "integer", "description": "Number of entities found", "minimum": 0}, "source_count": {"type": "integer", "description": "Number of source documents found", "minimum": 0}}}}, "additionalProperties": false}