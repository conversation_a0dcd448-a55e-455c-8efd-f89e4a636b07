const Ajv = require('ajv');
const addFormats = require('ajv-formats');
const fs = require('fs');
const path = require('path');

/**
 * Schema Validator for Graphiti Chat Response v2.0
 * Provides validation, error formatting, and schema evolution support
 */
class SchemaValidator {
  constructor() {
    this.ajv = new Ajv({ 
      allErrors: true, 
      verbose: true,
      strict: false // Allow additional properties for extensibility
    });
    addFormats(this.ajv);
    
    // Load schemas
    this.schemas = new Map();
    this.loadSchemas();
  }

  /**
   * Load all available schemas
   */
  loadSchemas() {
    const schemaDir = path.join(__dirname, '..', 'schemas');
    
    try {
      // Load v2.0 schema
      const v2Schema = JSON.parse(
        fs.readFileSync(path.join(schemaDir, 'chat-response-v2.json'), 'utf8')
      );
      this.ajv.addSchema(v2Schema, 'chat-response-v2');
      this.schemas.set('2.0', v2Schema);
      
      console.log('✅ Loaded chat response schema v2.0');
    } catch (error) {
      console.error('❌ Failed to load schemas:', error.message);
      throw new Error(`Schema loading failed: ${error.message}`);
    }
  }

  /**
   * Validate a response object against the specified schema version
   * @param {Object} response - Response object to validate
   * @param {string} version - Schema version (default: '2.0')
   * @returns {Object} Validation result with success flag and errors
   */
  validate(response, version = '2.0') {
    const startTime = Date.now();
    
    try {
      // Check if response has version field
      const responseVersion = response?.version || version;
      
      // Get the appropriate schema
      const schemaKey = `chat-response-v${responseVersion.replace('.', '')}`;
      let validate = this.ajv.getSchema(schemaKey);

      // Try alternative schema key format if first attempt fails
      if (!validate) {
        const altSchemaKey = `chat-response-v2`;
        validate = this.ajv.getSchema(altSchemaKey);
      }
      
      if (!validate) {
        return {
          success: false,
          errors: [{
            message: `Schema version ${responseVersion} not found`,
            path: 'version',
            severity: 'error'
          }],
          validationTime: Date.now() - startTime
        };
      }

      const isValid = validate(response);
      const validationTime = Date.now() - startTime;

      if (isValid) {
        return {
          success: true,
          errors: [],
          validationTime,
          metadata: {
            version: responseVersion,
            schemaId: this.schemas.get(responseVersion)?.$id
          }
        };
      }

      // Format validation errors
      const formattedErrors = this.formatErrors(validate.errors);
      
      return {
        success: false,
        errors: formattedErrors,
        validationTime,
        metadata: {
          version: responseVersion,
          schemaId: this.schemas.get(responseVersion)?.$id,
          totalErrors: formattedErrors.length
        }
      };

    } catch (error) {
      return {
        success: false,
        errors: [{
          message: `Validation error: ${error.message}`,
          path: 'root',
          severity: 'error'
        }],
        validationTime: Date.now() - startTime
      };
    }
  }

  /**
   * Format AJV validation errors into user-friendly messages
   * @param {Array} errors - AJV validation errors
   * @returns {Array} Formatted error objects
   */
  formatErrors(errors) {
    if (!errors || !Array.isArray(errors)) {
      return [];
    }

    return errors.map(error => {
      const path = error.instancePath || error.dataPath || 'root';
      let message = error.message;
      let severity = 'error';

      // Enhance error messages based on keyword
      switch (error.keyword) {
        case 'required':
          message = `Missing required field: ${error.params?.missingProperty}`;
          break;
        case 'type':
          message = `Expected ${error.params?.type} but got ${typeof error.data}`;
          break;
        case 'format':
          message = `Invalid ${error.params?.format} format`;
          break;
        case 'maxLength':
          message = `Text too long (max: ${error.params?.limit} characters)`;
          severity = 'warning';
          break;
        case 'minLength':
          message = `Text too short (min: ${error.params?.limit} characters)`;
          break;
        case 'maxItems':
          message = `Too many items (max: ${error.params?.limit})`;
          severity = 'warning';
          break;
        case 'enum':
          message = `Invalid value. Allowed: ${error.params?.allowedValues?.join(', ')}`;
          break;
        case 'pattern':
          message = `Invalid format pattern`;
          break;
        default:
          message = error.message;
      }

      return {
        path: path.replace(/^\//, '').replace(/\//g, '.') || 'root',
        message,
        severity,
        keyword: error.keyword,
        value: error.data,
        allowedValues: error.params?.allowedValues,
        limit: error.params?.limit
      };
    });
  }

  /**
   * Validate and repair common JSON issues
   * @param {Object} response - Response object to validate and repair
   * @param {string} version - Schema version
   * @returns {Object} Repaired response and validation result
   */
  validateAndRepair(response, version = '2.0') {
    // Create a deep copy for repair
    const repairedResponse = JSON.parse(JSON.stringify(response));
    const repairs = [];

    try {
      // Ensure version field exists
      if (!repairedResponse.version) {
        repairedResponse.version = version;
        repairs.push('Added missing version field');
      }

      // Ensure required arrays exist
      const requiredArrays = ['sections', 'sources', 'entities', 'badges', 'follow_up'];
      requiredArrays.forEach(field => {
        if (!Array.isArray(repairedResponse[field])) {
          repairedResponse[field] = [];
          repairs.push(`Initialized missing ${field} array`);
        }
      });

      // Ensure metadata object exists
      if (!repairedResponse.metadata || typeof repairedResponse.metadata !== 'object') {
        repairedResponse.metadata = {};
        repairs.push('Initialized missing metadata object');
      }

      // Truncate oversized text fields
      if (repairedResponse.answer && repairedResponse.answer.length > 5000) {
        repairedResponse.answer = repairedResponse.answer.substring(0, 4997) + '...';
        repairs.push('Truncated oversized answer text');
      }

      // Limit array sizes
      const arrayLimits = {
        sections: 5,
        sources: 20,
        entities: 15,
        badges: 10,
        follow_up: 3
      };

      Object.entries(arrayLimits).forEach(([field, limit]) => {
        if (repairedResponse[field] && repairedResponse[field].length > limit) {
          repairedResponse[field] = repairedResponse[field].slice(0, limit);
          repairs.push(`Truncated ${field} array to ${limit} items`);
        }
      });

      // Validate the repaired response
      const validationResult = this.validate(repairedResponse, version);

      return {
        response: repairedResponse,
        validation: validationResult,
        repairs,
        wasRepaired: repairs.length > 0
      };

    } catch (error) {
      return {
        response: repairedResponse,
        validation: {
          success: false,
          errors: [{
            message: `Repair failed: ${error.message}`,
            path: 'root',
            severity: 'error'
          }]
        },
        repairs,
        wasRepaired: false
      };
    }
  }

  /**
   * Get schema information for a specific version
   * @param {string} version - Schema version
   * @returns {Object} Schema metadata
   */
  getSchemaInfo(version = '2.0') {
    const schema = this.schemas.get(version);
    if (!schema) {
      return null;
    }

    return {
      version,
      id: schema.$id,
      title: schema.title,
      description: schema.description,
      requiredFields: schema.required || [],
      properties: Object.keys(schema.properties || {}),
      lastModified: new Date().toISOString() // Would be from file stats in production
    };
  }
}

// Create singleton instance
const validator = new SchemaValidator();

module.exports = {
  SchemaValidator,
  validator,
  
  // Convenience functions
  validate: (response, version) => validator.validate(response, version),
  validateAndRepair: (response, version) => validator.validateAndRepair(response, version),
  getSchemaInfo: (version) => validator.getSchemaInfo(version)
};
