/**
 * Format C Response Validator
 * Validates responses against Format C (Hybrid Core + Extensions) schema
 */

const fs = require('fs');
const path = require('path');

// Load JSON Schema
const schemaPath = path.join(__dirname, '../schemas/format-c-schema.json');
let formatCSchema;

try {
  formatCSchema = JSON.parse(fs.readFileSync(schemaPath, 'utf8'));
} catch (error) {
  console.error('Failed to load Format C schema:', error);
  formatCSchema = null;
}

/**
 * Validates a Format C response object
 * @param {Object} response - Response object to validate
 * @param {Object} options - Validation options
 * @returns {Object} Validation result with isValid, errors, warnings
 */
function validateFormatCResponse(response, options = {}) {
  const result = {
    isValid: true,
    errors: [],
    warnings: [],
    fieldCounts: {},
    metadata: {
      version: response?.version || 'unknown',
      format: response?.format || 'unknown',
      systemType: response?.extensions ? Object.keys(response.extensions)[0] || 'unknown' : 'unknown'
    }
  };

  // Basic structure validation
  if (!response || typeof response !== 'object') {
    result.isValid = false;
    result.errors.push('Response must be an object');
    return result;
  }

  // Required top-level fields
  const requiredFields = ['version', 'format', 'timestamp', 'query', 'core', 'extensions'];
  for (const field of requiredFields) {
    if (!(field in response)) {
      result.isValid = false;
      result.errors.push(`Missing required field: ${field}`);
    }
  }

  // Version validation
  if (response.version !== '3.0') {
    result.isValid = false;
    result.errors.push(`Invalid version: expected "3.0", got "${response.version}"`);
  }

  // Format validation
  if (response.format !== 'C') {
    result.isValid = false;
    result.errors.push(`Invalid format: expected "C", got "${response.format}"`);
  }

  // Query validation
  if (response.query) {
    if (!response.query.original || typeof response.query.original !== 'string') {
      result.isValid = false;
      result.errors.push('query.original must be a non-empty string');
    }
    if (!response.query.processed || typeof response.query.processed !== 'string') {
      result.isValid = false;
      result.errors.push('query.processed must be a non-empty string');
    }
  }

  // Core section validation
  if (response.core) {
    const core = response.core;
    
    // Required core fields
    const requiredCoreFields = ['summary', 'results', 'source_count', 'confidence_score', 'processing_time_ms'];
    for (const field of requiredCoreFields) {
      if (!(field in core)) {
        result.isValid = false;
        result.errors.push(`Missing required core field: ${field}`);
      }
    }

    // Summary validation
    if (typeof core.summary !== 'string' || core.summary.length < 10) {
      result.isValid = false;
      result.errors.push('core.summary must be a string with at least 10 characters');
    }

    // Results validation
    if (!Array.isArray(core.results)) {
      result.isValid = false;
      result.errors.push('core.results must be an array');
    } else {
      result.fieldCounts.results = core.results.length;
      
      core.results.forEach((result, index) => {
        if (!result.content || typeof result.content !== 'string') {
          result.isValid = false;
          result.errors.push(`Result ${index}: content must be a non-empty string`);
        }
        if (typeof result.relevance_score !== 'number' || result.relevance_score < 0 || result.relevance_score > 1) {
          result.isValid = false;
          result.errors.push(`Result ${index}: relevance_score must be a number between 0 and 1`);
        }
        if (!result.source || !result.source.type || !result.source.title) {
          result.isValid = false;
          result.errors.push(`Result ${index}: source must have type and title`);
        }
      });
    }

    // Confidence score validation
    if (typeof core.confidence_score !== 'number' || core.confidence_score < 0 || core.confidence_score > 1) {
      result.isValid = false;
      result.errors.push('core.confidence_score must be a number between 0 and 1');
    }

    // Processing time validation
    if (typeof core.processing_time_ms !== 'number' || core.processing_time_ms < 0) {
      result.isValid = false;
      result.errors.push('core.processing_time_ms must be a non-negative number');
    }

    // Source count validation
    if (typeof core.source_count !== 'number' || core.source_count < 0) {
      result.isValid = false;
      result.errors.push('core.source_count must be a non-negative number');
    }
  }

  // Extensions validation
  if (response.extensions && typeof response.extensions === 'object') {
    // Atlas RAG extension validation
    if (response.extensions.atlas_rag) {
      const atlas = response.extensions.atlas_rag;
      if (atlas.total_passages && typeof atlas.total_passages !== 'number') {
        result.warnings.push('atlas_rag.total_passages should be a number');
      }
      result.fieldCounts.atlas_passages = atlas.passage_metadata?.length || 0;
    }

    // Graphiti extension validation  
    if (response.extensions.graphiti) {
      const graphiti = response.extensions.graphiti;
      if (graphiti.version && typeof graphiti.version !== 'string') {
        result.warnings.push('graphiti.version should be a string');
      }
      if (graphiti.rrf_score && (typeof graphiti.rrf_score !== 'number' || graphiti.rrf_score < 0 || graphiti.rrf_score > 1)) {
        result.warnings.push('graphiti.rrf_score should be a number between 0 and 1');
      }
      result.fieldCounts.graphiti_nodes = graphiti.nodes?.length || 0;
      result.fieldCounts.graphiti_relationships = graphiti.relationships?.length || 0;
    }
  }

  return result;
}

/**
 * Quick validation check for Format C responses
 * @param {Object} response - Response to validate
 * @returns {boolean} True if valid Format C response
 */
function isValidFormatCResponse(response) {
  const result = validateFormatCResponse(response);
  return result.isValid;
}

/**
 * FOREX domain-specific validation checks
 * @param {Object} response - Format C response
 * @returns {boolean} True if contains FOREX-relevant content
 */
function hasForexContext(response) {
  if (!response?.core) return false;
  
  const content = JSON.stringify([
    response.core.summary,
    response.core.results?.map(r => r.content).join(' ') || ''
  ]).toLowerCase();
  
  const forexTerms = [
    'eur/usd', 'gbp/usd', 'usd/jpy', 'usd/chf',
    'currency', 'forex', 'fx', 'pip', 'spread',
    'central bank', 'monetary policy', 'interest rate',
    'settlement', 'forward', 'spot', 'swap'
  ];
  
  return forexTerms.some(term => content.includes(term));
}

/**
 * Development debug logging for validation results
 * @param {Object} validationResult - Result from validateFormatCResponse
 * @param {string} context - Context for logging
 */
function logValidationDebug(validationResult, context = 'Format C') {
  if (process.env.NODE_ENV !== 'development') return;
  
  if (!validationResult.isValid) {
    console.log(`🔍 ${context} Validation Debug:`, {
      isValid: validationResult.isValid,
      errorCount: validationResult.errors.length,
      warningCount: validationResult.warnings.length,
      fieldCounts: validationResult.fieldCounts,
      metadata: validationResult.metadata
    });
    
    if (validationResult.errors.length > 0) {
      console.warn(`⚠️ ${context} Validation Errors:`, validationResult.errors);
    }
  }
}

module.exports = {
  validateFormatCResponse,
  isValidFormatCResponse,
  hasForexContext,
  logValidationDebug,
  formatCSchema
};