const path = require('path');
const Ajv = require('ajv');
const addFormats = require('ajv-formats');

const schemaPath = path.resolve(__dirname, '../../config/search-config/dist/search-config.schema.json');
// eslint-disable-next-line import/no-dynamic-require, global-require
const schema = require(schemaPath);
// eslint-disable-next-line import/no-dynamic-require, global-require
const { DEFAULT_GRAPHITI_CONFIG, DEFAULT_ATLAS_RAG_CONFIG } = require('../../config/search-config/dist/index.js');

const clone = (value) => JSON.parse(JSON.stringify(value));

const copyIfPresent = (source, target, keys) => {
  keys.forEach((key) => {
    if (source && source[key] !== undefined) {
      target[key] = source[key];
    }
  });
  return target;
};

const ensureMode = (config) => {
  if (!config || typeof config !== 'object') {
    return clone(DEFAULT_GRAPHITI_CONFIG);
  }

  if (config.mode === 'atlasrag') {
    return {
      mode: 'atlasrag',
      llmProvider: config.llmProvider || 'ollama',
      ollamaUrl: config.ollamaUrl || 'http://localhost:11434',
      timeoutSeconds: config.timeoutSeconds ?? 180,
      atlasrag: {
        ...clone(DEFAULT_ATLAS_RAG_CONFIG.atlasrag),
        ...(config.atlasrag || {})
      },
      azureEndpoint: config.azureEndpoint,
      azureDeploymentName: config.azureDeploymentName,
      azureApiVersion: config.azureApiVersion,
      azureModel: config.azureModel
    };
  }

  return {
    mode: 'graphiti',
    llmProvider: config.llmProvider || 'ollama',
    ollamaUrl: config.ollamaUrl || 'http://localhost:11434',
    timeoutSeconds: config.timeoutSeconds ?? 180,
    graphiti: {
      ...clone(DEFAULT_GRAPHITI_CONFIG.graphiti),
      ...(config.graphiti || {})
    },
    azureEndpoint: config.azureEndpoint,
    azureDeploymentName: config.azureDeploymentName,
    azureApiVersion: config.azureApiVersion,
    azureModel: config.azureModel
  };
};


const ajv = new Ajv({
  allErrors: true,
  allowUnionTypes: true,
  strict: false
});
addFormats(ajv);
const validate = ajv.compile(schema);

const formatAjvErrors = (errors = []) => errors.map((err) => {
  const path = err.instancePath || err.schemaPath;
  return `${path} ${err.message}`.trim();
});

const validateBusinessRules = (config) => {
  const errors = [];
  if (config.mode === 'atlasrag' && config.llmProvider === 'azure-openai') {
    if (!config.azureEndpoint) errors.push('azureEndpoint is required when using Azure OpenAI');
    if (!config.azureDeploymentName) errors.push('azureDeploymentName is required when using Azure OpenAI');
    if (!config.azureApiVersion) errors.push('azureApiVersion is required when using Azure OpenAI');
  }

  if (config.mode === 'graphiti' && config.llmProvider === 'azure-openai') {
    if (!config.azureEndpoint) errors.push('azureEndpoint is required when using Azure OpenAI');
    if (!config.azureDeploymentName) errors.push('azureDeploymentName is required when using Azure OpenAI');
    if (!config.azureApiVersion) errors.push('azureApiVersion is required when using Azure OpenAI');
  }

  return errors;
};

const validateSearchConfig = (config) => {
  const normalized = ensureMode(config);
  const candidate = clone(normalized);

  const valid = validate(candidate);
  const errors = valid ? [] : formatAjvErrors(validate.errors);

  const businessErrors = validateBusinessRules(candidate);
  errors.push(...businessErrors);

  return {
    valid: errors.length === 0,
    errors,
    config: candidate
  };
};

module.exports = {
  validateSearchConfig,
  ensureMode
};
