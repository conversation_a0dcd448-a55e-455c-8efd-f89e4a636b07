/**
 * User Preferences Validation Utilities for 360T Knowledge Graph Visualizer
 * 
 * This module provides comprehensive validation for user preferences including
 * avatar selection, legend colors, and graph settings persistence.
 * 
 * @module preferenceValidation
 * @version 1.0.0
 * <AUTHOR> Knowledge Graph Visualizer Team
 */

const { validateAvatarSelection } = require('../constants/avatars');

/**
 * Validate hex color format
 * @param {string} color - Color string to validate
 * @returns {boolean} True if valid hex color
 */
const isValidHexColor = (color) => {
  if (!color || typeof color !== 'string') {
    return false;
  }
  
  // Match 3, 4, 6, or 8 digit hex colors with optional #
  const hexColorRegex = /^#?([A-Fa-f0-9]{3}|[A-Fa-f0-9]{4}|[A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$/;
  return hexColorRegex.test(color);
};

/**
 * Validate legend colors object
 * @param {Object} legendColors - Legend colors object to validate
 * @returns {Object} Validation result with valid flag and details
 */
const validateLegendColors = (legendColors) => {
  if (!legendColors) {
    return {
      valid: true,
      value: {},
      message: 'No legend colors provided, using defaults'
    };
  }

  if (typeof legendColors !== 'object' || Array.isArray(legendColors)) {
    return {
      valid: false,
      value: null,
      message: 'Legend colors must be an object'
    };
  }

  const validatedColors = {};
  const errors = [];

  // Validate each color in the legend colors object
  for (const [nodeType, color] of Object.entries(legendColors)) {
    if (typeof nodeType !== 'string' || nodeType.trim() === '') {
      errors.push(`Invalid node type: ${nodeType}`);
      continue;
    }

    if (!isValidHexColor(color)) {
      errors.push(`Invalid hex color for ${nodeType}: ${color}`);
      continue;
    }

    // Normalize hex color (ensure # prefix)
    const normalizedColor = color.startsWith('#') ? color : `#${color}`;
    validatedColors[nodeType.trim()] = normalizedColor;
  }

  if (errors.length > 0) {
    return {
      valid: false,
      value: null,
      message: `Invalid legend colors: ${errors.join(', ')}`
    };
  }

  return {
    valid: true,
    value: validatedColors,
    message: 'Valid legend colors'
  };
};

/**
 * Validate graph settings object
 * @param {Object} graphSettings - Graph settings object to validate
 * @returns {Object} Validation result with valid flag and details
 */
const validateGraphSettings = (graphSettings) => {
  if (!graphSettings) {
    return {
      valid: true,
      value: {},
      message: 'No graph settings provided, using defaults'
    };
  }

  if (typeof graphSettings !== 'object' || Array.isArray(graphSettings)) {
    return {
      valid: false,
      value: null,
      message: 'Graph settings must be an object'
    };
  }

  const validatedSettings = {};
  const errors = [];

  // Define allowed boolean settings
  const booleanSettings = [
    'showLabels',
    'is3DMode',
    'enablePhysics',
    'showLegend',
    'autoZoom',
    'enableFiltering'
  ];

  // Define allowed numeric settings with ranges
  const numericSettings = {
    'nodeSize': { min: 1, max: 100 },
    'linkDistance': { min: 10, max: 500 },
    'charge': { min: -1000, max: 0 },
    'friction': { min: 0, max: 1 },
    'alpha': { min: 0, max: 1 }
  };

  // Define allowed string settings
  const stringSettings = [
    'colorScheme',
    'layout',
    'theme'
  ];

  for (const [key, value] of Object.entries(graphSettings)) {
    if (booleanSettings.includes(key)) {
      if (typeof value !== 'boolean') {
        errors.push(`${key} must be a boolean, got ${typeof value}`);
        continue;
      }
      validatedSettings[key] = value;
    } else if (numericSettings[key]) {
      const { min, max } = numericSettings[key];
      if (typeof value !== 'number' || isNaN(value)) {
        errors.push(`${key} must be a number, got ${typeof value}`);
        continue;
      }
      if (value < min || value > max) {
        errors.push(`${key} must be between ${min} and ${max}, got ${value}`);
        continue;
      }
      validatedSettings[key] = value;
    } else if (stringSettings.includes(key)) {
      if (typeof value !== 'string' || value.trim() === '') {
        errors.push(`${key} must be a non-empty string, got ${typeof value}`);
        continue;
      }
      validatedSettings[key] = value.trim();
    } else {
      // Allow unknown settings but validate they're serializable
      try {
        JSON.stringify(value);
        validatedSettings[key] = value;
      } catch (e) {
        errors.push(`${key} contains non-serializable data`);
      }
    }
  }

  if (errors.length > 0) {
    return {
      valid: false,
      value: null,
      message: `Invalid graph settings: ${errors.join(', ')}`
    };
  }

  return {
    valid: true,
    value: validatedSettings,
    message: 'Valid graph settings'
  };
};

/**
 * Validate complete user preferences object (for new users or full updates)
 * @param {Object} preferences - Complete preferences object to validate
 * @returns {Object} Validation result with valid flag, normalized preferences, and details
 */
const validateUserPreferences = (preferences) => {
  if (!preferences) {
    return {
      valid: true,
      value: {
        selectedAvatar: 'user',
        legendColors: {},
        graphSettings: {}
      },
      message: 'No preferences provided, using defaults'
    };
  }

  if (typeof preferences !== 'object' || Array.isArray(preferences)) {
    return {
      valid: false,
      value: null,
      message: 'Preferences must be an object'
    };
  }

  const validatedPreferences = {};
  const validationResults = [];

  // Validate avatar selection
  const avatarValidation = validateAvatarSelection(preferences.selectedAvatar);
  if (!avatarValidation.valid) {
    return {
      valid: false,
      value: null,
      message: avatarValidation.message
    };
  }
  validatedPreferences.selectedAvatar = avatarValidation.value;
  validationResults.push(avatarValidation.message);

  // Validate legend colors
  const legendValidation = validateLegendColors(preferences.legendColors);
  if (!legendValidation.valid) {
    return {
      valid: false,
      value: null,
      message: legendValidation.message
    };
  }
  validatedPreferences.legendColors = legendValidation.value;
  validationResults.push(legendValidation.message);

  // Validate graph settings
  const graphValidation = validateGraphSettings(preferences.graphSettings);
  if (!graphValidation.valid) {
    return {
      valid: false,
      value: null,
      message: graphValidation.message
    };
  }
  validatedPreferences.graphSettings = graphValidation.value;
  validationResults.push(graphValidation.message);

  // Preserve any other valid preferences
  for (const [key, value] of Object.entries(preferences)) {
    if (!['selectedAvatar', 'legendColors', 'graphSettings'].includes(key)) {
      try {
        JSON.stringify(value);
        validatedPreferences[key] = value;
      } catch (e) {
        // Skip non-serializable values
        validationResults.push(`Skipped non-serializable preference: ${key}`);
      }
    }
  }

  return {
    valid: true,
    value: validatedPreferences,
    message: `Preferences validated successfully. ${validationResults.join('. ')}`
  };
};

/**
 * Validate partial user preferences object (for updates - only validates provided fields)
 * @param {Object} partialPreferences - Partial preferences object to validate
 * @returns {Object} Validation result with valid flag, normalized preferences, and details
 */
const validatePartialUserPreferences = (partialPreferences) => {
  if (!partialPreferences) {
    return {
      valid: true,
      value: {},
      message: 'No preferences provided for update'
    };
  }

  if (typeof partialPreferences !== 'object' || Array.isArray(partialPreferences)) {
    return {
      valid: false,
      value: null,
      message: 'Preferences must be an object'
    };
  }

  const validatedPreferences = {};
  const validationResults = [];

  // Validate avatar selection only if provided
  if (partialPreferences.hasOwnProperty('selectedAvatar')) {
    const avatarValidation = validateAvatarSelection(partialPreferences.selectedAvatar);
    if (!avatarValidation.valid) {
      return {
        valid: false,
        value: null,
        message: avatarValidation.message
      };
    }
    validatedPreferences.selectedAvatar = avatarValidation.value;
    validationResults.push(`Avatar: ${avatarValidation.message}`);
  }

  // Validate legend colors only if provided
  if (partialPreferences.hasOwnProperty('legendColors')) {
    const legendValidation = validateLegendColors(partialPreferences.legendColors);
    if (!legendValidation.valid) {
      return {
        valid: false,
        value: null,
        message: legendValidation.message
      };
    }
    validatedPreferences.legendColors = legendValidation.value;
    validationResults.push(`Legend colors: ${legendValidation.message}`);
  }

  // Validate graph settings only if provided
  if (partialPreferences.hasOwnProperty('graphSettings')) {
    const graphValidation = validateGraphSettings(partialPreferences.graphSettings);
    if (!graphValidation.valid) {
      return {
        valid: false,
        value: null,
        message: graphValidation.message
      };
    }
    validatedPreferences.graphSettings = graphValidation.value;
    validationResults.push(`Graph settings: ${graphValidation.message}`);
  }

  // Preserve any other valid preferences
  for (const [key, value] of Object.entries(partialPreferences)) {
    if (!['selectedAvatar', 'legendColors', 'graphSettings'].includes(key)) {
      try {
        JSON.stringify(value);
        validatedPreferences[key] = value;
        validationResults.push(`${key}: preserved`);
      } catch (e) {
        // Skip non-serializable values
        validationResults.push(`Skipped non-serializable preference: ${key}`);
      }
    }
  }

  return {
    valid: true,
    value: validatedPreferences,
    message: validationResults.length > 0 
      ? `Partial preferences validated: ${validationResults.join(', ')}`
      : 'No valid preferences to update'
  };
};

/**
 * Create default preferences object for new users
 * @returns {Object} Default preferences structure
 */
const createDefaultPreferences = () => {
  return {
    selectedAvatar: 'user',
    legendColors: {},
    graphSettings: {
      showLabels: true,
      is3DMode: false,
      enablePhysics: true,
      showLegend: true,
      autoZoom: false
    }
  };
};

/**
 * Merge user preferences with defaults
 * @param {Object} userPreferences - User's current preferences
 * @param {Object} newPreferences - New preferences to merge
 * @returns {Object} Merged preferences object
 */
const mergePreferences = (userPreferences = {}, newPreferences = {}) => {
  const defaults = createDefaultPreferences();
  
  return {
    ...defaults,
    ...userPreferences,
    ...newPreferences,
    // Deep merge nested objects
    legendColors: {
      ...defaults.legendColors,
      ...(userPreferences.legendColors || {}),
      ...(newPreferences.legendColors || {})
    },
    graphSettings: {
      ...defaults.graphSettings,
      ...(userPreferences.graphSettings || {}),
      ...(newPreferences.graphSettings || {})
    }
  };
};

/**
 * Validate settings object (for backward compatibility with existing endpoints)
 * @param {Object} settings - Settings object to validate
 * @returns {boolean} True if valid settings format
 */
const validateSettings = (settings) => {
  if (!settings || typeof settings !== 'object') {
    return false;
  }
  
  // Check for required sections
  const requiredSections = ['nodeColors', 'relationshipColors'];
  for (const section of requiredSections) {
    if (settings[section]) {
      if (typeof settings[section] !== 'object') {
        return false;
      }
      // Validate each color in the section
      for (const [key, color] of Object.entries(settings[section])) {
        if (!isValidHexColor(color)) {
          return false;
        }
      }
    }
  }
  
  return true;
};

module.exports = {
  isValidHexColor,
  validateLegendColors,
  validateGraphSettings,
  validateUserPreferences,
  validatePartialUserPreferences,
  createDefaultPreferences,
  mergePreferences,
  validateSettings
};