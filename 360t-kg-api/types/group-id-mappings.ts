
/**
 * TypeScript interface for Group ID mappings
 * Generated on 2025-07-08T20:51:13.653Z
 */

export interface GroupIdMapping {
  productName: string;
  category: string;
  sourceFilePath: string;
  urlReference: string;
  description: string;
  lastUpdated: string | null;
  version: string | null;
  tags: string[];
  metadata: {
    documentType: string;
    language: string;
    format: string;
    size: number | null;
    pageCount: number | null;
  };
}

export interface GroupIdMappings {
  metadata: {
    extractedAt: string;
    totalGroupIds: number;
    description: string;
  };
  mappings: Record<string, GroupIdMapping>;
}

// Available group IDs in the system
export const AVAILABLE_GROUP_IDS = [
  '',
  'debug_test_group',
  'diagnostic_group',
  'fixed_test_group',
  'regression_fix_test_group',
  'test_simplified',
  'user_guides_01C82B55_v1.0.0',
  'user_guides_0B4FB0EE_v1.0.0',
  'user_guides_31111D0E_v1.0.0',
  'user_guides_37E4534E_v1.0.0',
  'user_guides_A80C13FC_v1.0.0',
  'user_guides_AEVS_v1.0.0',
  'user_guides_B495D049_v1.0.0',
  'user_guides_BAIT_v20.21.10',
  'user_guides_BAMC_v20.21.10',
  'user_guides_BASP_v1.0.0',
  'user_guides_BAS_v20.21.10',
  'user_guides_BCRM_v1.0.0',
  'user_guides_BOBT_v1.0.0',
  'user_guides_BTB_v1.0.0',
  'user_guides_CRB_v20.21.10',
  'user_guides_DB_v20.21.10',
  'user_guides_E511EF4B_v1.0.0',
  'user_guides_ECM_v1.0.0',
  'user_guides_EM_v1.0.0',
  'user_guides_FFS_v1.0.0',
  'user_guides_HSG_v1.0.0',
  'user_guides_IB_v20.21.10',
  'user_guides_II_v1.0.0',
  'user_guides_LLP_v1.0.0',
  'user_guides_LM_v1.0.0',
  'user_guides_MMM_v1.0.0',
  'user_guides_PMB_v20.21.10',
  'user_guides_PPB_v20.21.10',
  'user_guides_RCC_v20.21.10',
  'user_guides_RMTB_v1.0.0',
  'user_guides_SO_v1.0.0',
  'user_guides_SR_v1.0.0',
  'user_guides_TMMB_v1.0.0',
  'user_guides_UEAD_v1.0.0',
  'user_guides_UHAD_v1.0.0',
  'user_guides_UMCH_v1.0.0'
] as const;

export type GroupIdType = typeof AVAILABLE_GROUP_IDS[number];
