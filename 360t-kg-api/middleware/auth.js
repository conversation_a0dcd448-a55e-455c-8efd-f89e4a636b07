// ============================================
// Authentication Middleware
// ============================================

const { verifyToken, extractTokenFromHeader } = require('../services/authService');
const { query } = require('../database/config');

/**
 * Middleware to authenticate JWT tokens
 * Requires a valid JWT token in the Authorization header
 * Attaches user object to req.user on success
 */
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      return res.status(401).json({
        error: 'Access denied. No token provided.',
        code: 'NO_TOKEN'
      });
    }

    // Verify the token
    const decoded = verifyToken(token);

    // Get the current user from database to ensure they still exist and are active
    const userQuery = `
      SELECT id, username, email, display_name, preferences, is_active, is_verified, created_at, updated_at
      FROM users 
      WHERE id = $1 AND is_active = true
    `;

    const result = await query(userQuery, [decoded.id]);

    if (result.rows.length === 0) {
      return res.status(401).json({
        error: 'Access denied. User not found or inactive.',
        code: 'USER_NOT_FOUND'
      });
    }

    // Attach user to request object
    req.user = result.rows[0];
    req.token = token;

    next();
  } catch (error) {
    console.error('Authentication middleware error:', error);
    
    return res.status(401).json({
      error: 'Access denied. Invalid token.',
      code: 'INVALID_TOKEN',
      message: error.message
    });
  }
};

/**
 * Optional authentication middleware
 * Attempts to authenticate but doesn't fail if no token is provided
 * Useful for routes that work both with and without authentication
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      // No token provided, continue without user
      req.user = null;
      return next();
    }

    // Verify the token
    const decoded = verifyToken(token);

    // Get the current user from database
    const userQuery = `
      SELECT id, username, email, display_name, preferences, is_active, is_verified, created_at, updated_at
      FROM users 
      WHERE id = $1 AND is_active = true
    `;

    const result = await query(userQuery, [decoded.id]);

    if (result.rows.length > 0) {
      req.user = result.rows[0];
      req.token = token;
    } else {
      req.user = null;
    }

    next();
  } catch (error) {
    console.error('Optional authentication middleware error:', error);
    // On error, continue without user
    req.user = null;
    next();
  }
};

/**
 * Middleware to check if user is verified
 * Must be used after authenticateToken
 */
const requireVerification = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      error: 'Authentication required.',
      code: 'AUTH_REQUIRED'
    });
  }

  if (!req.user.is_verified) {
    return res.status(403).json({
      error: 'Account verification required.',
      code: 'VERIFICATION_REQUIRED'
    });
  }

  next();
};

/**
 * Middleware to check if user is active
 * Must be used after authenticateToken
 */
const requireActiveUser = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      error: 'Authentication required.',
      code: 'AUTH_REQUIRED'
    });
  }

  if (!req.user.is_active) {
    return res.status(403).json({
      error: 'Account is inactive.',
      code: 'ACCOUNT_INACTIVE'
    });
  }

  next();
};

module.exports = {
  authenticateToken,
  optionalAuth,
  requireVerification,
  requireActiveUser
};