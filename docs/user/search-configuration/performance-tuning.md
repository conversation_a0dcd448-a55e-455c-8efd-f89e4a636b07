# Performance Tuning Guide

This guide provides strategies for optimizing search performance across both AtlasRAG and Graphiti search engines. Learn how to balance result quality with response speed for different use cases and system constraints.

## Performance Fundamentals

### Understanding Performance Trade-offs

**The Performance Triangle**:
- **Speed**: How quickly results are returned
- **Quality**: Relevance and accuracy of results
- **Scope**: Breadth and depth of information covered

**Key Principle**: You can optimize for any two of these characteristics, but optimizing all three simultaneously requires careful configuration balance.

### Performance Metrics to Monitor

**Response Time Metrics**:
- Query processing time (server-side)
- Network latency (client-server communication)
- Rendering time (client-side display)
- End-to-end user experience time

**Quality Metrics**:
- Result relevance scores
- User click-through rates
- Query refinement frequency
- User satisfaction ratings

**System Resource Metrics**:
- CPU utilization during queries
- Memory consumption
- Database connection usage
- Concurrent user capacity

## AtlasRAG Performance Optimization

### Speed Optimization Strategies

#### Quick Response Configuration
```json
{
  "topN": 8,
  "max_hops": 2,
  "damping_factor": 0.9,
  "temperature": 0.5,
  "max_tokens": 800,
  "detail_level": "concise",
  "show_graph_traversal": false,
  "show_pagerank_scores": false,
  "show_context": false
}
```

**Impact**: 60-80% faster responses with focused, authoritative results.

**Use Cases**:
- Dashboard widgets
- Real-time operational queries
- Mobile applications
- High-volume production systems

#### Parameter Impact on Speed

**Most Impact (Exponential)**:
- `max_hops`: Each additional hop dramatically increases processing time
- `topN`: Linear relationship with result processing time

**Moderate Impact (Linear)**:
- `max_tokens`: Affects response generation time
- Debug features: Add 10-20% overhead when enabled

**Minimal Impact**:
- `temperature`: Negligible effect on processing speed
- `damping_factor`: Minor impact on PageRank calculation

### Quality Optimization Strategies

#### Comprehensive Analysis Configuration
```json
{
  "topN": 25,
  "max_hops": 4,
  "damping_factor": 0.75,
  "temperature": 0.8,
  "max_tokens": 2500,
  "detail_level": "detailed",
  "response_mode": "detailed",
  "show_graph_traversal": true
}
```

**Trade-off**: 2-3x slower processing for significantly enhanced result quality and transparency.

**Use Cases**:
- Strategic analysis projects
- Incident investigation and root cause analysis
- Training and educational scenarios
- Research and development activities

### Balanced Configuration Strategies

#### Production-Ready Balance
```json
{
  "topN": 15,
  "max_hops": 3,
  "damping_factor": 0.85,
  "temperature": 0.7,
  "max_tokens": 1500,
  "detail_level": "normal",
  "show_graph_traversal": false
}
```

**Results**: Good performance with comprehensive results suitable for most business applications.

## Graphiti Performance Optimization

### Algorithm Selection for Performance

#### Performance Ranking (Fastest to Slowest)
1. **COMBINED_HYBRID_SEARCH_RRF**: Fastest, most predictable performance
2. **COMBINED_HYBRID_SEARCH_MMR**: Moderate speed, good for balanced needs
3. **COMBINED_HYBRID_SEARCH_CROSS_ENCODER**: Slowest, highest quality

#### Speed-Optimized Graphiti Configuration
```json
{
  "searchType": "COMBINED_HYBRID_SEARCH_RRF",
  "edgeCount": 4,
  "nodeCount": 3,
  "temperature": 0.5,
  "timeout": 30,
  "categoryFilters": ["SPECIFIC_CATEGORY"]
}
```

**Performance Impact**:
- RRF algorithm: 40-60% faster than cross-encoder
- Category filtering: 30-50% speed improvement
- Reduced node/edge counts: Linear performance gains

### Category Filtering Performance Benefits

**Single Category vs. No Filtering**:
- Speed improvement: 30-50%
- Resource reduction: 40-60% less memory usage
- Result relevance: Often significantly higher

**Optimal Category Selection Strategies**:
- Use 1-3 categories for best performance
- Combine logically related categories
- Avoid overly broad category combinations

#### Category Performance Impact Matrix

| Category Count | Speed Impact | Quality Impact | Use Case |
|---------------|--------------|----------------|----------|
| 1 category | +50% faster | High precision | Focused queries |
| 2-3 categories | +30% faster | Balanced precision/recall | Standard operations |
| 4-6 categories | +15% faster | High recall | Comprehensive analysis |
| No filtering | Baseline | Maximum recall | Exploratory research |

## System-Wide Performance Strategies

### Infrastructure Optimization

#### Database Performance
- **Neo4j Memory Configuration**: Allocate sufficient heap and page cache
- **Index Optimization**: Ensure proper indexing on frequently queried properties
- **Connection Pooling**: Configure appropriate connection limits
- **Query Optimization**: Use EXPLAIN and PROFILE for complex queries

#### Application Server Tuning
- **Node.js Configuration**: Optimize event loop and memory limits
- **Python FastAPI**: Configure worker processes and async handling
- **Caching Strategies**: Implement query result caching where appropriate
- **Load Balancing**: Distribute queries across multiple instances

### Network and Client Optimization

#### Network Performance
- **Compression**: Enable gzip compression for API responses
- **CDN Usage**: Cache static assets and common query results
- **Connection Optimization**: Use HTTP/2 and connection keep-alive
- **Latency Reduction**: Deploy services closer to users

#### Client-Side Optimization
- **Response Caching**: Cache frequently accessed results
- **Progressive Loading**: Show initial results while fetching details
- **Debouncing**: Prevent excessive API calls during user input
- **Lazy Loading**: Load detailed information on demand

## Use Case-Specific Optimization

### High-Volume Production Systems

#### Configuration Strategy
- Prioritize speed and resource efficiency
- Use aggressive caching strategies
- Implement circuit breakers for reliability
- Monitor performance continuously

**Recommended Settings**:
```json
{
  "mode": "graphiti",
  "searchType": "COMBINED_HYBRID_SEARCH_RRF",
  "nodeCount": 3,
  "edgeCount": 4,
  "timeout": 30,
  "categoryFilters": ["RELEVANT_CATEGORY"]
}
```

### Research and Analysis Workloads

#### Configuration Strategy
- Prioritize result quality and comprehensiveness
- Accept longer processing times
- Enable debug features for transparency
- Use comprehensive parameter settings

**Recommended Settings**:
```json
{
  "mode": "atlasrag",
  "topN": 20,
  "max_hops": 4,
  "damping_factor": 0.75,
  "detail_level": "detailed",
  "show_graph_traversal": true
}
```

### Interactive Dashboard Applications

#### Configuration Strategy
- Balance speed with sufficient information
- Use progressive disclosure patterns
- Implement smart caching
- Optimize for user experience

**Recommended Settings**:
```json
{
  "mode": "graphiti",
  "searchType": "COMBINED_HYBRID_SEARCH_MMR",
  "nodeCount": 4,
  "edgeCount": 6,
  "diversityFactor": 0.6,
  "timeout": 45
}
```

## Performance Monitoring and Alerting

### Key Performance Indicators

#### Response Time Targets
- **Interactive Queries**: < 5 seconds
- **Standard Analysis**: < 15 seconds
- **Complex Research**: < 60 seconds
- **Batch Processing**: < 300 seconds

#### System Health Metrics
- **Average Response Time**: Track trends over time
- **95th Percentile Response Time**: Monitor worst-case performance
- **Error Rate**: Queries failing due to timeouts or errors
- **Throughput**: Queries per minute/hour handled successfully

### Monitoring Implementation

#### Application Performance Monitoring
```javascript
// Example performance tracking
const startTime = Date.now();
const result = await searchAPI.query(params);
const duration = Date.now() - startTime;

// Log performance metrics
logger.info('Search Performance', {
  duration,
  mode: params.mode,
  nodeCount: params.nodeCount,
  categoryCount: params.categoryFilters?.length || 0,
  resultCount: result.length
});
```

#### Alerting Thresholds
- **Response Time Alert**: > 30 seconds for 5 consecutive queries
- **Error Rate Alert**: > 5% of queries failing
- **Resource Alert**: > 80% CPU or memory utilization
- **User Experience Alert**: > 15 seconds average response time

## Performance Testing and Optimization

### Load Testing Strategies

#### Test Scenarios
1. **Peak Load Testing**: Maximum expected concurrent users
2. **Stress Testing**: Beyond normal capacity to find breaking points
3. **Endurance Testing**: Extended periods at normal load
4. **Spike Testing**: Sudden load increases

#### Performance Test Configuration
```json
{
  "scenarios": [
    {
      "name": "quick_lookups",
      "weight": 60,
      "config": {"mode": "graphiti", "nodeCount": 3}
    },
    {
      "name": "detailed_analysis",
      "weight": 30,
      "config": {"mode": "atlasrag", "topN": 15}
    },
    {
      "name": "complex_research",
      "weight": 10,
      "config": {"mode": "atlasrag", "max_hops": 4}
    }
  ]
}
```

### Optimization Workflow

#### Continuous Improvement Process
1. **Baseline Measurement**: Establish current performance metrics
2. **Configuration Testing**: Test parameter variations systematically
3. **A/B Testing**: Compare configurations with real user traffic
4. **Performance Analysis**: Identify bottlenecks and optimization opportunities
5. **Implementation**: Deploy optimized configurations gradually
6. **Monitoring**: Continuously track performance improvements

#### Optimization Checklist
- [ ] Database indexes optimized for query patterns
- [ ] Category filtering implemented where appropriate
- [ ] Result limits set based on use case requirements
- [ ] Caching strategies implemented for frequent queries
- [ ] Timeout values balanced for user experience
- [ ] Debug features disabled in production
- [ ] Network optimization implemented (compression, CDN)
- [ ] Client-side optimization implemented (caching, progressive loading)
- [ ] Performance monitoring and alerting configured
- [ ] Load testing completed for expected traffic levels

## Troubleshooting Performance Issues

### Common Performance Problems

#### Slow Query Response
**Symptoms**: Queries consistently taking longer than expected

**Diagnosis Steps**:
1. Check server resource utilization (CPU, memory, disk I/O)
2. Review database query execution plans
3. Analyze network latency between services
4. Examine configuration parameters for optimization opportunities

**Solutions**:
- Reduce `topN`, `nodeCount`, or `max_hops` parameters
- Enable category filtering to limit search scope
- Switch to faster algorithm (RRF instead of cross-encoder)
- Optimize database queries and indexes
- Scale infrastructure resources

#### Memory Issues
**Symptoms**: Out of memory errors, system instability

**Diagnosis Steps**:
1. Monitor memory usage patterns during queries
2. Identify memory-intensive configuration combinations
3. Check for memory leaks in long-running processes
4. Analyze garbage collection patterns

**Solutions**:
- Reduce result set sizes and processing depth
- Implement result pagination
- Optimize data structures and caching
- Scale memory resources
- Implement query queuing to limit concurrent processing

#### High Error Rates
**Symptoms**: Frequent query timeouts or failures

**Diagnosis Steps**:
1. Analyze error patterns and frequency
2. Check system logs for error details
3. Monitor resource constraints during failures
4. Test configuration parameters systematically

**Solutions**:
- Adjust timeout values based on realistic processing times
- Implement retry logic with exponential backoff
- Optimize query parameters to reduce processing complexity
- Implement circuit breakers to handle system overload
- Scale infrastructure to handle load

## Related Documentation

- [AtlasRAG Configuration](./atlasrag-configuration.md) - Detailed AtlasRAG parameters
- [Graphiti Configuration](./graphiti-configuration.md) - Complete Graphiti settings
- [Troubleshooting](./troubleshooting.md) - Common issues and solutions
- [Search Configuration Overview](./README.md) - General configuration guidance