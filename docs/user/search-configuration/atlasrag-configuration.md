# AtlasRAG Configuration Guide

AtlasRAG provides advanced semantic search capabilities powered by HippoRAG integration with personalized PageRank algorithms. This guide explains each configuration parameter and how to optimize them for your use cases.

## Core Search Parameters

### Top N Results (`topN`)
**Range**: 5-50 | **Default**: 10 | **Impact**: Result quantity vs. processing speed

Controls the number of top-ranked results returned from the graph traversal.

- **5-10**: Fast responses, focused results for specific queries
- **10-20**: Balanced approach for most use cases
- **20-50**: Comprehensive results for exploratory research

**Business Impact**: Higher values provide more comprehensive context but increase processing time and may introduce less relevant information.

**Example Use Cases**:
- Incident analysis: 15-20 (need comprehensive context)
- Quick status checks: 5-10 (want fast, focused answers)
- Strategic planning: 20-30 (need broad perspective)

### Damping Factor (`damping_factor`)
**Range**: 0.1-0.9 | **Default**: 0.85 | **Impact**: Result diversity vs. authority focus

Controls how PageRank algorithm balances between following relationships and random exploration.

- **0.1-0.4**: High exploration, diverse but potentially scattered results
- **0.5-0.7**: Balanced exploration and authority weighting
- **0.8-0.9**: Strong authority focus, results cluster around highly-connected nodes

**Business Impact**: Lower values find unexpected connections across business domains. Higher values focus on core, well-established relationships.

**When to Adjust**:
- **Lower (0.6)**: Discovering cross-functional impacts, innovation opportunities
- **Higher (0.9)**: Focusing on established processes, core system dependencies

### Maximum Hops (`max_hops`)
**Range**: 1-5 | **Default**: 3 | **Impact**: Relationship discovery vs. computational complexity

Determines how far the algorithm traverses relationships from your query starting points.

- **1 hop**: Direct relationships only (immediate dependencies)
- **2 hops**: Friends-of-friends (one degree of separation)
- **3 hops**: Extended network (recommended for most queries)
- **4-5 hops**: Deep exploration (use sparingly, can timeout)

**Business Impact**: More hops reveal indirect relationships and cascading effects but increase processing time exponentially.

**Strategic Applications**:
- Risk assessment: 3-4 hops (understand cascading impacts)
- Direct dependency analysis: 1-2 hops (immediate relationships)
- Market analysis: 2-3 hops (competitive and partner relationships)

## Model Configuration

### Use Ollama (`use_ollama`)
**Options**: true/false | **Default**: false | **Impact**: Local vs. cloud processing

Determines whether to use local Ollama models or cloud-based LLM providers.

- **True**: Local processing, data privacy, potentially slower responses
- **False**: Cloud processing, faster responses, requires network connectivity

**Decision Factors**:
- Data sensitivity requirements
- Network reliability and latency
- Processing power of local infrastructure
- Cost considerations for cloud API usage

### Ollama Model (`ollama_model`)
**Options**: Model name string | **Default**: "llama2" | **Impact**: Response quality vs. speed

Specifies which local language model to use when `use_ollama` is true.

**Popular Options**:
- **llama2**: Good balance of speed and quality
- **codellama**: Optimized for technical/code-related queries
- **mistral**: Strong performance on business analysis
- **neural-chat**: Conversational and explanatory responses

### Temperature (`temperature`)
**Range**: 0.0-2.0 | **Default**: 0.7 | **Impact**: Response creativity vs. consistency

Controls response generation creativity and variability.

- **0.0-0.3**: Highly consistent, factual responses
- **0.4-0.8**: Balanced creativity and factual accuracy (recommended)
- **0.9-2.0**: Creative responses, higher variability

**Business Applications**:
- **Low (0.2)**: Compliance checks, regulatory queries
- **Medium (0.7)**: General business questions, analysis
- **High (1.0)**: Brainstorming, strategic planning

### Maximum Tokens (`max_tokens`)
**Range**: 100-4000 | **Default**: 1500 | **Impact**: Response length vs. processing time

Sets the maximum length of generated responses.

**Guidelines**:
- **500-1000**: Concise answers for quick reference
- **1000-2000**: Standard business responses with context
- **2000-4000**: Detailed analysis and comprehensive explanations

## Response Customization

### Detail Level (`detail_level`)
**Options**: concise | normal | detailed | **Default**: normal

Controls the depth of information included in responses.

- **Concise**: Key points only, minimal context
- **Normal**: Balanced detail with relevant context
- **Detailed**: Comprehensive explanations with extensive context

**When to Use Each**:
- **Concise**: Executive summaries, quick status updates
- **Normal**: Regular business queries, team communications
- **Detailed**: Training materials, comprehensive analysis

### Response Mode (`response_mode`)
**Options**: concise | balanced | detailed | **Default**: balanced

Determines the overall response structure and formatting.

- **Concise**: Brief, direct answers
- **Balanced**: Structured responses with supporting information
- **Detailed**: Comprehensive responses with multiple perspectives

### System Prompt Template (`system_prompt_template`)
**Options**: minimal | balanced | detailed | **Default**: balanced

Controls the system instructions that guide AI response generation.

- **Minimal**: Basic instructions, allows more AI creativity
- **Balanced**: Standard business communication guidelines
- **Detailed**: Extensive instructions for consistent professional responses

## Debug and Transparency Features

### Show Graph Traversal (`show_graph_traversal`)
**Options**: true/false | **Default**: false | **Impact**: Response transparency vs. readability

When enabled, displays the actual path the algorithm took through your knowledge graph.

**Benefits**:
- Understand how the AI reached its conclusions
- Verify that relevant relationships were considered
- Identify gaps in knowledge graph connections
- Improve query formulation for better results

**Use Cases**:
- Troubleshooting unexpected results
- Training team members on graph structure
- Validating knowledge graph completeness

### Show PageRank Scores (`show_pagerank_scores`)
**Options**: true/false | **Default**: false | **Impact**: Technical insight vs. user-friendliness

Displays the numerical importance scores assigned to each node by the PageRank algorithm.

**Business Value**:
- Identify the most influential components in your domain
- Understand relative importance of different business entities
- Validate that important systems have appropriate graph prominence
- Guide prioritization of resources and attention

### Show Context (`show_context`)
**Options**: true/false | **Default**: false | **Impact**: Response detail vs. conciseness

Includes the raw context information retrieved from the knowledge graph before AI processing.

**Applications**:
- Quality assurance for AI responses
- Understanding information sources
- Identifying missing or incomplete data
- Training and education purposes

### Show Embeddings (`show_embeddings`)
**Options**: true/false | **Default**: false | **Impact**: Technical transparency vs. simplicity

Displays vector embedding information used for semantic similarity calculations.

**Technical Use Cases**:
- Fine-tuning embedding models
- Understanding semantic relationships
- Debugging vector similarity issues
- Research and development activities

## Performance Optimization Strategies

### For Speed-Critical Applications
```
topN: 8
max_hops: 2
temperature: 0.5
max_tokens: 800
detail_level: concise
Show debug features: false
```

### For Comprehensive Analysis
```
topN: 25
max_hops: 4
damping_factor: 0.75
temperature: 0.8
max_tokens: 2500
detail_level: detailed
Show graph_traversal: true
```

### For Balanced General Use
```
topN: 15
max_hops: 3
damping_factor: 0.85
temperature: 0.7
max_tokens: 1500
detail_level: normal
response_mode: balanced
```

## Troubleshooting Common Issues

### Slow Response Times
1. Reduce `topN` to 10 or lower
2. Limit `max_hops` to 2-3
3. Decrease `max_tokens` to 1000
4. Set `detail_level` to "concise"
5. Disable debug features

### Irrelevant Results
1. Increase `damping_factor` to focus on authoritative nodes
2. Reduce `max_hops` to focus on direct relationships
3. Lower `temperature` for more consistent responses
4. Verify query terminology matches graph data

### Incomplete Coverage
1. Increase `max_hops` to explore further relationships
2. Lower `damping_factor` for more exploration
3. Increase `topN` to capture more results
4. Enable `show_graph_traversal` to verify coverage

## Integration Examples

### API Configuration
```json
{
  "mode": "atlasrag",
  "atlasrag": {
    "topN": 15,
    "damping_factor": 0.85,
    "max_hops": 3,
    "use_ollama": false,
    "temperature": 0.7,
    "detail_level": "normal",
    "show_graph_traversal": false
  }
}
```

### Programmatic Optimization
```javascript
// Adjust configuration based on query type
if (queryType === 'incident_analysis') {
  config.atlasrag.topN = 20;
  config.atlasrag.max_hops = 4;
  config.atlasrag.show_graph_traversal = true;
} else if (queryType === 'quick_lookup') {
  config.atlasrag.topN = 8;
  config.atlasrag.max_hops = 2;
  config.atlasrag.detail_level = 'concise';
}
```

## Advanced Configuration Patterns

### Multi-Stage Query Optimization
1. **Discovery Phase**: High exploration (low damping, high hops)
2. **Focus Phase**: Targeted search (high damping, medium hops)
3. **Detail Phase**: Comprehensive analysis (medium settings, debug enabled)

### Context-Aware Configuration
Automatically adjust parameters based on:
- User role and permissions
- Query complexity and type
- System performance metrics
- Business criticality of the domain

### Performance Monitoring
Track these metrics to optimize configuration:
- Average response time per parameter combination
- User satisfaction ratings by configuration
- Query success rate (relevant results)
- System resource utilization

## Related Documentation

- [Graphiti Configuration](./graphiti-configuration.md) - Alternative search engine
- [Performance Tuning](./performance-tuning.md) - System-wide optimization
- [Troubleshooting](./troubleshooting.md) - Common issues and solutions