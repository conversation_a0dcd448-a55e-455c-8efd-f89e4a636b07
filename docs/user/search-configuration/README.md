# Search Configuration Guide

The Knowledge Graph Visualizer provides two powerful search engines with extensive configuration options. This guide helps you understand and optimize search parameters for your specific use cases.

## Overview

The system supports two search modes:

- **AtlasRAG**: Advanced semantic search powered by HippoRAG integration with personalized PageRank
- **Graphiti**: Hybrid search engine with multiple algorithm options and category filtering

Each mode offers distinct advantages and can be fine-tuned through comprehensive parameter settings.

## Accessing Search Configuration

1. Navigate to **Settings** in the main navigation menu
2. Select the **Search Configuration** tab
3. Choose your preferred search mode (AtlasRAG or Graphiti)
4. Adjust parameters using the interactive controls
5. Changes are automatically saved and applied to subsequent queries

## Search Mode Comparison

| Feature | AtlasRAG | Graphiti |
|---------|----------|----------|
| **Search Type** | Semantic + Personalized PageRank | Hybrid (Vector + Keyword + Cross-encoder) |
| **Category Filtering** | No | Yes (31 business categories) |
| **Graph Traversal** | Multi-hop with damping | Fixed edge/node counts |
| **Response Detail** | Configurable (concise/normal/detailed) | Fixed format |
| **Debug Information** | Graph traversal, PageRank scores | Processing metadata |
| **Best For** | Complex semantic queries, relationship discovery | Precise category-based searches, known entities |

## Quick Start Configurations

### For General Exploration
- **Mode**: AtlasRAG
- **Top Results**: 10-15
- **Detail Level**: Normal
- **Graph Traversal**: Enabled (for transparency)

### for Category-Specific Searches
- **Mode**: Graphiti
- **Search Algorithm**: COMBINED_HYBRID_SEARCH_CROSS_ENCODER
- **Category Filters**: Select relevant business areas
- **Edge Count**: 6-8

### For Performance-Critical Applications
- **Mode**: Graphiti
- **Search Algorithm**: COMBINED_HYBRID_SEARCH_RRF
- **Node Count**: 3-5
- **Timeout**: 30 seconds

## Detailed Configuration Guides

- [AtlasRAG Configuration](./atlasrag-configuration.md) - Comprehensive guide to AtlasRAG parameters
- [Graphiti Configuration](./graphiti-configuration.md) - Complete Graphiti settings reference
- [Performance Tuning](./performance-tuning.md) - Optimization strategies for different use cases
- [Troubleshooting](./troubleshooting.md) - Common configuration issues and solutions

## Configuration Best Practices

### 1. Start Simple
Begin with default settings and gradually adjust parameters based on your search results quality and performance needs.

### 2. Match Configuration to Use Case
- **Exploratory Research**: Use AtlasRAG with higher top N values and detailed responses
- **Targeted Queries**: Use Graphiti with category filters and moderate result counts
- **Performance-Critical**: Reduce result counts and enable timeouts

### 3. Monitor Performance
- Use metadata display to track response times
- Adjust parameters if queries consistently timeout
- Balance result quality with response speed

### 4. Leverage Category Filtering
When using Graphiti mode, category filtering significantly improves result relevance:
- Select 2-4 relevant categories rather than all or none
- Combine related categories (e.g., EMS + TRADING for trading system queries)
- Use WORKFLOW + UI for user experience related searches

### 5. Test Configuration Changes
- Try the same query with different configurations
- Compare confidence scores and result relevance
- Document successful parameter combinations for future use

## Configuration Persistence

All search configuration settings are:
- **Automatically saved** when you make changes
- **Persistent across sessions** using local browser storage
- **Mode-specific** - AtlasRAG and Graphiti maintain separate configurations
- **Exportable** for sharing configurations across team members

## Advanced Topics

### Provider Configuration
The system supports multiple LLM providers:
- **Ollama**: Local model deployment
- **Azure OpenAI**: Enterprise cloud deployment
- **OpenAI**: Direct API integration
- **Google**: Google Cloud AI integration

Provider selection affects:
- Response generation quality
- Processing speed and costs
- Available model options
- Security and compliance requirements

### Integration with External Systems
Search configurations can be:
- Exported as JSON for programmatic access
- Integrated with CI/CD pipelines for testing
- Synchronized across development/staging/production environments
- Customized per user role or department

## Getting Help

If you need assistance with search configuration:

1. **Built-in Help**: Click the (?) icon next to any parameter for contextual help
2. **Performance Dashboard**: Monitor search metrics in the Settings panel
3. **Documentation**: Refer to the detailed configuration guides linked above
4. **Support**: Contact the knowledge graph team for optimization assistance

## Related Documentation

- [User Guide](../user-guide/README.md) - General system usage
- [API Reference](../api-reference.md) - Programmatic configuration access
- [Data Model Guide](../data-model.md) - Understanding graph structure for better search tuning