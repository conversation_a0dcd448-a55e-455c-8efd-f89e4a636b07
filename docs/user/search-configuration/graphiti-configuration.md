# Graphiti Configuration Guide

Graphiti provides hybrid search capabilities combining vector similarity, keyword matching, and cross-encoder ranking with powerful category filtering. This guide explains how to optimize Graphiti parameters for precise, category-aware searches.

## Search Algorithm Selection

### Search Type (`searchType`)
**Options**: COMBINED_HYBRID_SEARCH_CROSS_ENCODER | COMBINED_HYBRID_SEARCH_MMR | COMBINED_HYBRID_SEARCH_RRF

The core algorithm that determines how different search signals are combined and ranked.

#### COMBINED_HYBRID_SEARCH_CROSS_ENCODER
**Best for**: Highest accuracy, semantic understanding
**Trade-offs**: Slower processing, higher computational cost

This algorithm uses a sophisticated cross-encoder model to directly compare query-document pairs, providing the most accurate relevance scoring.

**Business Applications**:
- **Critical Decision Making**: When accuracy is more important than speed
- **Complex Technical Queries**: Understanding nuanced relationships
- **Regulatory Compliance**: Ensuring complete and accurate information retrieval
- **Strategic Analysis**: When comprehensive understanding is essential

**When to Use**:
- Queries involving complex technical concepts
- Multi-step business process analysis
- Risk assessment requiring high accuracy
- Research and investigation tasks

#### COMBINED_HYBRID_SEARCH_MMR (Maximal Marginal Relevance)
**Best for**: Diverse results, avoiding redundancy
**Trade-offs**: Balanced speed and quality, good diversity

MMR optimizes for both relevance and diversity, ensuring results cover different aspects of your query rather than returning similar items.

**Business Applications**:
- **Market Research**: Getting diverse perspectives on business topics
- **Competitive Analysis**: Ensuring coverage of different competitive factors
- **Product Planning**: Understanding various feature and requirement dimensions
- **Stakeholder Analysis**: Capturing different viewpoints and interests

**Diversity Factor Impact**:
- Higher diversity (0.8-1.0): More varied results, broader coverage
- Lower diversity (0.3-0.5): More focused, similar results

#### COMBINED_HYBRID_SEARCH_RRF (Reciprocal Rank Fusion)
**Best for**: Speed, consistent performance
**Trade-offs**: Good balance of speed and quality

RRF combines multiple ranking signals using reciprocal rank fusion, providing reliable results with consistent performance characteristics.

**Business Applications**:
- **Operational Queries**: Day-to-day information lookup
- **Dashboard Integration**: Real-time search in business applications
- **High-Volume Usage**: When many users perform concurrent searches
- **Performance-Critical Systems**: When response time SLAs are important

**Strengths**:
- Predictable performance characteristics
- Lower computational requirements
- Good baseline accuracy for most queries
- Suitable for production environments

## Result Quantity Parameters

### Edge Count (`edgeCount`)
**Range**: 1-20 | **Default**: 6 | **Impact**: Relationship breadth vs. focus

Controls how many relationship connections are included for each result node.

**Business Impact**: More edges reveal broader context and indirect relationships but can overwhelm users with information.

**Optimization Guidelines**:
- **2-4 edges**: Quick overviews, simple relationship mapping
- **6-8 edges**: Standard business analysis, balanced context
- **10-15 edges**: Comprehensive relationship analysis
- **16-20 edges**: Deep investigation, research purposes

**Use Case Examples**:
- **Incident Response** (8-12): Need to understand all potential impact relationships
- **Quick Status Check** (3-5): Want focused, essential relationships only
- **Strategic Planning** (10-15): Need comprehensive understanding of connections

### Node Count (`nodeCount`)
**Range**: 1-15 | **Default**: 5 | **Impact**: Result scope vs. processing speed

Determines how many primary result nodes are returned for each search.

**Strategic Applications**:
- **Focused Analysis** (3-5): Specific, targeted results
- **Comparative Analysis** (6-10): Multiple options or alternatives
- **Comprehensive Research** (10-15): Broad exploration of the domain

**Performance Considerations**:
- Each additional node significantly increases processing time
- Higher node counts work better with category filtering
- Consider user interface capacity for displaying results

## Quality and Diversity Controls

### Diversity Factor (`diversityFactor`)
**Range**: 0.0-1.0 | **Default**: 0.7 | **Impact**: Result variety vs. focused relevance

*Only applies to MMR search algorithm*

Controls the balance between result relevance and diversity in MMR algorithm.

**Configuration Impact**:
- **0.0-0.3**: Highly relevant but potentially redundant results
- **0.4-0.7**: Balanced relevance and diversity (recommended)
- **0.8-1.0**: Maximum diversity, potentially sacrificing relevance

**Business Context**:
- **Risk Analysis** (0.3-0.5): Want comprehensive coverage of similar risk factors
- **Innovation Research** (0.7-0.9): Need diverse perspectives and approaches
- **Compliance Checking** (0.2-0.4): Want thorough coverage of related requirements

### Temperature (`temperature`)
**Range**: 0.0-2.0 | **Default**: 0.7 | **Impact**: Response creativity vs. consistency

Controls AI response generation variability and creativity.

**Business Applications**:
- **Audit and Compliance** (0.1-0.3): Consistent, factual responses
- **Strategy and Planning** (0.6-0.8): Balanced analytical responses
- **Innovation and Brainstorming** (0.8-1.2): Creative, varied perspectives

## Category Filtering System

### Category Filters (`categoryFilters`)
**Options**: Array of business categories or null for no filtering

Graphiti's most powerful feature: restrict searches to specific business domains using 31 predefined categories.

#### Available Business Categories

**Core Business Functions**:
- **EMS** (Electronic Market System): Core trading infrastructure
- **TRADING**: Trading operations and algorithms
- **RISK**: Risk management and controls
- **COMPLIANCE**: Regulatory and compliance systems
- **WORKFLOW**: Business process management

**System Categories**:
- **SYSTEM**: Core system infrastructure
- **UI**: User interface components
- **API**: Application programming interfaces
- **DATABASE**: Data storage and management
- **SECURITY**: Security and authentication

**Specialized Areas**:
- **SEP** (Systematic Electronic Platform): Electronic trading platform
- **ECN** (Electronic Communication Network): Network infrastructure
- **BA** (Business Analysis): Analysis and reporting
- **MMC** (Market Making Components): Market making functionality

#### Category Filtering Strategies

**Single Category Focus**:
```json
{"categoryFilters": ["TRADING"]}
```
Use when you need deep expertise in one specific area.

**Related Category Combinations**:
```json
{"categoryFilters": ["TRADING", "RISK"]}
```
Common productive combinations:
- TRADING + RISK: Trading system risk analysis
- UI + WORKFLOW: User experience optimization
- SYSTEM + SECURITY: Infrastructure security
- EMS + COMPLIANCE: Regulatory compliance for trading

**Broad Domain Coverage**:
```json
{"categoryFilters": ["EMS", "TRADING", "RISK", "WORKFLOW"]}
```
Use for comprehensive business process analysis.

**No Filtering** (Search All Categories):
```json
{"categoryFilters": null}
```
Default mode for general exploration.

#### Category Selection Best Practices

1. **Start Specific**: Begin with 1-2 most relevant categories
2. **Expand Gradually**: Add related categories if initial results are too narrow
3. **Business Logic**: Combine categories that naturally work together in business processes
4. **User Context**: Consider the user's role and typical information needs
5. **Performance**: Fewer categories generally provide faster, more focused results

## Model Configuration

### Ollama Model (`ollamaModel`)
**Options**: Model name string | **Default**: "llama2" | **Impact**: Response quality vs. speed

Local language model selection when using Ollama provider.

**Model Recommendations**:
- **llama2**: General business queries, good balance
- **mistral**: Strong analytical and reasoning capabilities
- **codellama**: Technical documentation and system queries
- **neural-chat**: Conversational responses, training scenarios

### Graphiti Model (`graphitiModel`)
**Options**: Model identifier | **Default**: "default" | **Impact**: Search backend behavior

Specifies the underlying Graphiti search model configuration.

**Considerations**:
- Model affects vector similarity calculations
- Different models may be optimized for different data types
- Consult with system administrators for available options

## Performance and Timeout Settings

### Timeout (`timeout`)
**Range**: 5-120 seconds | **Default**: 60 | **Impact**: Reliability vs. speed

Maximum time allowed for search query completion.

**Environment-Based Recommendations**:
- **Development**: 30-45 seconds (faster feedback)
- **Production**: 60-90 seconds (reliability priority)
- **Interactive Demo**: 15-30 seconds (user experience priority)

**Timeout Strategy by Query Type**:
- **Simple Lookups**: 15-30 seconds
- **Standard Analysis**: 30-60 seconds
- **Complex Research**: 60-120 seconds

## Configuration Optimization Patterns

### High-Performance Configuration
```json
{
  "searchType": "COMBINED_HYBRID_SEARCH_RRF",
  "edgeCount": 4,
  "nodeCount": 3,
  "temperature": 0.5,
  "timeout": 30,
  "categoryFilters": ["SPECIFIC_CATEGORY"]
}
```

### Comprehensive Analysis Configuration
```json
{
  "searchType": "COMBINED_HYBRID_SEARCH_CROSS_ENCODER",
  "edgeCount": 12,
  "nodeCount": 8,
  "diversityFactor": 0.6,
  "temperature": 0.7,
  "timeout": 90,
  "categoryFilters": ["RELATED", "CATEGORIES"]
}
```

### Balanced General-Purpose Configuration
```json
{
  "searchType": "COMBINED_HYBRID_SEARCH_MMR",
  "edgeCount": 6,
  "nodeCount": 5,
  "diversityFactor": 0.7,
  "temperature": 0.7,
  "timeout": 60,
  "categoryFilters": null
}
```

## Business Use Case Configurations

### Incident Response and Troubleshooting
**Priority**: Speed and comprehensive relationship mapping
```json
{
  "searchType": "COMBINED_HYBRID_SEARCH_RRF",
  "edgeCount": 10,
  "nodeCount": 6,
  "temperature": 0.3,
  "timeout": 45,
  "categoryFilters": ["SYSTEM", "EMS", "TRADING"]
}
```

### Strategic Business Analysis
**Priority**: Accuracy and comprehensive insights
```json
{
  "searchType": "COMBINED_HYBRID_SEARCH_CROSS_ENCODER",
  "edgeCount": 12,
  "nodeCount": 8,
  "diversityFactor": 0.8,
  "temperature": 0.8,
  "timeout": 90,
  "categoryFilters": ["TRADING", "RISK", "COMPLIANCE"]
}
```

### Daily Operations and Quick Lookups
**Priority**: Speed and focused results
```json
{
  "searchType": "COMBINED_HYBRID_SEARCH_MMR",
  "edgeCount": 4,
  "nodeCount": 3,
  "diversityFactor": 0.5,
  "temperature": 0.5,
  "timeout": 30,
  "categoryFilters": ["UI", "WORKFLOW"]
}
```

### Regulatory and Compliance Analysis
**Priority**: Accuracy and completeness
```json
{
  "searchType": "COMBINED_HYBRID_SEARCH_CROSS_ENCODER",
  "edgeCount": 8,
  "nodeCount": 6,
  "temperature": 0.2,
  "timeout": 75,
  "categoryFilters": ["COMPLIANCE", "RISK", "TRADING"]
}
```

## Category-Specific Optimization

### Trading System Analysis
**Categories**: TRADING, EMS, RISK
**Algorithm**: Cross-encoder for accuracy
**Edge Count**: 8-12 (comprehensive relationship mapping)

### User Experience Optimization
**Categories**: UI, WORKFLOW, BA
**Algorithm**: MMR for diverse perspectives
**Diversity Factor**: 0.7-0.8

### Infrastructure and Security
**Categories**: SYSTEM, SECURITY, API
**Algorithm**: RRF for performance
**Node Count**: 5-7 (balanced coverage)

### Market Analysis and Research
**Categories**: EMS, SEP, ECN, MMC
**Algorithm**: Cross-encoder for nuanced understanding
**Temperature**: 0.7-0.9 (analytical flexibility)

## Performance Monitoring and Optimization

### Key Performance Indicators
- **Response Time**: Target under configured timeout
- **Result Relevance**: User feedback and click-through rates
- **Category Effectiveness**: Results distribution across categories
- **Resource Utilization**: Server CPU and memory usage

### Optimization Strategies
1. **Category Filtering**: Most effective performance improvement
2. **Result Limits**: Balance information needs with speed
3. **Algorithm Selection**: Match complexity to use case requirements
4. **Timeout Management**: Set realistic expectations for complex queries

### A/B Testing Configurations
Test different parameter combinations with real user queries:
- Compare algorithm performance on typical queries
- Measure user satisfaction with different result quantities
- Evaluate category filtering effectiveness
- Test timeout values against user patience

## Troubleshooting Common Issues

### Poor Result Relevance
1. **Verify Category Selection**: Ensure categories match query domain
2. **Try Different Algorithm**: Cross-encoder for complex queries
3. **Adjust Temperature**: Lower for more focused results
4. **Increase Node Count**: May need broader result set

### Slow Performance
1. **Enable Category Filtering**: Most effective optimization
2. **Reduce Node/Edge Counts**: Decrease result complexity
3. **Switch to RRF Algorithm**: Faster processing
4. **Decrease Timeout**: Set realistic expectations

### Missing Expected Results
1. **Remove Category Filters**: Check if filtering too aggressively
2. **Increase Node Count**: May need more results
3. **Try MMR Algorithm**: Better coverage with diversity
4. **Verify Graph Data**: Ensure expected content exists

## Integration and API Usage

### Programmatic Configuration
```javascript
const graphitiConfig = {
  mode: 'graphiti',
  graphiti: {
    searchType: 'COMBINED_HYBRID_SEARCH_CROSS_ENCODER',
    edgeCount: 8,
    nodeCount: 5,
    diversityFactor: 0.7,
    temperature: 0.7,
    timeout: 60,
    ollamaModel: 'llama2',
    graphitiModel: 'default',
    categoryFilters: ['TRADING', 'RISK']
  }
};
```

### Dynamic Configuration Adjustment
```javascript
// Adjust based on query complexity
if (queryComplexity === 'high') {
  config.graphiti.searchType = 'COMBINED_HYBRID_SEARCH_CROSS_ENCODER';
  config.graphiti.timeout = 90;
  config.graphiti.edgeCount = 10;
}

// Optimize for performance
if (performanceMode) {
  config.graphiti.searchType = 'COMBINED_HYBRID_SEARCH_RRF';
  config.graphiti.nodeCount = Math.min(config.graphiti.nodeCount, 5);
  config.graphiti.timeout = 30;
}
```

## Advanced Configuration Techniques

### Context-Aware Category Selection
Automatically select categories based on:
- User role and permissions
- Query keywords and intent
- Historical successful configurations
- Business domain context

### Adaptive Parameter Tuning
Dynamically adjust parameters based on:
- Real-time performance metrics
- User feedback and interaction patterns
- System load and resource availability
- Query success rates

### Multi-Stage Search Strategies
1. **Fast Initial Search**: RRF algorithm, limited results
2. **Detailed Follow-up**: Cross-encoder for selected results
3. **Comprehensive Analysis**: Full parameter search if needed

## Related Documentation

- [AtlasRAG Configuration](./atlasrag-configuration.md) - Alternative search engine
- [Performance Tuning](./performance-tuning.md) - System-wide optimization
- [Troubleshooting](./troubleshooting.md) - Common issues and solutions
- [Category Management](./category-management.md) - Business category definitions and usage