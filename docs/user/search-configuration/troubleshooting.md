# Search Configuration Troubleshooting Guide

This guide helps you diagnose and resolve common issues with search configuration in both AtlasRAG and Graphiti modes. Each problem includes symptoms, diagnosis steps, and proven solutions.

## Quick Diagnostic Checklist

Before diving into specific issues, run through this quick checklist:

- [ ] Check if the issue occurs in both search modes or just one
- [ ] Verify search mode is set correctly (AtlasRAG vs. Graphiti)
- [ ] Enable metadata display to see confidence scores and processing times
- [ ] Test with default configuration to isolate custom parameter issues
- [ ] Check browser console for JavaScript errors
- [ ] Verify network connectivity and API availability

## Common Search Quality Issues

### Issue: No Search Results Returned

**Symptoms**:
- Search queries return empty result sets
- "No results found" messages appear frequently
- Previously working queries stop returning results

**Diagnosis Steps**:
1. **Test with Simple Queries**: Try basic queries like "trading" or "system"
2. **Check Category Filters**: Verify category selection isn't too restrictive
3. **Review Query Spelling**: Check for typos in search terms
4. **Test Different Search Modes**: Compare AtlasRAG vs. Graphiti results
5. **Enable Debug Information**: Look for error messages in metadata

**Solutions by Search Mode**:

**AtlasRAG**:
```json
{
  "topN": 20,           // Increase to get more results
  "max_hops": 3,        // Increase to explore further
  "damping_factor": 0.7 // Lower for more exploration
}
```

**Graphiti**:
```json
{
  "nodeCount": 10,         // Increase result quantity
  "categoryFilters": null, // Remove category restrictions
  "searchType": "COMBINED_HYBRID_SEARCH_CROSS_ENCODER" // Most comprehensive
}
```

**General Solutions**:
- Clear category filters and search all categories
- Verify graph database contains expected data
- Check for network connectivity issues
- Restart services if configuration changes aren't applying

### Issue: Irrelevant or Low-Quality Results

**Symptoms**:
- Search results don't match the query intent
- Results are tangentially related but not useful
- Low confidence scores in metadata display

**Diagnosis Steps**:
1. **Check Confidence Scores**: Enable metadata to see AI confidence levels
2. **Review Related Data**: Use the related pill to see what information was considered
3. **Test Query Variations**: Try rephrasing queries with different terms
4. **Compare Search Modes**: See if one mode performs better for your query type

**Solutions by Search Mode**:

**AtlasRAG**:
```json
{
  "damping_factor": 0.9,    // Focus on authoritative nodes
  "temperature": 0.3,       // More consistent, factual responses
  "detail_level": "detailed", // More context for relevance assessment
  "max_hops": 2             // Reduce to focus on direct relationships
}
```

**Graphiti**:
```json
{
  "searchType": "COMBINED_HYBRID_SEARCH_CROSS_ENCODER", // Highest accuracy
  "diversityFactor": 0.3,   // Focus on relevance over diversity
  "temperature": 0.3,       // More focused responses
  "categoryFilters": ["RELEVANT_CATEGORY"] // Restrict to specific domain
}
```

**Query Optimization Strategies**:
- Use more specific terminology from your domain
- Include context in queries (e.g., "trading system authentication" vs. "authentication")
- Try exact phrases in quotes for precise matching
- Use category filtering to restrict search domain

### Issue: Missing Expected Results

**Symptoms**:
- Known relevant information doesn't appear in results
- Expected entities or relationships are absent
- Search seems to miss obvious connections

**Diagnosis Steps**:
1. **Verify Data Existence**: Search for specific entity names directly
2. **Check Graph Coverage**: Use graph visualization to verify connections exist
3. **Test Broader Parameters**: Increase result counts and exploration depth
4. **Review Category Filtering**: Ensure important categories are included

**Solutions by Search Mode**:

**AtlasRAG**:
```json
{
  "topN": 30,           // Cast wider net for results
  "max_hops": 4,        // Explore deeper relationships
  "damping_factor": 0.6, // Allow more exploration
  "show_graph_traversal": true // See what paths were explored
}
```

**Graphiti**:
```json
{
  "nodeCount": 12,         // Increase result count
  "edgeCount": 15,         // Include more relationships
  "categoryFilters": null, // Search all categories
  "searchType": "COMBINED_HYBRID_SEARCH_MMR", // Better coverage
  "diversityFactor": 0.8   // Prioritize diversity
}
```

**Data Verification Steps**:
- Use direct search for entity names to verify they exist
- Check relationship connections in graph visualization
- Verify category assignments are correct for missing entities

## Performance and Timeout Issues

### Issue: Slow Search Response Times

**Symptoms**:
- Queries take longer than 30-60 seconds to complete
- Users experience delays in getting results
- System appears unresponsive during searches

**Diagnosis Steps**:
1. **Check Processing Times**: Enable metadata to see actual processing duration
2. **Monitor System Resources**: Look for CPU, memory, or network bottlenecks
3. **Test Parameter Impact**: Try queries with reduced parameters
4. **Compare Search Modes**: AtlasRAG vs. Graphiti performance differences

**Immediate Performance Fixes**:

**AtlasRAG Speed Optimization**:
```json
{
  "topN": 8,               // Reduce result processing
  "max_hops": 2,           // Limit exploration depth
  "damping_factor": 0.9,   // Focus on authoritative results
  "max_tokens": 1000,      // Shorter responses
  "detail_level": "concise", // Less processing overhead
  "show_graph_traversal": false, // Disable debug features
  "show_pagerank_scores": false,
  "show_context": false
}
```

**Graphiti Speed Optimization**:
```json
{
  "searchType": "COMBINED_HYBRID_SEARCH_RRF", // Fastest algorithm
  "nodeCount": 3,          // Minimal result set
  "edgeCount": 4,          // Fewer relationships
  "timeout": 30,           // Set realistic timeout
  "categoryFilters": ["SPECIFIC_CATEGORY"] // Limit search scope
}
```

**Long-term Performance Solutions**:
- Scale infrastructure resources (CPU, memory)
- Optimize database queries and indexing
- Implement caching for frequent queries
- Use load balancing for concurrent users

### Issue: Search Timeouts

**Symptoms**:
- Queries fail with timeout errors
- "Request timed out" messages appear
- Inconsistent query completion

**Diagnosis Steps**:
1. **Check Timeout Settings**: Verify current timeout configuration
2. **Test Query Complexity**: Try simpler versions of failing queries
3. **Monitor System Load**: Check if system is under heavy load
4. **Review Parameter Combinations**: Some combinations are particularly resource-intensive

**Timeout Resolution Strategies**:

**Adjust Timeout Values**:
```json
{
  "timeout": 90  // Increase for complex queries
}
```

**Reduce Query Complexity**:
```json
{
  "atlasrag": {
    "topN": 10,
    "max_hops": 2
  },
  "graphiti": {
    "nodeCount": 5,
    "edgeCount": 6
  }
}
```

**System-Level Solutions**:
- Increase server timeout settings
- Optimize database query performance
- Scale computational resources
- Implement query queuing for high load

## Configuration and Integration Issues

### Issue: Configuration Changes Not Applied

**Symptoms**:
- Parameter changes don't affect search results
- Settings revert to previous values
- Configuration UI shows different values than actual behavior

**Diagnosis Steps**:
1. **Check Browser Storage**: Clear browser cache and local storage
2. **Verify API Connectivity**: Ensure configuration changes reach the server
3. **Test Different Browsers**: Rule out browser-specific issues
4. **Check Service Restart**: Some changes may require service restart

**Solutions**:
1. **Clear Browser Storage**:
   ```javascript
   // Clear configuration cache
   localStorage.removeItem('kg-visualizer-unified-config');
   // Refresh the page
   location.reload();
   ```

2. **Force Configuration Refresh**:
   - Navigate to Settings → Search Configuration
   - Click "Reset to Defaults"
   - Reconfigure with desired settings

3. **Verify Service Status**:
   - Check that all services are running (API, Python AI, Proxy)
   - Restart services if configuration changes aren't applying
   - Monitor service logs for configuration loading errors

### Issue: Category Filtering Not Working

**Symptoms**:
- Category selection doesn't affect search results
- Results from excluded categories still appear
- Category filter UI appears but doesn't function

**Diagnosis Steps**:
1. **Verify Search Mode**: Category filtering only works in Graphiti mode
2. **Check Category Selection**: Ensure categories are actually selected
3. **Test with Specific Categories**: Try single-category filtering
4. **Review Result Categories**: Check if results match selected categories

**Solutions**:
1. **Ensure Correct Mode**:
   ```json
   {
     "mode": "graphiti",  // Category filtering requires Graphiti mode
     "graphiti": {
       "categoryFilters": ["TRADING", "RISK"]
     }
   }
   ```

2. **Verify Category Names**:
   - Use exact category names from the predefined list
   - Check for case sensitivity issues
   - Ensure categories exist in your graph data

3. **Test Category Coverage**:
   - Start with broad categories (e.g., "SYSTEM", "TRADING")
   - Verify your graph data has entities in selected categories
   - Check category assignment in your knowledge graph

### Issue: Provider Configuration Problems

**Symptoms**:
- Azure OpenAI or other providers not working
- Authentication errors with external providers
- Provider selection doesn't affect responses

**Diagnosis Steps**:
1. **Check Environment Variables**: Verify Azure/OpenAI credentials are set
2. **Test Provider Connectivity**: Use provider-specific test queries
3. **Review Error Messages**: Look for authentication or network errors
4. **Verify Provider Selection**: Ensure correct provider is selected in configuration

**Solutions**:

**Azure OpenAI Configuration**:
```bash
# Required environment variables
export AZURE_API_KEY="your-api-key"
export AZURE_ENDPOINT="https://your-instance.openai.azure.com/"
export AZURE_DEPLOYMENT_NAME="your-deployment"
export AZURE_API_VERSION="2023-12-01-preview"
```

**Provider Testing**:
```json
{
  "llmProvider": "azure-openai",
  "azureEndpoint": "https://your-instance.openai.azure.com/",
  "azureDeploymentName": "your-deployment",
  "azureApiVersion": "2023-12-01-preview"
}
```

**Fallback Configuration**:
```json
{
  "llmProvider": "ollama",  // Use local Ollama as fallback
  "ollamaUrl": "http://localhost:11434"
}
```

## Search Mode Specific Issues

### AtlasRAG-Specific Issues

#### Issue: Graph Traversal Not Showing Expected Paths

**Symptoms**:
- Graph traversal debug output shows unexpected paths
- Missing connections that should exist
- Traversal stops earlier than expected

**Solutions**:
```json
{
  "max_hops": 4,           // Allow deeper exploration
  "damping_factor": 0.6,   // Increase exploration vs. authority
  "show_graph_traversal": true, // Enable path visualization
  "topN": 25               // Get more potential starting points
}
```

#### Issue: PageRank Scores Seem Wrong

**Symptoms**:
- Unexpected entities have high PageRank scores
- Important entities have low scores
- Scores don't match business importance

**Investigation Steps**:
1. Enable PageRank score display: `"show_pagerank_scores": true`
2. Review graph structure for unexpected connections
3. Check for data quality issues (duplicate entities, missing relationships)
4. Consider if business importance aligns with graph connectivity

**Solutions**:
- Adjust `damping_factor` to change authority vs. exploration balance
- Review and clean graph data for structural issues
- Consider domain-specific PageRank customization

### Graphiti-Specific Issues

#### Issue: Search Algorithm Performance Differences

**Symptoms**:
- Some algorithms consistently perform poorly
- Unexpected performance variations between algorithms
- Quality differences not matching expectations

**Algorithm Troubleshooting**:

**Cross-Encoder Issues**:
- Very slow: Reduce node/edge counts, enable category filtering
- Poor quality: Check query formulation and terminology
- Timeouts: Use shorter timeout or switch algorithms

**MMR Issues**:
- Too diverse: Lower `diversityFactor` (0.3-0.5)
- Too focused: Increase `diversityFactor` (0.7-0.9)
- Poor balance: Try `diversityFactor` of 0.6

**RRF Issues**:
- Poor quality: Switch to Cross-Encoder for important queries
- Still too slow: Reduce result counts further
- Missing nuance: Use MMR or Cross-Encoder for complex queries

## Debug Information and Logging

### Using Debug Features Effectively

#### AtlasRAG Debug Information
```json
{
  "show_graph_traversal": true,    // See exploration paths
  "show_pagerank_scores": true,    // Understand entity importance
  "show_context": true,            // See raw retrieved information
  "show_embeddings": false         // Usually too technical for debugging
}
```

#### Graphiti Debug Information
Enable metadata display in the UI to see:
- Processing time breakdown
- Confidence scores
- Algorithm performance metrics
- Category filter effectiveness

### Systematic Troubleshooting Approach

1. **Isolate the Problem**:
   - Test with minimal configuration
   - Use default settings to establish baseline
   - Change one parameter at a time

2. **Gather Information**:
   - Enable all relevant debug features
   - Document exact query text and parameters
   - Record error messages and response times

3. **Test Systematically**:
   - Compare both search modes
   - Test with and without category filtering
   - Try different parameter ranges

4. **Document Solutions**:
   - Record successful parameter combinations
   - Share solutions with team members
   - Update configuration defaults based on findings

## Getting Additional Help

### Internal Resources
1. **Built-in Help**: Click (?) icons in configuration UI
2. **Metadata Display**: Enable debug information in responses
3. **Performance Dashboard**: Monitor metrics in Settings panel

### Documentation References
- [AtlasRAG Configuration](./atlasrag-configuration.md) - Detailed parameter explanations
- [Graphiti Configuration](./graphiti-configuration.md) - Complete settings guide
- [Performance Tuning](./performance-tuning.md) - Optimization strategies
- [User Guide](../user-guide/README.md) - General usage information

### Escalation Process
If issues persist after trying these troubleshooting steps:

1. **Document the Issue**:
   - Exact query text and configuration used
   - Expected vs. actual behavior
   - Error messages and debug information
   - Steps already attempted

2. **Contact Support**:
   - Include all documentation from step 1
   - Specify urgency and business impact
   - Provide configuration export for reproduction

3. **Temporary Workarounds**:
   - Switch search modes if one is problematic
   - Use simpler configuration until issues are resolved
   - Implement fallback queries for critical use cases