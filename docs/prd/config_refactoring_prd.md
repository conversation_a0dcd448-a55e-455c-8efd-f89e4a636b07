RAG Configuration Rewrite PRD

Overview and Objectives

The current dual-mode RAG configuration (Graphiti + Atlas) is overly complex and tightly coupled.  Analysis shows redundant transformations, duplicate validation, and intertwined legacy logic ￼ ￼.  We will re-architect from the ground up: Graphiti and Atlas RAG configurations become entirely separate systems with no shared schema or code paths.  All legacy fallbacks (including graphitiSettings and auto‐detection logic) will be eliminated ￼ ￼.  The UI will expose only the fields needed per mode (e.g. removing all Azure/OpenAI fields), and the backend will use fixed Azure credentials.  This clean‑slate design prioritizes simplicity, maintainability, and clarity.

Architecture & Design Principles
	•	Separate Pipelines: Implement two independent configuration pipelines (Graphiti vs Atlas). Each has its own schema, validation, and endpoints. No “union” or mode-guessing logic. This avoids overlap and complexity ￼ ￼.
	•	Explicit Mode Selection: The user explicitly chooses Graphiti or Atlas mode up front. UI components and state are isolated per mode. We remove any flags like useAtlasRAG that tried to toggle modes in shared state ￼ ￼.
	•	No Legacy Fallbacks: Delete all legacy logic. For example, remove graphitiSettings entirely and disallow it in requests ￼. Do not auto-infer mode from payload. Reject any old-format payloads outright after migration.
	•	Minimal Transformation: Each layer works with a single canonical format. The UI sends a final config matching the backend’s schema; the backend validates once and passes it to Python as-is. This eliminates 4–6 conversion steps per request ￼.
	•	Field-Level Validation Only: In the UI, perform only light field validations (e.g. ranges, required fields). All deeper schema validation is done server-side via strict JSON schema or Pydantic models. We will remove any duplicated validation code in the client ￼.
	•	Hardcoded Azure Config: All Azure/OpenAI-specific settings (endpoint URL, deployment name, API version, etc.) are removed from the UI. The backend will use predefined Azure credentials and models. The UI may still allow choosing a provider (e.g. Azure vs Ollama) but will not collect Azure keys from the user.

Feature Elimination Plan

We will systematically remove or disable legacy features to simplify the system:
	•	Drop graphitiSettings: Delete all code that references graphitiSettings in UI, API, and Python (models, converters, logs). Require mode-specific payloads instead ￼ ￼.
	•	Remove Fallback Logic: Eliminate ensureMode() functions and any auto-detection of mode. Do not accept legacy “searchConfig” unions in API. The API and Python layers will only accept the new format ￼ ￼.
	•	Eliminate Dual Validation: Remove duplicate validation layers for RAG settings. Keep only one authoritative schema validation on the server (AJV for API, Pydantic for Python). Client-side validation is limited to basic field rules ￼.
	•	Purge Azure Fields from UI: In both the Graphiti and Atlas panels, delete all Azure/OpenAI fields (e.g. endpoint URL, deployment name, API version). These values will be fixed in backend configuration. For Atlas, Azure fields are no longer nested in the payload ￼ ￼.
	•	Remove Legacy Endpoints: The old /api/chat/message (union-based) endpoint and its streaming counterpart will be replaced with separate endpoints (see API design). We will disable or delete the old handlers.
	•	Discard Unused UI State: Remove any Zustand store entries or localStorage keys used for legacy RAG config (e.g. settings.graphiti.useAtlasRAG). Migrate or clear old localStorage data.
	•	Break Compatibility: We accept that all existing saved configs and API clients will break. This is by design for a full clean‑slate. No backward compatibility code (e.g. migration shims) will remain after rollout ￼ ￼.

Backend Schema Design

We define two independent schemas (e.g. JSON Schema or Pydantic models):
	•	GraphitiConfig: Contains only Graphiti-specific fields (no Atlas fields). For example:

{
  "edges":      { "type": "integer", "minimum": 0 },
  "nodes":      { "type": "integer", "minimum": 0 },
  "timeoutSeconds": { "type": "integer", "minimum": 0 },
  "temperature":     { "type": "number",  "minimum": 0, "maximum": 1 },
  // (Optional) "provider": e.g. "azure-openai" or "ollama"
}

Fields like edges, nodes, and timeoutSeconds come from the Graphiti panel. There are no fields related to Atlas or nested objects.

	•	AtlasRagConfig: Contains only Atlas-specific fields. For example:

{
  "topN":      { "type": "integer", "minimum": 1 },
  "dampingFactor": { "type": "number", "minimum": 0, "maximum": 1 },
  "maxHops":   { "type": "integer", "minimum": 1 },
  "model":     { "type": "string" },
  "temperature": { "type": "number",  "minimum": 0, "maximum": 1 },
  "maxTokens":   { "type": "integer", "minimum": 1 }
  // (Optional) "provider": e.g. "azure-openai" or "ollama"
}

As with Graphiti, no Azure credential fields are included. (The UI will no longer send them, so the schema does not define them.) If a provider field is kept, it is for informational purposes only; the server will ignore any client‑supplied Azure details.

For both schemas, defaults will be explicitly defined. Azure-specific defaults (endpoint, etc.) are configured entirely in backend environments. All fields will use consistent naming (e.g. camelCase) and explicit types.

API Endpoint Specifications

We replace the single union-based chat endpoint with two distinct endpoints, each accepting a mode-specific config payload:
	•	POST /api/chat/graphiti
Payload:

{
  "message": "string",
  "history": [/* array of past messages */],
  "config": {
    /* GraphitiConfig fields only, e.g. edges, nodes, timeoutSeconds, temperature */
  }
}

	•	The config object must conform to the GraphitiConfig schema above.
	•	The API will validate strictly against this schema (AJV in Node.js, Pydantic in Python) and reject any extra or missing fields.
	•	Example (per Refactoring 3): POST /api/chat/graphiti with { message, history, config: GraphitiSettingsOnly } ￼.

	•	POST /api/chat/atlasrag
Payload:

{
  "message": "string",
  "history": [/* array of past messages */],
  "config": {
    /* AtlasRagConfig fields only, e.g. topN, dampingFactor, maxHops, model, temperature, maxTokens */
  }
}

	•	The config object must conform to the AtlasRagConfig schema.
	•	Azure details are not included; the backend will use its own Azure configuration if needed.
	•	(Optionally, a provider field may indicate which LLM to use, but Azure credentials are never sent.)

Both endpoints will forward the request to the Python service unchanged after validation. The Python FastAPI code should have separate handlers (or a single handler distinguished by path) that parse GraphitiSearchConfig or AtlasRagSearchConfig Pydantic models directly ￼.  No runtime union normalization is required.

Authentication: Existing auth (if any) continues to apply. Chat requests must include user/session context as before.

Error Handling: If a payload fails schema validation, return a 400 error with details. Legacy-style payloads (e.g. containing graphitiSettings or nested Azure fields) will be rejected with a clear error message.

Frontend Changes

The UI must be split into two cleanly separated configuration panels, and all legacy fields removed:
	•	Mode Selection: In the main UI (Search Configuration screen), the user selects either Graphiti or Atlas RAG mode. We will maintain a simple toggle or dropdown. Once selected, the corresponding configuration panel appears. There is no shared or hidden secondary schema. We remove the settings.graphiti.useAtlasRAG flag entirely ￼ ￼.
	•	GraphitiConfigPanel.jsx:
	•	Retain only Graphiti fields (e.g. Edges, Nodes, Timeout, LLM Temperature).
	•	Remove Azure section: Delete the Azure Endpoint, Deployment Name, API Version, etc. fields from the form. For example:
Figure: Current Graphiti configuration UI (Azure fields will be removed).
	•	Simplify LLM provider: If the UI still allows choosing a provider, it should list available providers but not require Azure details. (Alternatively, hardcode Azure as the only provider behind the scenes.)
	•	Update state management: Replace any calls to searchConfigService that enforced mode or merged Atlas settings. Instead, the panel can maintain its own local state or use a new Graphiti-specific settings hook. On “Apply,” it should invoke chatApiService.post('/api/chat/graphiti', {message, history, config}). Remove any dual-state code.
	•	Remove validation code that was copied from Atlas; only validate fields (e.g. ranges) and let the server enforce schema rules.
	•	Adjust unit tests to only use the Graphiti schema (remove any tests expecting graphitiSettings or mode toggling).
	•	AtlasRagConfigPanel.jsx:
	•	Retain only Atlas fields (e.g. Top N Results, Damping Factor, Max Hops, Model, Temperature, Max Tokens).
	•	Remove Azure section: Delete LLM Provider: Azure OpenAI and all Azure settings (Endpoint, Deployment, etc.) from the panel. For example:
Figure: Current Atlas RAG configuration UI (Azure settings will be removed).
	•	If multiple providers are supported, allow selecting a provider (e.g. Ollama) but do not collect Azure credentials.
	•	State management: Similar to Graphiti, use a dedicated state (or a simplified settings hook) that only tracks Atlas fields. On “Apply,” call POST /api/chat/atlasrag with the Atlas config. Remove the previous ensureMode() logic that nested Azure fields; now Azure is simply not part of the UI state ￼ ￼.
	•	Remove any toggles or messages that referenced atlasRagSettings. The panel’s validation code should be minimal; rely on server-side schema for full checking. Update any UI tests/snapshots to expect the new flat config.
	•	SearchConfigService: We will decommission or significantly simplify this service. It currently held the mode-union and persisted to localStorage under one key. Instead: either create two small services (e.g. graphitiConfigService and atlasConfigService) or keep a single service that does nothing more than save/load mode-specific JSON to localStorage if needed. In practice, because chat requests do not permanently save settings (they come from the form each time), we may not need a complex store at all. Any existing code in searchConfigService that manipulates graphitiSettings or merges schemas should be removed ￼ ￼.
	•	Chat API Service: Update chatApiService.sendMessage (and streaming) to hit the new endpoints instead of /chat/message. The logic will be: if Graphiti mode, POST to /chat/graphiti with Graphiti config; if Atlas, POST to /chat/atlasrag with Atlas config. Remove any logging or code that referred to payloadConfig = graphitiSettings. (Refactoring notes highlight removing legacy logs ￼.)
	•	LocalStorage Keys: Ensure the only persisted data is the new canonical form (if any). For example, if local caching is needed, use keys like 'kg-visualizer-graphiti-config' and 'kg-visualizer-atlas-config' (or similar) instead of the old 'kg-visualizer-search-config'. Migrate or drop any data under the old keys ￼.

Migration and Cleanup Plan

The transition will be done in stages, with thorough testing at each step:
	1.	Define new schemas and contracts:  Finalize the GraphitiConfig and AtlasRagConfig schemas (JSON Schema and Pydantic). Generate any client/typed definitions. Ensure defaults are set. Update API documentation/contracts.
	2.	Implement backend endpoints:  Add /api/chat/graphiti and /api/chat/atlasrag handlers. In chatRoutes.js, create distinct routes that validate each payload against its schema (using AJV). Do not accept graphitiSettings or union payloads. Initially keep the old /chat/message route inactive or returning an error.  On the Python side, add matching FastAPI handlers or route logic that parses each schema (using Pydantic models). Keep migration code only momentarily to ease development.
	3.	Develop frontend panels:  In parallel, refactor the React UI: modify GraphitiConfigPanel and AtlasRagConfigPanel per the design above. Remove all Azure fields and legacy toggles. Wire the “Apply” buttons to the new endpoints (chatApiService.post('/api/chat/graphiti', ...) etc.). Simplify or remove the existing searchConfigService. At this point, the new UI and backend should be in sync with the same assumptions.
	4.	Testing (Phase 1):  Write unit tests for each config panel (field validation, state updates) and for the chatApiService. Write API integration tests to ensure /chat/graphiti and /chat/atlasrag accept valid payloads and reject invalid ones. Specifically test that nested or union fields are rejected. Update contract tests: the UI’s searchConfigContract.test.js should now expect a GraphitiConfig or AtlasConfig shape, not the old union ￼ ￼. Update Python tests similarly.
	5.	Disable legacy path: Once new endpoints are verified, fully disable or remove the old /api/chat/message route and related code. Update any front-end code to no longer reference it. (Keep version control history for reference, but delete the code paths.)
	6.	Cleanup legacy code: Delete or comment out all legacy logic: ensureMode(), the old service methods, UI elements, fallback validators. Remove any code that writes to graphitiSettings or expects a mode toggle. Prune searchConfigService entirely if unused. Remove any migration/helper functions in Python that deal with legacy formats ￼.
	7.	Deployment and rollout: Deploy the new version. Existing saved configs in users’ localStorage (if any) should be cleared or automatically converted to the new keys. Announce breaking changes to any stakeholders. No “v2 behind feature flag” is needed since backward compatibility is not required; we can do a direct cutover.
	8.	Post-deployment testing: Perform end-to-end tests: verify that Graphiti chat and Atlas chat both work from the UI to the database. Check that removing Azure fields did not introduce connection issues (Azure endpoint is taken from env). Confirm that error messages are clear if a user tries an invalid input.

Breaking Changes and Testing Guidance
	•	Expected Breakpoints: Any client or saved configuration using the old schema will break. For example, if UI code still tries to nest Azure under atlasragSettings, it will be rejected (the API now expects Azure at root or not at all) ￼. Similarly, requests to /chat/message or payloads containing graphitiSettings will fail. Make sure to clear old config data.
	•	Validation Testing: Rigorous testing is critical because this is a wholesale schema change. Key tests include:
	•	Unit tests: For each new schema, write tests that valid payloads pass and all invalid cases (missing fields, extra fields) fail with correct error messages. Mock the API validators.
	•	Frontend tests: Ensure the config panels show/hide the correct fields (no Azure fields) and enforce basic rules. Snapshot tests should be updated to reflect removed fields.
	•	Integration tests: Simulate the full chat flow in each mode. For example, send a Graphiti query with a sample graph config and verify the correct API is called and the Python service receives the right data.
	•	Edge cases: Test mixed or incomplete inputs, such as selecting a provider but not supplying mandatory model/temperature (UI should catch this). Test timeouts, very large numbers, etc.
	•	Regressions: Ensure that the existing (non-chat) features of the application continue to work unaffected. The graph visualization and other APIs are unchanged.
	•	Monitoring: In production, monitor logs for errors specifically related to chat endpoints. If any old-format payload somehow arrives, log it for cleanup. Verify that the volume of normalization or fallback events (if any remain briefly) goes to zero.
	•	Documentation: Update developer documentation to reflect the new config model. Remove references to legacy fields and “v1” logic. Document the new API contracts (paths, payload schemas) for both modes.

By strictly separating the two modes and removing all legacy baggage, the new configuration system will be far simpler and more robust. There will be fewer layers of transformation and validation, and each part of the stack can be maintained independently. This clean‑slate approach scores highly on simplicity and maintainability, at the cost of one-time migration effort and test coverage ￼ ￼.

Sources: Analysis and refactoring documents provided by the team ￼ ￼ ￼. These informed the above design decisions (e.g. removing graphitiSettings, splitting endpoints, and eliminating Azure UI fields). The final design aligns with the “Refactoring 3” approach of mode-specific endpoints and with recommendations to delete all fallback logic ￼ ￼.