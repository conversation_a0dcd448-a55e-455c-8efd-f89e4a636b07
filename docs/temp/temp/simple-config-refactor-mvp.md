# Simple Configuration Refactor MVP

## Context: Clean Slate Approach
Since this project has no production users, we can eliminate ALL backward compatibility complexity and implement a clean, simple solution. The existing shared package (`config/search-config`) already provides a good discriminated union foundation - we just need to use it properly.

## 5-Iteration Self-Reflection Simplification

### Iteration 1: What's Actually Broken?
- **Problem**: Dual UI services, dual localStorage keys, Azure field duplication, dual API acceptance
- **Root Cause**: Evolutionary complexity, not architectural necessity
- **Simplest Fix**: Direct replacement, no migration needed

### Iteration 2: What's the Minimum Viable Change?
- **UI**: Replace two services with one store using shared types
- **API**: Accept only `searchConfig`, remove `graphitiSettings` path entirely
- **Python**: Use unified config directly
- **Storage**: One localStorage key, simple replacement
- **Security**: Environment variables only, no client secrets

### Iteration 3: What Complexity Can We Eliminate?
- ❌ No versioning, compatibility layers, or migration scripts
- ❌ No feature flags, rollback mechanisms, or telemetry systems
- ❌ No dual-read/write patterns or gradual rollouts
- ❌ No complex secret lifecycle management
- ✅ Simple direct replacement with basic error handling

### Iteration 4: What's the Simplest Implementation Path?
1. Fix security issue: Remove Azure secrets from client entirely
2. Replace UI configuration layer: One store, shared types, direct binding
3. Simplify API: One endpoint, one validation path, shared schema
4. Align Python: Direct consumption of unified config
5. Migrate storage: Simple localStorage key replacement with fallback

### Iteration 5: Can We Simplify Further?
- **Question Each Step**: Is this absolutely necessary for MVP?
- **Remove Everything Non-Essential**: Focus only on working unified config
- **Defer All Sophistication**: Monitoring, complex validation, error handling can be added later

## MVP Implementation Plan (2-3 Days)

### Day 1: Foundation & Security
**Goal**: Remove client-side secrets and establish unified config foundation

#### Step 1.1: Security Fix - Remove Client Azure Secrets (2 hours)
```bash
# 1. Update shared package defaults
cd config/search-config/src
# Remove azureApiKey from defaults.ts, keep other Azure fields for provider info
# Set azureApiKey: undefined in defaults

# 2. Update Python to read Azure secrets from environment only
cd python-ai
# Add AZURE_API_KEY environment variable reading
# Remove azureApiKey from Pydantic models or make Optional with env fallback

# 3. Update API validation to not require azureApiKey in payloads
cd 360t-kg-api/utils
# Modify searchConfigValidator.js to read Azure secrets from process.env
# Remove azureApiKey requirement from business rules validation
```

#### Step 1.2: Simplify Shared Package (1 hour)
```typescript
// config/search-config/src/types.ts - Keep existing discriminated union, just clean up Azure handling
export interface BaseProviderConfig {
  llmProvider: LlmProvider;
  ollamaUrl?: string;
  // Azure info fields (non-secret) for UI display
  azureEndpoint?: string;
  azureDeploymentName?: string;
  azureApiVersion?: string;
  azureModel?: string;
  // Remove azureApiKey - backend only
}
```

### Day 2: UI Simplification
**Goal**: Replace dual UI services with single store using shared types

#### Step 2.1: Create Unified Config Store (3 hours)
```typescript
// 360t-kg-ui/src/stores/configStore.ts
import { SearchConfig } from '../../../config/search-config/src/types.js';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface ConfigStore {
  config: SearchConfig;
  setConfig: (config: SearchConfig) => void;
  setMode: (mode: 'graphiti' | 'atlasrag') => void;
  updateGraphiti: (partial: Partial<GraphitiSettings>) => void;
  updateAtlasRag: (partial: Partial<AtlasRagSettings>) => void;
}

export const useConfigStore = create<ConfigStore>()(
  persist(
    (set, get) => ({
      config: DEFAULT_GRAPHITI_CONFIG, // From shared package
      setConfig: (config) => set({ config }),
      setMode: (mode) => set({
        config: mode === 'graphiti' ?
          { ...get().config, mode, graphiti: DEFAULT_GRAPHITI_CONFIG.graphiti } :
          { ...get().config, mode: 'atlasrag', atlasrag: DEFAULT_ATLAS_RAG_CONFIG.atlasrag }
      }),
      updateGraphiti: (partial) => set(state => ({
        config: { ...state.config, graphiti: { ...state.config.graphiti, ...partial } }
      })),
      updateAtlasRag: (partial) => set(state => ({
        config: { ...state.config, atlasrag: { ...state.config.atlasrag, ...partial } }
      }))
    }),
    { name: 'kg-visualizer-unified-config' } // Single storage key
  )
);
```

#### Step 2.2: Replace Services and Update Panels (2 hours)
```bash
# 1. Delete old services
rm 360t-kg-ui/src/services/searchConfigService.js
# Keep settingsService only for non-config settings (auth, etc.)

# 2. Update panels to use store directly
# AtlasRAGConfigPanel.jsx: Replace searchConfigService with useConfigStore
# GraphitiConfigPanel.jsx: Replace searchConfigService with useConfigStore
# Remove Azure secret input fields, keep provider selection only

# 3. Update chatApiService to use store
# Replace searchConfigService.getConfig() with useConfigStore.getState().config
```

### Day 3: Backend Alignment
**Goal**: Simplify API and Python to handle unified config only

#### Step 3.1: Simplify API Endpoint (2 hours)
```javascript
// 360t-kg-api/routes/chatRoutes.js
// Remove graphitiSettings acceptance entirely - only accept searchConfig
const { message, history, conversationId, searchConfig } = req.body;

// Remove this line: const configInput = searchConfig || graphitiSettings || {};
// Replace with: const configInput = searchConfig || {};

// Update validation to read Azure secrets from environment
// Modify validateSearchConfig to inject Azure credentials from process.env when needed
```

#### Step 3.2: Simplify Python Service (2 hours)
```python
# python-ai/src/models/search_config.py
# Remove all migration functions: convert_legacy_graphiti_to_union, migrate_atlas_config_structure
# Remove build_legacy_graphiti_settings function
# Simplify ensure_search_config to just validate against Pydantic models directly

# python-ai/main.py
# Update /chat endpoint to expect search_config only
# Remove any legacy conversion logic
# Add Azure credential reading from environment variables
```

#### Step 3.3: Environment Variable Setup (1 hour)
```bash
# Add to .env files
AZURE_API_KEY=your_azure_key_here
AZURE_ENDPOINT=your_azure_endpoint_here
AZURE_DEPLOYMENT_NAME=your_deployment_name_here
AZURE_API_VERSION=2023-12-01-preview

# Update deployment docs with required environment variables
# Add validation that fails fast if Azure provider selected but env vars missing
```

## Testing Strategy (Simple)
1. **Unit Tests**: Zustand store reducers, validation helpers
2. **Integration Tests**: UI panels → store, API endpoint with unified config
3. **Manual QA**: Switch between modes, submit configurations, verify no client secrets
4. **Contract Tests**: Verify UI → API → Python config flow works end-to-end

## Migration Strategy (One-Shot)
```typescript
// Simple localStorage migration on app startup
const legacyConfig = localStorage.getItem('kg-visualizer-search-config');
const legacySettings = localStorage.getItem('kg-visualizer-settings');

if (legacyConfig && !localStorage.getItem('kg-visualizer-unified-config')) {
  try {
    const parsed = JSON.parse(legacyConfig);
    // Simple mapping to new format
    const unified = ensureMode(parsed); // Use existing ensureMode function
    localStorage.setItem('kg-visualizer-unified-config', JSON.stringify({ state: { config: unified } }));
    // Clean up old keys
    localStorage.removeItem('kg-visualizer-search-config');
  } catch (e) {
    console.warn('Legacy config migration failed, using defaults');
  }
}
```

## Success Criteria
- ✅ No Azure secrets in localStorage or client payloads
- ✅ Single UI store with typed configuration
- ✅ API accepts only unified searchConfig format
- ✅ Python service handles unified config directly
- ✅ Configuration works for both Graphiti and AtlasRAG modes
- ✅ Basic error handling for missing environment variables

## Architectural Assessment

### **Architectural Impact**: Medium
Simple replacement of existing patterns with cleaner equivalents. No new architectural concepts introduced.

### **Pattern Compliance Checklist**:
- [x] **Adherence to existing patterns**: Uses existing discriminated union from shared package
- [x] **SOLID Principles**: Single responsibility (one store, one config format), dependency inversion (shared types)
- [x] **Dependency Management**: Clean dependency flow from shared package to services

### **Identified Benefits**:
- **Eliminates duplication**: Single source of truth for configuration
- **Improves security**: No client-side secrets
- **Reduces complexity**: One storage mechanism, one API path, one validation flow
- **Maintains type safety**: Leverages existing shared TypeScript types

### **Long-Term Implications**:
- **Scalability**: Clean foundation for future configuration features
- **Maintainability**: Significantly reduced codebase complexity
- **Development velocity**: Developers only need to understand one configuration system

## Why This Approach Works
1. **Leverages existing good architecture**: The shared config package discriminated union is already well-designed
2. **Eliminates complexity without adding new complexity**: Direct replacement approach
3. **Focuses on core value**: Working unified configuration system
4. **Enables future iteration**: Clean foundation allows adding sophistication later
5. **Minimal risk**: Simple changes with clear rollback (git revert)

This MVP can be implemented quickly, proves the concept works, and provides a solid foundation for future enhancements without the operational overhead of complex migration systems.