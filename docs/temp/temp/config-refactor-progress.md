| Task ID | Summary | Owner | Status | Evidence | Validator | Notes |
|---------|---------|-------|--------|----------|-----------|-------|
| T1 | Remove client Azure secrets & align backend env usage | Codex | done | `config/search-config/src/*`, `360t-kg-api/utils/searchConfigValidator.js`, `python-ai/src/models/search_config.py`, `python-ai/main.py` updates | Karen | Refer to simple-config-refactor-mvp.md Step 1 |
| T2 | Replace UI config layers with single store & panels | Codex | done | `360t-kg-ui/src/stores/configStore.js`, panels sync effects, new jest tests | Karen & Jenny | New store replaces legacy service |
| T3 | Simplify API endpoint to accept only searchConfig | Codex | done | `360t-kg-api/routes/chatRoutes.js`, `tests/searchConfigContract.test.js`, removed legacy validator` | Karen | Unified payload only |
| T4 | Align Python service to unified config only | Codex | done | `python-ai/src/models/search_config.py`, `python-ai/main.py`, `tests/test_search_config_contract.py` | Karen & Jenny | Legacy converters removed |
| T5 | Cleanup, docs, run tests, verify no legacy references | Codex | done | Tests: `npm test -- configStore`, `npx mocha tests/searchConfigContract.test.js`, `PYTHONPATH=. pytest tests/test_search_config_contract.py -q` | Karen & Jenny | Legacy keys removed; repo tidy |
| T6 | Implement Graphiti chat category filters end-to-end | Codex | done | UI store wiring, chat chip UI, API schema update, Python filtering pipeline | Karen & Jenny | Await validation: chip sync + AtlasRAG guard |
