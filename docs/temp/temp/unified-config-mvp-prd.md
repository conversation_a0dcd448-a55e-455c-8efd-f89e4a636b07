# Product Requirements Document: Unified Configuration System MVP

## 1. Executive Summary

### 1.1 Product Overview
The Unified Configuration System MVP eliminates configuration complexity in the Knowledge Graph Visualizer by implementing a single, type-safe configuration object that replaces the current dual-service, dual-storage architecture. This clean-slate approach prioritizes security (removing client-side Azure secrets) and developer experience (single source of truth) while maintaining full functionality for both Graphiti and AtlasRAG search modes.

### 1.2 Business Objectives
- **Security**: Eliminate client-side storage of Azure API keys and credentials
- **Developer Experience**: Reduce configuration complexity from multiple services to single store
- **Maintainability**: Remove duplicate validation logic and transformation layers
- **Foundation**: Establish clean architecture for future configuration features

### 1.3 Success Metrics
- **Security**: 0 Azure secrets in localStorage or client payloads (100% server-side)
- **Complexity Reduction**: 90% reduction in configuration-related code duplication
- **Performance**: <200ms configuration save latency maintained
- **Reliability**: 0% configuration-related error rate increase during rollout

## 2. Problem Statement

### 2.1 Current Architecture Issues
The existing configuration system suffers from evolutionary complexity that creates maintenance burden and security vulnerabilities:

#### Configuration Service Duplication
- **UI Layer**: Two services (`settingsService` + `searchConfigService`) with overlapping responsibilities
- **Storage Layer**: Dual localStorage keys (`kg-visualizer-settings` + `kg-visualizer-search-config`) causing drift
- **Transformation Logic**: Multiple `ensureMode()` implementations creating inconsistency

#### Security Vulnerabilities
- **Client-Side Secrets**: Azure API keys stored in localStorage accessible to browser inspection
- **Payload Transmission**: Credentials sent in API requests creating attack surface
- **Development Exposure**: Secrets visible in browser dev tools and network logs

#### Validation Complexity
- **5-Layer Validation**: UI panels → searchConfigService → API AJV → business rules → Python Pydantic
- **Error Shape Inconsistency**: Different validation layers return different error formats
- **Duplicate Logic**: Same validation rules implemented multiple times across services

#### Legacy Support Burden
- **Dual API Acceptance**: Backend accepts both `searchConfig` and `graphitiSettings`
- **Migration Logic**: Complex Python conversion functions for legacy formats
- **Azure Field Duplication**: Same Azure fields in UI panels, defaults, and API validator

### 2.2 User Impact
- **Configuration Drift**: Users experience settings not persisting or reverting unexpectedly
- **Error Confusion**: Inconsistent validation messages across different parts of the UI
- **Security Risk**: Azure credentials visible in browser storage creates compliance concerns
- **Development Friction**: New configuration features require changes across 5 different validation layers

## 3. Solution Architecture

### 3.1 Target Architecture
The MVP implements a clean, single-source-of-truth configuration system leveraging the existing shared package foundation:

```
┌─────────────────────────────────────────────────────────────┐
│                    Target Architecture                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐    ┌──────────────────────────────────┐ │
│  │   UI Layer      │    │      Shared Package              │ │
│  │                 │    │   config/search-config          │ │
│  │ ┌─────────────┐ │    │                                  │ │
│  │ │ConfigStore  │◄├────┤ SearchConfig (discriminated      │ │
│  │ │(Zustand)    │ │    │ union types)                     │ │
│  │ └─────────────┘ │    │                                  │ │
│  │ ┌─────────────┐ │    │ Defaults, Types, Validation      │ │
│  │ │Config Panels│ │    └──────────────────────────────────┘ │
│  │ └─────────────┘ │                     ▲                  │
│  └─────────────────┘                     │                  │
│           │                              │                  │
│           ▼                              │                  │
│  ┌─────────────────┐                     │                  │
│  │   API Layer     │                     │                  │
│  │                 │                     │                  │
│  │ ┌─────────────┐ │                     │                  │
│  │ │Single       │◄┼─────────────────────┘                  │
│  │ │Endpoint     │ │                                        │
│  │ └─────────────┘ │                                        │
│  │ ┌─────────────┐ │                                        │
│  │ │AJV          │ │                                        │
│  │ │Validation   │ │                                        │
│  │ └─────────────┘ │                                        │
│  └─────────────────┘                                        │
│           │                                                 │
│           ▼                                                 │
│  ┌─────────────────┐                                        │
│  │  Python Layer   │                                        │
│  │                 │                                        │
│  │ ┌─────────────┐ │                                        │
│  │ │Pydantic     │◄┼────────────────────────────────────────┘
│  │ │Models       │ │
│  │ └─────────────┘ │
│  └─────────────────┘
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 Configuration Flow
1. **UI Interaction**: User modifies configuration in panels bound directly to Zustand store
2. **Type Safety**: All mutations use TypeScript types from shared package
3. **Local Storage**: Single key `kg-visualizer-unified-config` with Zustand persistence
4. **API Transmission**: Only `searchConfig` sent to `/api/chat/message` endpoint
5. **Server Validation**: AJV validates against shared schema, injects Azure secrets from environment
6. **Python Processing**: Direct Pydantic validation using generated models from same schema

### 3.3 Security Model
- **Client Layer**: No secrets stored or transmitted, only provider selection and configuration parameters
- **Server Layer**: Azure credentials read from environment variables only
- **Development**: Local `.env` files for development credentials with clear documentation
- **Production**: Secret management system or secure environment variable injection

## 4. Functional Requirements

### 4.1 Configuration Management

#### FR-1: Unified Configuration Store
**Description**: Single Zustand store manages all search configuration state
**Acceptance Criteria**:
- Store uses TypeScript types from shared config package
- Store exposes typed selectors for all configuration fields
- Store supports atomic updates for nested configuration objects
- Store persists to single localStorage key `kg-visualizer-unified-config`

#### FR-2: Mode Switching
**Description**: Support switching between Graphiti and AtlasRAG search modes
**Acceptance Criteria**:
- `setMode('graphiti' | 'atlasrag')` method updates discriminated union
- Mode switch preserves non-mode-specific settings (provider, timeout, etc.)
- Mode-specific configuration sections populated with appropriate defaults
- UI panels show/hide based on selected mode

#### FR-3: Configuration Persistence
**Description**: User configuration persists across browser sessions
**Acceptance Criteria**:
- Configuration auto-saves on any change
- Page refresh preserves all user settings
- Invalid configuration gracefully falls back to defaults
- Migration from legacy localStorage keys on first load

### 4.2 Security Requirements

#### FR-4: Client-Side Secret Elimination
**Description**: No sensitive credentials stored or transmitted from client
**Acceptance Criteria**:
- No `azureApiKey` field in client configuration objects
- No Azure secrets in localStorage at any time
- No Azure credentials in API request payloads
- Browser dev tools show no sensitive information

#### FR-5: Server-Side Credential Management
**Description**: Azure credentials managed exclusively on backend
**Acceptance Criteria**:
- API reads Azure secrets from environment variables only
- Python service reads Azure secrets from environment variables only
- Missing credentials cause clear error messages with remediation steps
- Health check endpoints verify credential availability

### 4.3 API Requirements

#### FR-6: Unified API Endpoint
**Description**: Single endpoint accepts unified configuration format
**Acceptance Criteria**:
- `/api/chat/message` accepts only `searchConfig` parameter
- No `graphitiSettings` parameter accepted (removed entirely)
- AJV validation uses shared schema from config package
- Error responses use consistent format with field-level details

#### FR-7: Configuration Validation
**Description**: Server-side validation with clear error reporting
**Acceptance Criteria**:
- Invalid configuration returns HTTP 400 with structured error details
- Error response includes field path, validation rule, and suggested correction
- Validation errors displayed inline in UI configuration panels
- Azure provider selection validates required environment variables are available

### 4.4 User Experience Requirements

#### FR-8: Configuration Panels
**Description**: UI panels for configuring search parameters per mode
**Acceptance Criteria**:
- AtlasRAG panel shows: topN, damping_factor, max_hops, temperature, detail_level, response_mode
- Graphiti panel shows: searchType, edgeCount, nodeCount, diversityFactor, temperature
- Provider selection shows: ollama, azure-openai (with environment status indicator)
- All form inputs use controlled components with immediate validation feedback

#### FR-9: Error Handling
**Description**: Clear error messages for configuration issues
**Acceptance Criteria**:
- Network errors show "Unable to save configuration" with retry option
- Validation errors show inline next to relevant form fields
- Azure environment errors show clear setup instructions
- Configuration reset option available if settings become corrupted

## 5. Non-Functional Requirements

### 5.1 Performance Requirements
- **Configuration Save Latency**: <200ms for local storage operations
- **API Response Time**: <500ms for configuration validation
- **UI Responsiveness**: <100ms for configuration panel interactions
- **Memory Usage**: Configuration store <1MB memory footprint

### 5.2 Security Requirements
- **Credential Storage**: 0 secrets in client-side storage
- **Transport Security**: HTTPS required for all API communications
- **Error Information**: No sensitive data in error messages or logs
- **Development Security**: Clear documentation for secure local development setup

### 5.3 Compatibility Requirements
- **Browser Support**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **TypeScript**: Strict mode compatibility
- **React**: Compatible with React 18+ and Zustand 4+
- **Node.js**: Compatible with Node 18+ for backend services

### 5.4 Maintainability Requirements
- **Code Reduction**: 90% reduction in configuration validation duplication
- **Type Safety**: 100% TypeScript coverage for configuration objects
- **Documentation**: Inline code documentation for all public APIs
- **Testing**: Unit test coverage >80% for configuration store and validation

## 6. Implementation Plan

### 6.1 Phase 1: Foundation & Security (Day 1)
**Duration**: 3 hours
**Deliverables**:
- Remove Azure secrets from shared package defaults
- Update Python service to read Azure credentials from environment
- Modify API validation to use environment credentials
- Add environment variable documentation

### 6.2 Phase 2: UI Unification (Day 2)
**Duration**: 5 hours
**Deliverables**:
- Create unified Zustand configuration store
- Update configuration panels to use store directly
- Remove legacy searchConfigService
- Implement simple localStorage migration
- Update chatApiService to use unified store

### 6.3 Phase 3: Backend Alignment (Day 3)
**Duration**: 3 hours
**Deliverables**:
- Remove graphitiSettings acceptance from API
- Simplify Python configuration handling
- Remove legacy migration functions
- Add environment variable validation
- Update API error responses

### 6.4 Testing & Validation
**Duration**: 2 hours
**Deliverables**:
- Unit tests for configuration store
- Integration tests for API endpoint
- Manual QA for mode switching
- Security validation (no client secrets)

## 7. User Stories

### 7.1 Configuration Management

**US-1: As a user, I want to configure AtlasRAG search parameters**
```
Given I am on the configuration page
When I select "AtlasRAG" mode
Then I see AtlasRAG-specific configuration options
And I can adjust topN, damping factor, max hops, and other parameters
And my changes are automatically saved
And switching away and back preserves my settings
```

**US-2: As a user, I want to configure Graphiti search parameters**
```
Given I am on the configuration page
When I select "Graphiti" mode
Then I see Graphiti-specific configuration options
And I can adjust search type, edge count, node count, and other parameters
And my changes are automatically saved
And switching away and back preserves my settings
```

**US-3: As a user, I want to select my LLM provider**
```
Given I am configuring search settings
When I select a provider (Ollama or Azure OpenAI)
Then the system shows whether that provider is properly configured
And if Azure is selected but not configured, I see clear setup instructions
And I cannot submit chat requests with an unconfigured provider
```

### 7.2 Error Handling

**US-4: As a user, I want clear feedback when configuration is invalid**
```
Given I enter invalid configuration values
When I try to save or use the configuration
Then I see specific error messages next to the problematic fields
And the error messages explain what values are acceptable
And I can correct the issues without losing other valid settings
```

**US-5: As a developer, I want secure credential management**
```
Given I need to use Azure OpenAI for development
When I set up the development environment
Then I configure credentials via environment variables only
And no secrets appear in browser dev tools or localStorage
And the application clearly indicates when credentials are missing
```

## 8. Technical Specifications

### 8.1 Data Models

#### Configuration Store Schema
```typescript
interface ConfigStore {
  config: SearchConfig; // From shared package
  setConfig: (config: SearchConfig) => void;
  setMode: (mode: 'graphiti' | 'atlasrag') => void;
  updateGraphiti: (partial: Partial<GraphitiSettings>) => void;
  updateAtlasRag: (partial: Partial<AtlasRagSettings>) => void;
}
```

#### API Request Format
```typescript
interface ChatRequest {
  message: string;
  history: ChatMessage[];
  searchConfig: SearchConfig; // Only this field, no graphitiSettings
  conversationId?: string;
}
```

#### Environment Variables
```bash
# Required for Azure OpenAI provider
AZURE_API_KEY=sk-...
AZURE_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_DEPLOYMENT_NAME=gpt-4
AZURE_API_VERSION=2023-12-01-preview
```

### 8.2 API Endpoints

#### POST /api/chat/message
**Request Body**:
```json
{
  "message": "user question",
  "history": [],
  "searchConfig": {
    "mode": "atlasrag",
    "llmProvider": "azure-openai",
    "azureEndpoint": "https://your-resource.openai.azure.com/",
    "azureDeploymentName": "gpt-4",
    "azureApiVersion": "2023-12-01-preview",
    "atlasrag": {
      "topN": 10,
      "damping_factor": 0.85,
      "max_hops": 3,
      "temperature": 0.7
    }
  }
}
```

**Success Response (200)**:
```json
{
  "response": {
    "role": "assistant",
    "content": "...",
    "timestamp": "2025-01-23T10:00:00Z"
  },
  "conversationId": "uuid",
  "structured_response": { /* Format-C response */ }
}
```

**Error Response (400)**:
```json
{
  "error": "Configuration validation failed",
  "details": [
    {
      "field": "searchConfig.atlasrag.topN",
      "message": "Must be between 1 and 100",
      "code": "VALIDATION_RANGE_ERROR"
    }
  ]
}
```

### 8.3 Storage Schema

#### localStorage Key: `kg-visualizer-unified-config`
```json
{
  "state": {
    "config": {
      "mode": "atlasrag",
      "llmProvider": "ollama",
      "ollamaUrl": "http://localhost:11434",
      "timeoutSeconds": 180,
      "azureEndpoint": "",
      "azureDeploymentName": "",
      "azureApiVersion": "",
      "azureModel": "",
      "atlasrag": {
        "topN": 10,
        "damping_factor": 0.85,
        "max_hops": 3,
        "use_ollama": true,
        "ollama_model": "llama3.2",
        "temperature": 0.7,
        "max_tokens": 2048,
        "detail_level": "normal",
        "response_mode": "balanced",
        "system_prompt_template": "balanced",
        "show_graph_traversal": true,
        "show_pagerank_scores": true,
        "show_context": true,
        "show_embeddings": false
      }
    }
  },
  "version": 0
}
```

## 9. Testing Strategy

### 9.1 Unit Tests
- **Configuration Store**: Test all mutations, persistence, mode switching
- **Validation Helpers**: Test shared validation functions
- **API Middleware**: Test configuration validation and error formatting
- **Python Models**: Test Pydantic validation with various inputs

### 9.2 Integration Tests
- **UI → Store**: Test panel interactions update store correctly
- **Store → API**: Test API receives correct configuration format
- **API → Python**: Test configuration forwarding and processing
- **Error Handling**: Test validation errors displayed correctly in UI

### 9.3 Security Tests
- **No Client Secrets**: Verify no Azure credentials in localStorage or network requests
- **Environment Variables**: Test Azure provider fails gracefully when credentials missing
- **Error Sanitization**: Verify error messages don't leak sensitive information

### 9.4 Manual QA Checklist
- [ ] Switch between Graphiti and AtlasRAG modes
- [ ] Configure all parameters for each mode
- [ ] Submit chat requests with different configurations
- [ ] Verify configuration persists across browser refresh
- [ ] Test with missing Azure environment variables
- [ ] Verify no secrets in browser dev tools
- [ ] Test error handling for invalid configuration values

## 10. Rollout Strategy

### 10.1 Implementation Approach
Since this project has no production users, we implement a **clean slate replacement**:

1. **Direct Implementation**: No backward compatibility needed
2. **Single Deployment**: All changes deployed together
3. **Simple Rollback**: Git revert if issues discovered
4. **Migration Assistance**: Simple localStorage key migration on first load

### 10.2 Risk Mitigation
- **Development Testing**: Thorough local testing before deployment
- **Staging Validation**: Full end-to-end testing in staging environment
- **Monitoring**: Basic error monitoring for configuration issues
- **Quick Rollback**: Simple git revert process documented

### 10.3 Success Validation
- [ ] All configuration functionality works end-to-end
- [ ] No Azure secrets visible in client
- [ ] Performance metrics maintained
- [ ] No increase in configuration-related errors
- [ ] Developer experience improved (single configuration system)

## 11. Future Considerations

### 11.1 Post-MVP Enhancements
- **Configuration Presets**: Save/load named configuration profiles
- **Team Configuration**: Share configurations across team members
- **Configuration Validation**: Advanced client-side validation with server sync
- **Configuration Analytics**: Usage metrics for optimization

### 11.2 Architectural Evolution
- **Schema Versioning**: Add versioning when breaking changes needed
- **Configuration API**: Dedicated configuration management endpoints
- **Real-time Sync**: Live configuration updates across multiple sessions
- **Configuration Testing**: A/B testing different configuration parameters

This MVP establishes a clean foundation that can support these future enhancements without the technical debt of the current system.