# Graphiti Ingestion Tool - Neo4j Compatibility Requirements

Version: 1.0 • Date: 2025-09-27

## 1. Executive Summary

This document defines the definitive Neo4j compatibility requirements for the Graphiti Ingestion Tool so its output integrates with the existing Knowledge Graph Visualizer (KGV) with zero changes to the KGV API/UI.

Compatibility goal:
- The ingestion output MUST match the data shapes used by KGV graph endpoints and UI utilities today.
- No API contract or UI code changes should be required to visualize and traverse ingested data.

Key requirements (high level):
- Business nodes MUST carry the `:Entity` label (in addition to any domain labels) and MUST include `name`, `uuid`, `group_id`, `category` properties.
- Relationships MUST use canonical types and include at least `name` or `fact` property for UI labeling.
- Minimal mandatory indexes/constraints MUST exist on `:Entity(uuid)`, and SHOULD exist on `:Entity(name)` and `:Entity(group_id)`.

References to current KGV behavior:
- KGV Graph API queries only `(:Entity)-[r]->(:Entity)` (see `360t-kg-api/routes/graph.js`).
- KGV edge transformation extracts `r.properties.name` and `r.properties.fact` when present.
- KGV expand resolves nodes by `id(name|uuid)` and supports `group_id` filtering.
- UI type/color mapping prioritizes `node.properties.category`.

---

## 2. Critical Node Schema Requirements

All business nodes created by ingestion MUST:

- Labels
  - MUST include the generic label `:Entity`.
  - SHOULD include a domain label (e.g., `:Product`, `:Workflow`, `:Module`, etc.).

- Properties (required)
  - `name: string` — human-readable display name (≤ 80 chars recommended).
  - `uuid: string` — globally unique and stable identifier (UUID v4). MUST be unique.
  - `group_id: string` — canonical group identifier (e.g., `user_guides_<ID>_<ver>` or `jira_data_<ID>_<ver>`).
  - `category: string` — one of 360T business categories used by UI (e.g., `EMS`, `TRADING`, `RISK`, `COMPLIANCE`, `SEP`, `MMC`, `WORKFLOW`, `SYSTEM`).

- Properties (recommended)
  - `summary: string` — short description.
  - `description: string` — longer free text where available.
  - `url: string` — source documentation link or ticket URL.
  - `created_at: datetime` — creation timestamp (ISO 8601). 
  - `business_category: string` — duplicate of `category` if needed for legacy logic.

- Documents (optional, future-proof for chat)
  - `:Document` nodes MAY be created separately with `name`, `id`, `group_id`, and optional `text` for full-text search. Current graph visualization endpoints ignore `:Document`, which is acceptable.

Data Type Notes:
- Strings SHOULD be ASCII/UTF-8 without control chars; truncate long `name` values; keep verbose content in `description`.

---

## 3. Relationship Schema Standards

Canonical relationship types (UPPERCASE) for KGV compatibility:
- `RELATES_TO`, `DEPENDS_ON`, `MENTIONS` (Document→Entity), `CONTAINS`, `REFERENCES`, `DUPLICATES` (if needed).

Required relationship properties (at least one):
- `name: string` — short label used by UI (e.g., `relates_to`, `blocks`).
- `fact: string` — alternative textual fact label if `name` is not suitable.

Recommended relationship properties:
- `context: string` — short context (e.g., Jira summary snippet or doc snippet).
- `confidence: float` — 0.0–1.0 scoring for inferred links.
- `source_type: string` — e.g., `jira_api`, `document`.
- `group_id: string` — consistent with node group.
- `created_at: datetime` — creation time.

Jira mapping rules:
- `blocks` / `is_blocked_by` → type `DEPENDS_ON` (directional), set `name` to original link text (e.g., `blocks`).
- `relates to` → type `RELATES_TO` (undirected or two directed edges), set `name: "relates_to"`.
- `duplicates` / `is_duplicated_by` → type `DUPLICATES` or `RELATES_TO` with `fact: "duplicates"`.

Document mapping rules:
- Provenance links SHOULD use `(:Document)-[:MENTIONS{snippet,page,...}]->(:Entity)` with optional `context`.

Directionality:
- Maintain correct direction for asymmetric types (e.g., `DEPENDS_ON`).

---

## 4. Neo4j Indexing and Constraints

MUST have:

```cypher
// Unique identifier for Entities
CREATE CONSTRAINT entity_uuid_unique IF NOT EXISTS
FOR (e:Entity) REQUIRE e.uuid IS UNIQUE;

// Indexes commonly used by API/UI
CREATE INDEX entity_name_idx IF NOT EXISTS
FOR (e:Entity) ON (e.name);

CREATE INDEX entity_group_idx IF NOT EXISTS
FOR (e:Entity) ON (e.group_id);
```

SHOULD have (optional but recommended):

```cypher
// Document full-text search (for chat/provenance tooling)
CALL db.index.fulltext.createNodeIndex(
  'document_fulltext', ['Document'], ['text', 'name', 'id']
);
```

Relationship property indexes (optional, add only if workload requires):

```cypher
// Relationship property schema — use cautiously per performance needs
// CREATE INDEX rel_type_idx FOR ()-[r]-() ON (r.type);
// CREATE INDEX rel_confidence_idx FOR ()-[r]-() ON (r.confidence);
// CREATE INDEX rel_group_idx FOR ()-[r]-() ON (r.group_id);
```

---

## 5. Property Mapping Specifications

Source → Neo4j Entity mapping (Documents):
- `file_name` / `title` → `name`
- `doc_id` → `properties.external_id` (optional) and/or part of `name` suffix
- `group_id` (generator) → `group_id` (e.g., `user_guides_<SRCID>_<ver>`)
- `category` (from group mapping) → `category`
- `url` (if available) → `url`
- `summary/abstract` → `summary`; longer → `description`
- `uuid` (generated) → `uuid`

Source → Neo4j Entity mapping (Jira):
- `key` (e.g., `PO-5157`) + `fields.summary` → `name` as `"<KEY> — <summary>"`
- `id` → `properties.external_id` (optional)
- `group_id` (generator) → `group_id` (e.g., `jira_data_<PROJECT>_<ver>`)
- `category` (from mapping) → `category`
- `self`/`browse URL` → `url`
- `uuid` (generated) → `uuid`

Normalization rules (MUST/SHOULD):
- `name` MUST be present; if missing, synthesize from available identifiers.
- `name` SHOULD be ≤ 80 chars; truncate with ellipsis; store full text in `description`.
- `category` MUST be resolvable via deterministic mapping from `group_id` if not directly provided.
- All strings SHOULD be trimmed; remove control characters.

Group ID generator (reference pattern):
- Documents: `user_guides_<source_id>_<version>`
- Jira: `jira_data_<source_id>_<version>`

---

## 6. Code Examples and Templates

Upsert an Entity node:

```cypher
MERGE (e:Entity:Product { uuid: $uuid })
ON CREATE SET
  e.name = $name,
  e.group_id = $group_id,
  e.category = $category,
  e.url = $url,
  e.summary = $summary,
  e.created_at = datetime()
ON MATCH SET
  e.name = coalesce($name, e.name),
  e.group_id = coalesce($group_id, e.group_id),
  e.category = coalesce($category, e.category),
  e.url = coalesce($url, e.url),
  e.summary = coalesce($summary, e.summary);
```

Create a canonical relationship with UI-friendly props:

```cypher
MATCH (a:Entity { uuid: $u1 }), (b:Entity { uuid: $u2 })
MERGE (a)-[r:RELATES_TO]->(b)
ON CREATE SET
  r.name = $name,           // e.g., "relates_to"
  r.context = $context,
  r.confidence = $confidence,
  r.source_type = $source_type,
  r.group_id = $group_id,
  r.created_at = datetime()
ON MATCH SET
  r.context = coalesce($context, r.context),
  r.confidence = coalesce($confidence, r.confidence);
```

Jira link to DEPENDS_ON mapping:

```cypher
MATCH (src:Entity { uuid: $fromUuid }), (dst:Entity { uuid: $toUuid })
MERGE (src)-[r:DEPENDS_ON]->(dst)
ON CREATE SET r.name = 'blocks', r.context = $summary, r.source_type = 'jira_api', r.group_id = $group_id, r.created_at = datetime();
```

Document provenance (optional):

```cypher
MERGE (d:Document { id: $docId })
ON CREATE SET d.name = $docName, d.group_id = $group_id
WITH d
MATCH (e:Entity { uuid: $entityUuid })
MERGE (d)-[:MENTIONS { snippet: $snippet, page: $page }]->(e);
```

---

## 7. Validation Checklist

After ingestion for a sample group_id:

1) Cypher validations (MUST):
```cypher
// Entities exist and are typed
MATCH (e:Entity) WHERE e.group_id CONTAINS $group_id RETURN count(e) AS c;

// Required properties present
MATCH (e:Entity) WHERE e.group_id CONTAINS $group_id AND (e.name IS NULL OR e.uuid IS NULL OR e.category IS NULL)
RETURN e LIMIT 10;

// Relationships have at least name or fact
MATCH ()-[r]->() WHERE r.group_id CONTAINS $group_id AND (r.name IS NULL AND r.fact IS NULL)
RETURN r LIMIT 10;
```

2) API contract spot-check (SHOULD, via KGV):
- Call `GET /api/graph/visualization?group_id=<your_group>` — nodes SHOULD render without API changes.
- Call `GET /api/graph/expand?nodeId=<uuid or name>&group_id=<your_group>` — expansion SHOULD return neighbors.

3) UI behaviors (SHOULD):
- Nodes SHOULD display with colors/shapes driven by `category`.
- Edge labels SHOULD show via `name` or `fact`.

---

## 8. Implementation Priority Matrix

- P0 (must do now)
  1. Label nodes with `:Entity` and add required props: `name`, `uuid`, `group_id`, `category`. (Low–Medium effort)
  2. Canonicalize relationship types; ensure `name` or `fact` property present. (Low effort)
  3. Add `:Entity(uuid unique)` constraint; indexes on `name`, `group_id`. (Low effort)

- P1
  4. Deterministic group_id and category mapping; set `url`, `summary` where available. (Low–Medium effort)

- P2
  5. Add `:Document` nodes and `MENTIONS` edges for provenance (no UI change required). (Low effort)

- P3 (optional, later)
  6. Full-text indexes and performance tuning for document search. (Low effort)

---

## 9. Testing Scenarios

Functional tests (ingestion + KGV):
- GraphView render (P0): After ingesting a small dataset, `GET /api/graph/visualization` SHOULD return nodes/edges; UI SHOULD paint nodes by `category`.
- NodeDetails (P0): Selecting a node SHOULD show `properties.name`, `properties.category`, and relationships.
- Expand (P0): `/api/graph/expand?nodeId=<uuid or name>` SHOULD return 1–2 hop neighbors without errors.
- Group filter (P0): Adding `group_id` param SHOULD restrict results to the target group.
- Relationship labeling (P0): Edges SHOULD display labels from `r.name` or `r.fact`.

Performance/regression (P1/P2):
- Constraint enforcement: MERGE by `uuid` SHOULD avoid duplicates.
- Index utilization: Queries SHOULD complete within expected times for typical sizes.

---

## 10. Troubleshooting Guide

Common issues and remedies:

- No nodes in visualization endpoints
  - Cause: Nodes missing `:Entity` label.
  - Fix: Ensure all business nodes have `:Entity` alongside domain labels.

- Nodes render with generic type/color
  - Cause: Missing `category` property.
  - Fix: Map `group_id` → `category` or provide `category` directly during ingestion.

- Expand fails for a node
  - Cause: Missing `name` and `uuid`.
  - Fix: Always set `uuid`; ensure `name` is populated/synthesized.

- Edges show without labels
  - Cause: `r.name`/`r.fact` missing.
  - Fix: Populate at least one; keep canonical type uppercase.

- Group filter returns empty
  - Cause: `group_id` not set on nodes/edges.
  - Fix: Apply `group_id` consistently to nodes (required) and edges (recommended).

- Slow expand/search
  - Cause: Missing indexes/constraint.
  - Fix: Create `uuid` unique constraint; indexes on `name`, `group_id`.

---

## Appendix — Current KGV Expectations (Code References)

- Graph endpoints query only `(:Entity)-[r]->(:Entity)` and output `properties.name`, `r.properties.name/fact` (`360t-kg-api/routes/graph.js`).
- Node type derivation prioritizes `properties.category` (UI: `360t-kg-ui/src/utils/nodeUtils.js`).
- Expand resolves node by `id(name|uuid)` and applies optional `group_id` filters.

