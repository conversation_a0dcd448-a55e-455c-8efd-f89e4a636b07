# Unified Config v2 Refactor — Detailed Execution Plan (Enhanced)

Note: Aligns to docs/prd/config_refactoring_prd.md while prioritizing Unified Config v2 (single discriminated union + single endpoint) based on current codebase and contracts.

Objectives
- Implement Unified Config v2 end-to-end (UI → API → Python)
- Eliminate legacy config paths and duplication
- Max transparency/traceability via manifests, structured logs, decision log, and diagrams

Dependency Graph (Critical Path)
- 1 Decision/Contract → (3 Azure, 4 Validation, 5 Storage) → (6 UI, 7 API, 8 Python) → 9 Tests → 10 Observability

Orchestrator Constraints
- Declarative manifests (YAML/JSON) for tasks/edges/IO/acceptance
- Orchestrator code = sequence + deps + status + logging only (no business rules)
- Standard interfaces: REST, file IO, env vars

Logging/Observability (global)
- JSON logs with fields: ts, corrId, taskId, name, phase(start|finish|error), status(ok|fail), inputsRef, outputsRef, durationMs, resource{cpuPct,memMB}, error{code,message,path}
- Propagate X-Request-ID from UI→API→Python; log sanitized IO examples at task boundaries; add dashboards for progress/failures

Legacy Cleanup Inventory & Order (with Rollback)
1) graphitiSettings acceptance in API (reject; feature-flagged) — rollback: flip flag
2) Dual localStorage keys → single canonical key — rollback: read legacy once behind flag
3) Azure fields in UI/config (remove secrets; provider-only) — rollback: show advisory banner only
4) Redundant UI validations/ensureMode transforms — rollback: revert commit
5) Python migration/fallback shims — rollback: reintroduce shim module

Documentation & Transparency
- ADR: ADR-XXXX-unified-config-v2.md
- Contracts: chat-unified-v2.md (payloads per mode)
- Diagrams: before/after Mermaid flow
- Migration checklists with rollback for each phase
- Inline rationale comments in validators/panels/routes

=============================================================
Atomic Subtasks per Workstream (15–20 min each)
Format per subtask: Description | Inputs → Outputs | Validation
=============================================================

1) Decision & Contract Alignment (Critical Path)
1.1 Compare unified schema vs PRD split; list deltas | schema+PRD → delta-table.md | Table complete & reviewed
1.2 Draft Decision Memo favoring Unified v2 | analysis+delta → ADR draft | Lead sign-off
1.3 Update contract: keep POST /api/chat/message; forbid graphitiSettings | current contract → chat-unified-v2.md | Lint passes; examples parse
1.4 Provide canonical payload examples (graphiti/atlasrag) | types → examples block | Validates in AJV
1.5 Blast-radius matrix (UI/API/Python/tests) | code scan → risk-matrix.md | Stakeholder ack

2) Dependency Inventory & Field Matrix
2.1 Extract fields from config/search-config/types+defaults | code → fields.csv | 100% fields captured
2.2 Catalog UI panels/services fields | UI code → ui-fields.csv | Cross-checked
2.3 Catalog API validator (ensureMode/business) | api code → api-fields.csv | Cross-checked
2.4 Catalog Python Pydantic/migrations | py code → py-fields.csv | Cross-checked
2.5 Duplication heatmap (Azure/provider/temp) | csvs → heatmap.md | Matches findings

3) Azure Config Deduplication & Env Strategy
3.1 Decide SOoT: backend env-only secrets; UI provider-only | matrix → az-strategy.md | Sec review ok
3.2 Specify validator rule change (no secrets required for azure-openai) | rule-note → PR plan | Unit test plan ready
3.3 UI removal list + optional read-only diagnostics | UI scan → ui-azure-removals.md | UX review ok
3.4 Migration note: drop/sanitize stored Azure | keys scan → migration-azure.md | Telemetry counters planned
3.5 Security policy: never store/transmit secrets client-side | policy.md | Approved

4) Validation Consolidation Plan
4.1 Pin authoritative JSON Schema (shared package) | ref → schema-ref.md | Ref present
4.2 Define minimal client checks (required/ranges) | checklist → ui-val-min.md | Panel updates planned
4.3 Standard 400 error shape (code,path,message) | errorspec.json → doc | Sample responses pass schema
4.4 AJV↔Pydantic parity table | tests → parity.md | Red diffs resolved plan
4.5 Redundant UI validations removal list | code scan → ui-val-removals.md | Reviewed

5) Storage & Migration (localStorage)
5.1 Inventory keys & shapes | runtime audit → keys.md | All keys listed
5.2 Specify canonical key & shape | spec → storage-spec.md | Approved
5.3 Draft migration function (pseudo/TS) | code stub → migration.ts | Unit test outline ready
5.4 Telemetry: success/fail counters | event spec → orch-log.md | Log produced in dry-run
5.5 Rollback plan (temporary legacy read) | doc → rollback.md | Switch verified

6) UI Refactor Plan
6.1 Remove Azure secret fields; keep provider | diff list → PR plan | Screenshots updated
6.2 Replace heavy searchConfigService with light store/hook | API of hook → hook.ts | Unit tests stubbed
6.3 Ensure chatApiService sends only searchConfig | confirm → note.md | Dev trace shows no legacy
6.4 Defaults sourced only from shared package | refs → code notes | Build passes
6.5 Apply minimal validations from 4.2 | checklist → code changes | Jest passes

7) API Routing/Endpoint Plan
7.1 Reject graphitiSettings (single endpoint only) | chatRoutes.js → PR diff | Unit tests pass
7.2 Update validator business rules (Azure env) | validator → PR diff | New tests green
7.3 Standardize 400 errors & logging | middleware → PR diff | Shape verified in tests
7.4 Route unit tests (valid/invalid/legacy) | tests → added | Coverage threshold met
7.5 API docs update | docs → updated | Published locally

8) Python Service Alignment
8.1 Confirm unified Pydantic model (discriminant) | model spec → py-note.md | Imports compile
8.2 Remove legacy migration helpers | removal list → PR diff | Tests adapted
8.3 Align error mapping with API spec | map → code | Sample errors align
8.4 Python unit tests (both modes) | tests → added | Green
8.5 Node→FastAPI integration test | test → added | Green

9) Contract & E2E Testing
9.1 UI↔API contract tests (unified payload) | tests → updated | Green
9.2 API↔Python contract tests (forwarding) | tests → added | Green
9.3 Playwright flows (Graphiti/Atlas end-to-end) | specs → added | Pass
9.4 CI gates & coverage targets | config → updated | Required checks enforced
9.5 Regression sweep for unrelated features | report → added | No regressions

10) Telemetry & Error Harmonization
10.1 Logging schema enforcement | schema.json → checked | Sample logs valid
10.2 Correlation ID propagation | header spec → code | IDs present across hops
10.3 Boundary logs (inputs/outputs refs) | code points → added | Sanitization verified
10.4 Error catalog (codes/messages) | catalog.md → added | UX copy reviewed
10.5 Dashboard/hooks for progress | dashboard spec → added | Live in dev

Acceptance & Testing (Phase Gates)
- Gate A: ADR + contract approved (Task 1)
- Gate B: Azure strategy + validation plan approved (Tasks 3,4)
- Gate C: Storage migration verified in dev (Task 5)
- Gate D: UI/API/Python unit tests green (Tasks 6,7,8)
- Gate E: Contract + E2E green in CI (Task 9)
- Gate F: Observability confirms stable runs (Task 10)

Security & Privacy Notes
- No secrets in client storage or payloads; secrets in server env only
- Sanitize logs; store only counts/refs; rotate correlation IDs per run

Deliverable Artifacts (locations)
- docs/decisions/ADR-XXXX-unified-config-v2.md
- docs/contracts/chat-unified-v2.md (payloads, error shapes)
- docs/analysis/config-field-matrix.md (+ heatmap)
- docs/migrations/*.md (keys, azure, rollback)
- docs/orchestrator/*.yaml (manifests)
- docs/arch/*.md (Mermaid diagrams)
- docs/observability/log-schema.json (with samples)


I’m going to gather the key materials first to ground the analysis:
- Read the PRD and the temp analysis doc to understand current issues and proposed refactor
- Locate and open the main config-related code paths in UI, API, and Python services
- List the config schemas in config/search-config
This will let me map the dependencies and validate issues before decomposing tasks.





## Task Receipt and Plan

I will perform a deep-dive on the configuration system using the two provided sources:
- docs/temp/settings_configuration_analysis.md (findings)
- docs/prd/config_refactoring_prd.md (implementation plan; note: docs/prd/config_refactoring.prd was not found)

I’ve created a tasklist to structure the work and enable parallel subagent execution. Current status:
- [x] Investigate/Triage/Understand the problem
- [/] Decision & Contract Alignment (Unified-v2 vs Split-Pipelines) — IN_PROGRESS
- [ ] 8 additional subtasks (detailed below)

Below is the analysis, decomposition, coordination strategy, and a synthesized recommendation with evidence.

---

## Deep Analysis Phase

### What the code currently does (evidence)

- Frontend sends a single “unified” config object as searchConfig to a single endpoint /chat/message:
````javascript path=360t-kg-ui/src/services/chatApiService.js mode=EXCERPT
// Always send unified config
const payload = {
  message,
  history: sanitizeHistory(history),
  searchConfig: effectiveConfig,
  ...(conversationId && { conversationId })
};
return await api.post('/chat/message', payload, { cancelToken: cancelToken.token });
````

- Backend accepts searchConfig (and also legacy graphitiSettings), validates with AJV against a shared schema, normalizes, and forwards to Python:
````javascript path=360t-kg-api/routes/chatRoutes.js mode=EXCERPT
const { message, history, conversationId, searchConfig, graphitiSettings } = req.body;
const configInput = searchConfig || graphitiSettings || {};
const validation = validateSearchConfig(configInput);
if (!validation.valid) return res.status(400).json({ error: ... });
const normalizedSearchConfig = validation.config;
// call FastAPI: POST /chat with search_config: normalizedSearchConfig
````

- The validator normalizes and enforces a discriminated union, including Azure root fields for both modes:
````javascript path=360t-kg-api/utils/searchConfigValidator.js mode=EXCERPT
const ensureMode = (config) => {
  if (config.mode === 'atlasrag') {
    return { mode:'atlasrag', llmProvider:..., atlasrag:{...}, azureEndpoint: config.azureEndpoint, ... };
  }
  return { mode:'graphiti', llmProvider:..., graphiti:{...}, azureEndpoint: config.azureEndpoint, ... };
};
````

- Defaults (including Azure fields at the root) live in a shared config package consumed by both UI and API:
````ts path=config/search-config/src/defaults.ts mode=EXCERPT
export const DEFAULT_ATLAS_RAG_CONFIG = {
  mode: 'atlasrag',
  llmProvider: 'ollama',
  // Azure OpenAI fields (root-level)
  azureEndpoint: '', azureDeploymentName: '', azureApiVersion: '', azureModel: '', azureApiKey: '',
  atlasrag: { topN: 10, damping_factor: 0.85, max_hops: 3, ... }
};
````

- The analysis doc is accurate about current overlap/duplication: multiple config stores in the UI, legacy graphitiSettings, and duplicated Azure/LLM fields. Backend validator/business rules also require Azure fields when llmProvider === 'azure-openai', which drives duplication.

### Key issues (validated with repo code)

1. Mode ambiguity and dual storage artifacts
   - Two storage keys and intermediate transformations lead to drift and desync (UI’s settingsService vs searchConfigService; server still optionally accepts graphitiSettings).

2. Azure configuration duplication
   - Same Azure fields exist at UI settings.graphiti.*, settings.atlasRAGSettings.*, unified root searchConfig.*, and sometimes inside nested structures. This is reflected in both UI defaults and API validator ensureMode constructions.

3. Validation duplication
   - Client panels perform validations; searchConfigService validates; API validates via AJV + business rules; Python Pydantic validates. The flow is redundant and yields multiple error shapes.

4. Default values scattered
   - Shared defaults exist in config/search-config, but panels and services still sprinkle defaults and data-massaging, increasing divergence and cognitive overhead.

5. Legacy migration surface
   - Server still accepts graphitiSettings (even if UI doesn’t send it), and Python contains conversion/migration logic paths, increasing complexity.

6. Architectural divergence between PRD and codebase trendlines
   - The codebase has converged on a single unified discriminated union (config/search-config package + single endpoint).
   - The PRD proposes deleting the unified union and splitting into two separate schemas and endpoints, discarding the shared package model.

### Conflicts between the PRD and the current architecture/goals

- PRD proposes “Separate Pipelines” (two endpoints, two schemas), removing union logic and all legacy fallbacks.
- The repo’s shared package config/search-config and AJV validator codify a unified discriminated union and are used by both UI and API (and contract tests reference them).
- The Additional Rules emphasize a unified “Generic RAG Workflow” and centralized contracts; split-mode endpoints would be a large blast radius change requiring rewiring UI, API, validators, Python, tests, and the shared package.

---

## Task Decomposition (for parallel subagents)

For each subtask: scope, deliverables and validation, dependencies, success criteria.

1) Decision & Contract Alignment (Unified-v2 vs Split-Pipelines) — IN PROGRESS
- Scope: Choose the target model: keep a unified discriminated-union (v2) or adopt split pipelines as per PRD.
- Deliverables:
  - Decision memo with pros/cons, risk, and blast radius.
  - Updated high-level contracts (paths/payloads) to match the decision.
- Dependencies: None.
- Success criteria: Stakeholders sign off; updated contract doc is unambiguous and testable.

2) Dependency Inventory & Field Matrix
- Scope: Produce a field-by-field matrix across UI services, config/search-config, API validator, Python models; identify duplication and conflict mapping.
- Deliverables: Matrix artifact + a heatmap of duplication; list of validation points and defaults by layer.
- Dependencies: None.
- Success criteria: Matrix covers 100% of fields identified in defaults, types, and validators; reviewed by Schema/Contracts task.

3) Azure Config Deduplication & Env Strategy
- Scope: Remove multi-location Azure fields; define single-source-of-truth (root-only within unified config or backend env-only for secrets).
- Deliverables: Proposal doc + migration plan; if env-only, clarify UI removal scope and provide server config.
- Dependencies: (1), (2).
- Success criteria: One location for Azure credentials; security posture documented; migration steps validated with a test payload.

4) Validation Consolidation Plan
- Scope: Define authoritative JSON Schema (from shared package) + AJV/Pydantic alignment; list minimal client validations (range/required only); remove redundant validations and unify error shapes.
- Deliverables: Validation plan + schema references; error response format spec; removal checklist.
- Dependencies: (1), (2).
- Success criteria: Single server-authoritative policy; consistent 400 responses across UI/API/Python; tests passing.

5) Storage & Migration (localStorage keys)
- Scope: Migrate to a single localStorage key and eliminate intermediate cache keys; one-time migration script; cleanup of legacy keys.
- Deliverables: Migration function; rollback plan; acceptance notes.
- Dependencies: (1), (2), (3).
- Success criteria: Legacy keys removed; no settings desync after restart; migration telemetry confirms success.

6) UI Refactor Plan
- Scope: Simplify panels; remove Azure fields if env-only; unify through a single configuration service or simple hook; ensure chatApiService sends canonical payload only.
- Deliverables: Component diff plan; payload examples per mode; UX of mode toggle; removal of dead code (legacy toggles).
- Dependencies: (1), (3), (5).
- Success criteria: Panels manage only per-mode fields; no duplicate Azure UI fields; payloads pass server validation.

7) API Routing/Endpoint Plan
- Scope: If unified-v2: remain at POST /api/chat/message with union schema and remove graphitiSettings acceptance; if split: define new endpoints and validators.
- Deliverables: Route/table of paths; validation attachment; deprecation/removal plan.
- Dependencies: (1), (4).
- Success criteria: Only intended endpoints exist; validators cover intended schema paths; contract tests pass.

8) Python Service Alignment
- Scope: Ensure FastAPI /chat expects unified search_config and remove legacy migration logic post‑client update; add tests.
- Deliverables: Pydantic model alignment; removal list; unit tests.
- Dependencies: (1), (4), (7).
- Success criteria: FastAPI accepts canonical payload; no migration code needed; tests pass.

9) Contract & E2E Testing Plan
- Scope: Update UI↔API↔Python contract tests; API/AJV unit tests; UI component tests; Playwright e2e for both modes.
- Deliverables: Test plan + CI gates; green runs.
- Dependencies: (1), (4), (6), (7), (8).
- Success criteria: All suite levels green; contract test artifacts match final contract.

10) Telemetry & Error Handling Harmonization
- Scope: Standardize error payloads (400 details); add structured logs at validation points and migration path; ensure traceable errors across layers.
- Deliverables: Error schema examples; logger integration points; dashboards (if applicable).
- Dependencies: (4), (7), (8).
- Success criteria: Consistent error shape; logs include correlation IDs; improved triage speed.

---

## Coordination Strategy

- Orchestrator: I will coordinate with @agent-workflow-orchestrator to distribute tasks with explicit inputs/outputs and dependencies as above.
- Parallelization:
  - Run (1) Decision & Contract Alignment in parallel with (2) Dependency Inventory (partial); (2) informs (1) with a timeboxed checkpoint.
  - After (1) decision: unblock (3), (4), (5) in parallel.
  - In parallel: draft (6) UI Refactor and (7) API Routing plans against selected model; both align against (4) validation.
  - (8) Python alignment follows (7) decision; (9) testing after implementation plans stabilize; (10) error/telemetry templates can be prepped early, wired late.
- Gates:
  - Gate A: Decision memo (1) approved.
  - Gate B: Validation plan (4) and Azure strategy (3) approved.
  - Gate C: Storage migration (5) proved safe in dev; UI/API payload checks green.
  - Gate D: Contract + E2E tests (9) green.

---

## Synthesis: Validated Recommendations

There are two viable pathways:

- Option A: Unified Config v2 (recommended)
  - Keep a single discriminated-union SearchConfig across UI/API/Python using the shared config/search-config package.
  - Remove legacy graphitiSettings acceptance in API; UI continues to send only searchConfig.
  - Deduplicate Azure fields and, if feasible, move secrets to backend env only. Keep only a provider selector in UI (e.g., azure-openai vs ollama), but not credentials.
  - Consolidate validation: minimal client checks; server is source of truth (AJV in Node, Pydantic in Python) using the same schema contract.
  - Unify storage: one localStorage key; remove the transformation layer duplication. Migrators run on startup.
  - Rationale:
    - Aligns with current code and tests: one schema, one endpoint, shared schema package.
    - Minimizes blast radius with the same user-visible behavior but less duplication/ambiguity.
    - Matches project “Generic RAG Workflow” and current contract tests.

- Option B: Split Pipelines (PRD)
  - Two separate endpoints and schemas; delete union and shared package contract, replace with two schemas.
  - Remove all Azure fields from the UI; backend-only env config.
  - Rationale:
    - Simpler mental model within each mode, at the cost of a very large refactor across all three services and tests.
    - Breaks existing integrations and shared package usage; high effort and risk.

Evidence against full split now:
- The shared contract is embedded in code and tooling today (UI/Node/Python all reference the shared package or shape).
- Current chat flow and AJV validator assume a union; Python’s FastAPI /chat accepts a unified search_config.
- Code shows UI already moved to always send searchConfig, backend logs show “Never send legacy settings” with a hard-coded null for that path; so we’re most of the way to a simplified unified model already.

Therefore: Adopt Option A (Unified Config v2), while incorporating the best PRD ideas that directly solve the identified issues:
- Remove Azure UI credential fields; use backend env configuration for Azure secrets.
- Delete residual graphitiSettings acceptance/paths; keep a single canonical searchConfig payload.
- Remove ensureMode-style auto-detection in UI; keep explicit config.mode as the discriminator.
- Centralize validation on server; keep light UI validation only.
- Single localStorage key; remove redundant transformation layers.

### Concrete, validated fixes and their coverage of issues

- Eliminate Azure duplication
  - Move Azure credentials to backend env (UI shows provider but not secrets).
  - Remove nested Azure fields in panels; keep root-level Azure slots only if needed for diagnostics; ideally omit from transport to reduce leakage.
  - Validates against current validator which enforces Azure fields when llmProvider === 'azure-openai'; update rule to allow env-managed Azure (no credential transmission).

- Consolidate validation
  - Keep JSON Schema in config/search-config; AJV in Node, Pydantic models generated/kept in Python; remove panel/business duplication.
  - Harmonize error payload shape.

- Remove legacy paths
  - Chat API: stop accepting graphitiSettings; remove ensureMode-like fallback in the Node layer.
  - Python: remove migration logic once UI/API are cut over.

- Single storage
  - Replace dual keys with one; write a one-time migration function, then clean up legacy keys.

- Panels and service simplification
  - Retain two panels (mode-specific UI) but bind directly to the unified discriminated union shape.
  - Decommission searchConfigService’s transformation logic; simple store/hook suffices.

---

## Evidence Snippets (selected)

- chatApiService unified payload:
````javascript path=360t-kg-ui/src/services/chatApiService.js mode=EXCERPT
const effectiveConfig = searchConfig || searchConfigService.getConfig();
const payload = { message, history: sanitizeHistory(history), searchConfig: effectiveConfig, ...(conversationId && { conversationId }) };
return await api.post('/chat/message', payload, { cancelToken: cancelToken.token });
````

- API accepts searchConfig or graphitiSettings; we will remove the latter:
````javascript path=360t-kg-api/routes/chatRoutes.js mode=EXCERPT
const { message, history, conversationId, searchConfig, graphitiSettings } = req.body;
const configInput = searchConfig || graphitiSettings || {};
const validation = validateSearchConfig(configInput);
````

- Validator builds Azure at root for both modes (source of duplication pressure):
````javascript path=360t-kg-api/utils/searchConfigValidator.js mode=EXCERPT
return { mode:'graphiti', llmProvider:..., graphiti:{...},
  azureEndpoint: config.azureEndpoint, azureDeploymentName: config.azureDeploymentName, ... };
````

- Shared defaults include Azure fields at root:
````ts path=config/search-config/src/defaults.ts mode=EXCERPT
azureEndpoint: '', azureDeploymentName: '', azureApiVersion: '', azureModel: '', azureApiKey: '',
atlasrag: { topN: 10, damping_factor: 0.85, max_hops: 3, ... }
````

---

## Subagent Work Packages (detailed format)

For each subtask, the subagent should deliver:
- Outputs: specific artifacts (docs, diffs, schemas, code stubs)
- Validation criteria: how we’ll verify correctness
- Dependencies: prerequisite artifacts/tasks

1) Decision & Contract Alignment — IN PROGRESS
- Outputs: Decision memo; updated contract doc (paths, payloads) for unified-v2; impact assessment for removing legacy paths.
- Validation: Sign-off by tech lead; no conflicts with Additional Rules (unified workflow).
- Dependencies: none.

2) Dependency Inventory & Field Matrix
- Outputs: Field matrix across UI ⇄ Shared Package ⇄ API Validator ⇄ Python; map validation points and default sources.
- Validation: Coverage verified against config/search-config/src/types.ts and dist schema; cross-checked in chatRoutes.js, searchConfigValidator.js, UI panels/services.
- Dependencies: none.

3) Azure Deduplication & Env Strategy
- Outputs: Proposal doc + required changes in validator to not require Azure secrets in payload when provider is azure-openai; UI removals list.
- Validation: Example payloads pass; secrets pulled from env only; no Azure fields stored in localStorage.
- Dependencies: (1), (2).

4) Validation Consolidation
- Outputs: JSON Schema alignment doc + list of removed client validations; standard 400 error shape; Pydantic parity check.
- Validation: AJV unit tests + Python validation tests; single error format observed end-to-end.
- Dependencies: (1), (2).

5) Storage & Migration
- Outputs: Migration function for localStorage; removal list of old keys; sanity checks.
- Validation: Post-migration, UI settings hydrate consistently; no drift after reload; telemetry confirms success rate.
- Dependencies: (1), (2), (3).

6) UI Refactor Plan
- Outputs: Panel diffs (remove Azure secrets, keep provider); simplified config service or hook; sample payloads for both modes.
- Validation: Manual UX check + unit tests; payload validation passes server AJV.
- Dependencies: (1), (3), (5).

7) API Routing/Endpoint Plan
- Outputs: Keep /api/chat/message for unified-v2; PR to remove graphitiSettings acceptance; revised validator business rules.
- Validation: API tests for valid/invalid payloads; 400 error shape consistent; no regression in chat flows.
- Dependencies: (1), (4).

8) Python Alignment
- Outputs: Pydantic model update (if any); migration code removal; unit tests.
- Validation: FastAPI /chat accepts canonical search_config; contract tests green.
- Dependencies: (1), (4), (7).

9) Contract & E2E Testing Plan
- Outputs: Updated cross-service contract tests; Playwright path for both modes; CI config.
- Validation: All tests green locally and in CI.
- Dependencies: (1), (4), (6), (7), (8).

10) Telemetry & Errors
- Outputs: Error payload schema; logging additions around validation and migration; correlation IDs.
- Validation: Logs and responses consistent across layers in a dry-run; dashboards reflect error breakdowns.
- Dependencies: (4), (7), (8).

---

## Next Steps and Coordination

- I will keep “Decision & Contract Alignment” IN PROGRESS and, in parallel, kick off “Dependency Inventory & Field Matrix.” Once the decision memo is finalized, we’ll green‑light Azure strategy, validation consolidation, and storage migration in parallel.
- I will interface with @agent-workflow-orchestrator to assign the 10 subtasks to specialized subagents with timelines and dependency gates. Parallel work will be maximized per the plan above.

---

## Risks and Mitigations

- Risk: Breaking changes by removing graphitiSettings acceptance too early.
  - Mitigation: Feature flag or staged removal; telemetry to confirm no callers are still using it.
- Risk: Azure env-only approach invalidates some UI workflows.
  - Mitigation: Keep provider selector in UI; document that Azure credentials are backend-managed; provide a diagnostic “LLM Provider info” read-only panel.
- Risk: Validation consolidation causes short-term error shape changes.
  - Mitigation: Publish and adopt a standard error schema; update UI parsing and tests in lockstep.

---

## Summary Recommendation

Adopt Unified Config v2 (single discriminated union, single endpoint) and implement:
- Azure secrets moved to backend env; remove Azure UI fields
- Eliminate graphitiSettings and transformation duplication
- Consolidate validation to server-side with a shared schema, minimal client checks
- Single localStorage key with a one-time migration

This directly resolves all issues in docs/temp/settings_configuration_analysis.md with minimal blast radius, aligns with current code and shared contracts, and maintains consistency with the repository’s “Generic RAG Workflow.” The PRD’s “split pipelines” approach yields simplicity per mode but dramatically increases change scope; it can be reconsidered later if we need stricter separation.
