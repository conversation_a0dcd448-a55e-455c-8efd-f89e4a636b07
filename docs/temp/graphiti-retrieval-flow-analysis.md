# Graphiti Retrieval Flow and Architecture Analysis

## Executive Summary

This document provides a comprehensive technical analysis of the current Graphiti retrieval flow, data model relationships, and category filtering mechanisms. The analysis reveals a sophisticated multi-layered architecture that connects episodes, entities, and categories through shared group IDs.

## Core Architecture Overview

### 1. Data Model Structure

**Graphiti Data Model:**
- **Episodes**: Document chunks with metadata (`Episodic` nodes)
- **Entities**: Extracted entities with type-specific labels (`Entity`, `Module`, `Configuration`, etc.)
- **Relationships**: Connections between entities within episodes
- **Group IDs**: Shared identifiers linking episodes to their source documents

**Key Finding**: Episodes and entities **DO share group IDs**. This is the critical linking mechanism.

```cypher
// Episode-Entity Relationship Pattern
// Each episode contains multiple entities
// All entities from the same episode share the same group_id
// Episodes represent document chunks, entities represent extracted concepts
```

### 2. Group ID Mapping System

**Group ID Structure:**
- Format: `user_guides_{DOCUMENT_ID}_{VERSION}`
- Example: `user_guides_01C82B55_v1.0.0`
- Maps to: Document files with categories and URLs

**Category Mapping (from CSV):**
```
group_id,filename,category,url
user_guides_01C82B55_v1.0.0,360T User Guide EMS Part3.txt,EMS,https://...
user_guides_0B4FB0EE_v1.0.0,360T User Guide SupersonicTrader.txt,SEP,https://...
```

**Categories Identified:**
- EMS (Execution Management System)
- TRADING
- SEP (SupersonicTrader)
- RISK
- COMPLIANCE
- BA (Bridge Administration)
- CRM
- ECN
- SEF
- And more...

### 3. Category Filtering Implementation

**Filter Location**: `python-ai/src/search/graphiti_search_engine.py`

**Filtering Logic:**
```python
def extract_category(entity: Any) -> Optional[str]:
    # Check category property first
    category = getattr(entity, 'category', None)
    if category:
        return category

    # Check attributes dictionary
    attributes = getattr(entity, 'attributes', None)
    if isinstance(attributes, dict):
        attr_category = attributes.get('category') or attributes.get('Category')
        if attr_category:
            return attr_category

    # Check properties dictionary
    properties = getattr(entity, 'properties', None)
    if isinstance(properties, dict):
        return properties.get('category')

    return None

def should_include_category(category_value: Optional[str]) -> bool:
    if category_filters is None:
        return True  # No filtering
    if len(category_filters) == 0:
        return False  # Empty filter list means include nothing
    if not category_value:
        return False  # No category means exclude
    return category_value in category_filters
```

**Search Flow with Filtering:**
1. Extract search results from Graphiti (episodes + entities)
2. Apply category filters to both edges and nodes
3. Respect limits after filtering (not before)
4. Double search buffer when filters are active

### 4. Graphiti Search Engine Architecture

**Three-Tier LLM Architecture:**
1. **Graphiti LLM**: Internal knowledge graph operations
2. **Response LLM**: Final answer generation
3. **Embedding LLM**: Vector embeddings (OpenAI)

**Search Configuration:**
```typescript
interface GraphitiSettings {
  searchType: 'COMBINED_HYBRID_SEARCH_CROSS_ENCODER' |
               'COMBINED_HYBRID_SEARCH_MMR' |
               'COMBINED_HYBRID_SEARCH_RRF';
  edgeCount: number;
  nodeCount: number;
  diversityFactor: number;
  temperature: number;
  timeout: number;
  ollamaModel: string;
  graphitiModel: string;
  categoryFilters?: string[] | null;
}
```

**Search Process:**
1. **Query Enhancement**: Domain knowledge injection
2. **Search Execution**: Graphiti `_search()` with configured algorithm
3. **Category Filtering**: Post-search filtering of results
4. **Response Generation**: LLM-based answer creation
5. **Format-C Transformation**: Structured response formatting

### 5. Retrieval Flow Details

**What the Retriever Feeds into Graphiti:**
- Raw query text
- Search configuration (algorithm, limits, diversity)
- Category filters (if specified)
- Conversation history (for context)

**Graphiti Search Methods:**
- `COMBINED_HYBRID_SEARCH_CROSS_ENCODER`: Uses neural reranking
- `COMBINED_HYBRID_SEARCH_MMR`: Maximum Marginal Relevance
- `COMBINED_HYBRID_SEARCH_RRF`: Reciprocal Rank Fusion

**Search Results Structure:**
```python
class SearchResults:
    edges: List[EntityEdge]  # Relationship facts
    nodes: List[EntityNode]  # Entity summaries
    edge_reranker_scores: List[float]  # Algorithm scores
    node_reranker_scores: List[float]  # Algorithm scores
    # ... other metadata
```

### 6. Category Propagation System

**Category Assignment Process:**
1. Episodes created with `group_id` from source documents
2. Entities extracted from episodes inherit `group_id`
3. Category mapped from `group_id` using CSV mapping
4. Category stored on both episodes and entities

**Category Storage Locations:**
- Direct property: `n.category`
- Attributes dictionary: `n.attributes.category`
- Properties dictionary: `n.properties.category`

**Current Data Distribution:**
```
Episodic nodes: 125 per document group
Entity nodes: 82+ per document group
Mixed labels: Module+Entity, Configuration+Entity, etc.
```

### 7. Performance Optimizations

**Search Buffering:**
```python
# Calculate search limit with buffer for better filtering
base_search_limit = max(10, (params.edge_count + params.node_count) * 3)
search_limit = base_search_limit
if category_filters and len(category_filters) > 0:
    search_limit = max(base_search_limit, base_search_limit * 2)
```

**Caching:**
- Graphiti instance caching based on configuration hash
- Prompt template caching
- Validation result caching

**Score Extraction:**
- Raw algorithm scores captured from `*_reranker_scores`
- Fallback to position-based scoring if reranker unavailable
- Scores preserved through Format-C transformation

## Key Technical Insights

### 1. Group ID is the Critical Link
- Episodes and entities share `group_id`
- This enables category propagation from documents to entities
- Essential for filtering by document source

### 2. Category Filtering is Post-Search
- Graphiti search returns unfiltered results
- Category filtering applied after search
- Ensures sufficient results for effective filtering

### 3. Multi-Modal Category Storage
- Categories stored in multiple locations
- Robust extraction handles different storage patterns
- Supports both direct and nested category properties

### 4. Algorithm-Agnostic Architecture
- Supports multiple search algorithms
- Consistent score extraction across algorithms
- Unified filtering logic regardless of search method

## Implications for New Ingestion Tool

### Requirements:
1. **Group ID Assignment**: Must assign consistent `group_id` to episodes and entities
2. **Category Mapping**: Must implement CSV-based category mapping
3. **Relationship Preservation**: Must maintain episode-entity relationships
4. **Metadata Propagation**: Must propagate document metadata to entities

### Design Patterns:
1. **Document-Centric Ingestion**:
   - Ingest documents as groups of episodes
   - Assign shared `group_id` to all components
   - Map categories using existing CSV mapping

2. **Entity Consolidation**:
   - Merge duplicate entities across episodes
   - Preserve `group_id` relationships
   - Maintain category associations

3. **Search Compatibility**:
   - Ensure new data follows existing patterns
   - Test with existing category filters
   - Validate score extraction works

### Data Quality Checks:
1. **Group ID Consistency**: Verify all entities from same episode share `group_id`
2. **Category Completeness**: Ensure all `group_id` values have category mappings
3. **Relationship Integrity**: Verify episode-entity relationships are preserved
4. **Search Performance**: Test filtering performance with larger datasets

## Next Steps

1. **Implement Group ID Assignment Logic**: Ensure consistent `group_id` assignment
2. **Category Mapping Integration**: Integrate CSV mapping into ingestion pipeline
3. **Testing Framework**: Create tests for category filtering and group ID relationships
4. **Performance Validation**: Test with realistic data volumes
5. **Documentation**: Update ingestion tool documentation with these patterns

This analysis provides the foundation for building a new ingestion tool that maintains compatibility with the existing Graphiti search and filtering architecture.