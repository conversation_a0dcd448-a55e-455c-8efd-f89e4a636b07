# Graphiti Ingestion Tool Context

This document provides comprehensive context for understanding the Graphiti retrieval flow, data model, and architecture to inform the design of a new episode strategy for the ingestion tool.

## Executive Summary

The current Graphiti system implements a sophisticated knowledge graph architecture where **episodes and entities share group IDs** as the fundamental linking mechanism. The system uses **post-search category filtering** rather than pre-filtering, with a robust data model that supports semantic search across documents, episodes (chunks), and entities with rich relationship networks.

## 1. Retrieval Flow Architecture

### 1.1 End-to-End Query Flow

```
User Query → Query Enhancement → Graphiti Search → Category Filtering → Response Generation → Format-C Output
```

### 1.2 Three-Tier LLM Architecture

- **Graphiti LLM**: Internal knowledge graph operations (semantic search, entity extraction)
- **Response LLM**: Final answer generation (user-facing)
- **Embedding LLM**: Vector embeddings (OpenAI-based)

### 1.3 Search Configuration System

**Unified Configuration Flow:**
```
Frontend (configStore.js) → API Validation → Python FastAPI → Graphiti Search Engine
```

**Key Configuration Parameters:**
```python
class GraphitiSettings(BaseModel):
    searchType: Literal[
        'COMBINED_HYBRID_SEARCH_CROSS_ENCODER',
        'COMBINED_HYBRID_SEARCH_MMR',
        'COMBINED_HYBRID_SEARCH_RRF'
    ] = 'COMBINED_HYBRID_SEARCH_CROSS_ENCODER'
    edgeCount: int = 6
    nodeCount: int = 2
    diversityFactor: float = 0.3
    temperature: float = 0.3
    categoryFilters: Optional[list[str]] = None
```

## 2. Data Model & Group ID System

### 2.1 Core Data Hierarchy

```
Documents → Episodes (Chunks) → Entities → Relationships
```

### 2.2 Group ID Architecture

**Structure Pattern:**
```
user_guides_[PRODUCT_ID]_[VERSION]  # Production data
```

**Purpose:**
- Primary categorization mechanism
- Source document tracking
- Episode-entity linking
- Category filtering foundation

**Group ID Sharing:**
```
group_id                          | labels(n)                   | count(*)
---------------------------------------------------------------------
"user_guides_LM_v1.0.0"           | ["Episodic"]                | 125
"user_guides_LM_v1.0.0"           | ["Entity"]                  | 82
"user_guides_LM_v1.0.0"           | ["Module", "Entity"]        | 11
```

### 2.3 Neo4j Node Structure

**Entity Nodes:**
```cypher
// Primary entity structure
{
  name: "Entity Name",
  uuid: "unique-identifier",
  group_id: "user_guides_LM_v1.0.0",
  category: "EMS",
  attributes: {category: "EMS", type: "Concept"},
  properties: {category: "EMS"}
}
```

**Document Nodes:**
```cypher
// Document structure with full-text indexing
{
  name: "Document Title",
  uuid: "doc-uuid",
  group_id: "user_guides_LM_v1.0.0",
  content: "Full document text...",
  category: "EMS"
}
```

**Episodic Nodes:**
```cypher
// Episode/chunk structure
{
  name: "Section Title",
  uuid: "episode-uuid",
  group_id: "user_guides_LM_v1.0.0",
  category: "EMS",
  content: "Section content..."
}
```

### 2.4 Relationship Architecture

**Primary Relationship Types:**
- `MENTIONS`:  Episode/Document -> Entity
- `RELATES_TO`: Entity → Entity (dependency relationships)


**Relationship Properties:**
```cypher
// Example relationship with metadata
[entity1]-[:DEPENDS_ON {
  context: "Entity1 depends on Entity2 for X functionality",
  strength: 0.8,
  group_id: "user_guides_LM_v1.0.0"
}]->[entity2]
```

## 3. Category Filtering System

### 3.1 Post-Search Filtering Architecture

**Critical Insight:** Category filtering happens **after** Graphiti search, not during. This enables:

- Efficient search over unknown category distributions
- Better performance with large datasets
- Flexible category assignment during ingestion

**Filtering Algorithm:**
```python
def should_include_category(category_value: Optional[str]) -> bool:
    if category_filters is None:
        return True  # No filtering
    if not category_value:
        return False  # No category means exclude
    return category_value in category_filters
```

### 3.2 Multi-Modal Category Storage

Categories are stored in multiple locations for robustness:

```python
def extract_category(entity: Any) -> Optional[str]:
    # Check direct category property
    category = getattr(entity, 'category', None)
    if category:
        return category

    # Check attributes dictionary
    attributes = getattr(entity, 'attributes', None)
    if isinstance(attributes, dict):
        attr_category = attributes.get('category') or attributes.get('Category')
        if attr_category:
            return attr_category

    # Check properties dictionary
    properties = getattr(entity, 'properties', None)
    if isinstance(properties, dict):
        return properties.get('category')

    return None
```

### 3.3 Category Mapping System

**8 Business Categories:**
  EMS,MMC,SEP etc..

**Group ID to Category Mapping:**
```javascript
const GROUP_ID_MAPPINGS = {
  'user_guides_EMS': 'EMS',
  // ... other mappings
}
```

## 4. Search Algorithms & Performance

### 4.1 Supported Search Algorithms

1. **COMBINED_HYBRID_SEARCH_CROSS_ENCODER** (Default):
   - Neural reranking for maximum relevance
   - Provides reranker scores in output
   - Best for production use

2. **COMBINED_HYBRID_SEARCH_MMR**:
   - Maximum Marginal Relevance for diversity
   - Uses `diversity_factor` to control lambda: `mmr_lambda = 1.0 - diversity_factor`

3. **COMBINED_HYBRID_SEARCH_RRF**:
   - Reciprocal Rank Fusion for balanced retrieval
   - Fallback when other algorithms unavailable

### 4.2 Performance Optimizations

**Search Buffering:**
```python
# Adaptive search limits based on filtering
base_search_limit = max(10, (params.edge_count + params.node_count) * 3)
if category_filters and len(category_filters) > 0:
    search_limit = base_search_limit * 2  # Double buffer for filtering
```

**Caching Strategies:**
- Configuration hash caching for Graphiti instances
- Prompt template caching
- Validation result memoization
- Score preservation through entire pipeline

### 4.3 Score Extraction & Preservation

```python
# Cross-encoder scores (preferred)
if hasattr(search_results, 'edge_reranker_scores'):
    raw_scores['edge_reranker_scores'] = [float(score) for score in edge_scores_used]
    scoring_method = "cross_encoder_reranking"
else:
    # Position-based fallback
    edge_scores = [max(1.0 - (i * 0.05), 0.1) for i in range(len(edges))]
    raw_scores['edge_position_scores'] = edge_scores
```

## 5. Episode Strategy Requirements

### 5.1 Critical Design Constraints

**Group ID Consistency:**
- All entities from the same document/episode MUST share the same `group_id`
- Group ID format must follow: `user_guides_[DOCUMENT_ID]_[VERSION]`
- This is the foundation of category filtering and search functionality

**Category Propagation:**
- Categories flow from documents → episodes → entities via shared group IDs
- Every episode and entity must have a category for effective filtering


### 5.2 Data Quality Requirements

**Mandatory Properties:**
```python
# Every entity must have
{
  "name": str,           # Entity name/identifier
  "uuid": str,           # Unique identifier
  "group_id": str,       # Shared with episode/document
  "category": str,       # One of 8 business categories
  "name_embedding": List[float]  # 1024-dimensional OpenAI embedding
}
```



### 5.3 Critical Embedding Requirements

**⚠️ HIGH PRIORITY: Must use exact embedding specifications**

**Embedding Model:** OpenAI `text-embedding-3-small`
**Dimensions:** Exactly 1024 dimensions (non-negotiable)
**Storage:** `name_embedding` property on nodes
**Token Limit:** 8,191 tokens per text chunk

**Configuration Requirements:**
```bash
# Required environment variables
OPENAI_API_KEY=[your-openai-api-key]
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
OPENAI_EMBEDDING_DIMENSIONS=1024
```

**Implementation:**
```python
from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig

config = OpenAIEmbedderConfig(
    api_key=api_key,
    embedding_model="text-embedding-3-small",
    embedding_dim=1024
)

embedder = OpenAIEmbedder(config)
embedding = await embedder.create(text)

# Store in Neo4j
MERGE (n:Entity {uuid: $uuid})
SET n.name_embedding = $embedding_vector
```

**Compatibility Warning:**
Using different embedding models or dimensions will break semantic search functionality and cause data inconsistency.

### 5.4 Ingestion Process Flow

**Recommended Ingestion Sequence:**
1. **Document Processing**: Extract document metadata and assign group_id
2. **Episode Creation**: Split document into logical chunks with consistent group_id
3. **Entity Extraction**: Extract entities from episodes, inheriting group_id
4. **Embedding Generation**: Create 1024-dimensional embeddings using text-embedding-3-small
5. **Category Assignment**: Map group_id to business category using mapping system
6. **Relationship Creation**: Establish both episode-entity and entity-entity relationships
7. **Data Validation**: Verify group_id consistency, category assignment, and embedding dimensions

## 6. Integration Points

### 6.1 Graphiti Search Engine Integration

**Search Parameters:**
```python
@dataclass
class SearchParameters:
    edge_count: int           # Final edge limit after filtering
    node_count: int           # Final node limit after filtering
    search_type: str          # Algorithm selection
    diversity_factor: float   # MMR control
    category_filters: Optional[Set[str]]  # Post-search filtering
```

