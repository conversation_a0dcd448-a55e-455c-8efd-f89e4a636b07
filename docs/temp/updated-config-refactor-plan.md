# Unified Configuration Platform Refactor — Revised Execution Plan

## 0. Context & Objectives
The current configuration stack (see `docs/temp/settings-configuration-analysis.md`) delivers user-visible power but is unmanageable to extend. The earlier "Unified Config v2" proposal (`docs/temp/temp/augment-ssearch-refactor.md`) targets the right pain points yet creates high operational risk (per `docs/temp/temp/claude-refactor-settings.md`). This plan keeps the ambition—single coherent configuration surface—while adding safety rails, compatibility layers, and stronger operational support so a Claude code agent can execute incrementally.

**Primary Objectives**
1. Reduce active configuration formats to one versioned schema with explicit compatibility rules.
2. Simplify the UI state graph to a single typed store and eliminate redundant services.
3. Consolidate validation without degrading UX latency or losing security controls.
4. Remove client-side secrets safely by introducing a managed secret lifecycle.
5. Deliver the migration via progressive, reversible steps with automated telemetry and rollback levers.

## 1. Guiding Principles
- **Version Everything**: Every schema change carries a `major.minor` version. Consumers negotiate supported versions; breaking changes require dual-path support.
- **Progressive Change**: Dual read/write migrations with feature flags and canary rollouts precede removals. No all-or-nothing drops.
- **User Feedback First**: Keep in-panel validation for must-have feedback while delegating deeper checks server-side.
- **Secure by Construction**: Secrets live server-side; developers receive clear local overrides. Logs only contain whitelisted fields.
- **Operational Visibility**: Each phase emits structured telemetry (success, fallback, error types) and has scripted rollback instructions.

## 2. Target Architecture Snapshot
- **Configuration Control Plane** (new shared package `@kg/config-control`):
  - `schema/` JSON Schema + TypeScript types + Pydantic models generated from a single source.
  - `versions/` compatibility matrix & changelog.
  - `runtime/` helpers: `coerceConfig(input, {fromVersion, toVersion})`, `validateConfig(config, {level:'ui'|'server'})`.
- **UI Layer**: Single Zustand store `useConfigStore` with typed selectors; panels bind directly to store slices; validation uses shared lightweight rules (`level:'ui'`).
- **API Layer**: Express middleware `configGateway` that accepts `schemaVersion`-tagged payloads, auto-upgrades via `coerceConfig`, and logs metrics.
- **Python Layer**: FastAPI `search_config_router` consumes the same schema package (vendored wheel) and honors version negotiation.
- **Secrets Management**: Backend reads Azure credentials from environment or secret manager; UI exposes provider selection and diagnostics only. Local dev fallback via `.env` suffixed values.

## 3. Phased Execution Plan
Each phase lists scope, key workstreams (with deliverables + acceptance), and rollback/toggles. Claude agents can tackle workstreams independently once prerequisites are met.

### Phase 0 — Foundations & Governance (Week 0-1)
**Scope**: Establish shared artifacts, change governance, and telemetry scaffolding.

| Workstream | Tasks | Deliverables | Acceptance |
|------------|-------|--------------|------------|
| P0.1 Schema Governance | Define `@kg/config-control` package skeleton; author `SCHEMA_VERSIONING.md`; create CI rule blocking breaking changes without compatibility PR | Package repo or folder; doc with review checklist | CI fails on schema edits without version bump; lead sign-off |
| P0.2 Tooling | Generate TS/Pydantic types from schema; publish `yarn generate-config-types` | Codegen script; `README` usage | API & python compile using generated types |
| P0.3 Telemetry Contracts | Define `config_migration_state` event schema (fields: `schemaVersion`, `sourceVersion`, `coercionApplied`, `result`, `errorCode`) | `docs/telemetry/config-migration-events.md`; JSON schema | Dry-run logs validated via unit tests |
| P0.4 Feature Flags | Introduce `config.unified.enabled`, `config.dualread.enabled`, `config.azure.envOnly` flags using existing config flag module | Flag definitions + admin docs | Feature toggle API returns flags; unit tests green |

**Rollback**: No runtime changes yet—foundation only.

### Phase 1 — Compatibility Layer & Telemetry (Week 1-2)
**Scope**: Introduce version metadata, dual-read/write, and safety instrumentation while preserving behavior.

| Workstream | Tasks | Deliverables | Acceptance |
|------------|-------|--------------|------------|
| P1.1 Version Tagging | Modify UI store to annotate saved configs with `schemaVersion` (`'1.0'` initial); update API payload to forward version; Python logs version | UI diff; API request sample; FastAPI logs | `kg-visualizer-search-config` entries include version; API request snapshot verified |
| P1.2 Dual Storage** | Implement dual-read/write layer: persist to new `kg-config-v2` key while continuing legacy keys; migrations run only when flag on | `src/config/storage/migrateToV2.ts`; unit tests covering malformed JSON | Integration test shows both keys populated when flag on |
| P1.3 Config Gateway (Node) | Add Express middleware using `coerceConfig` to normalize payloads; emit telemetry events; fallback to legacy path when coercion fails | `360t-kg-api/middleware/configGateway.js`; tests for coercion success/failure | When flag off, behavior identical; when on, telemetry captures conversions |
| P1.4 Python Compatibility | Wrap FastAPI endpoint to detect version; use compatibility helpers; log metrics | `python-ai/src/services/config_gateway.py`; unit tests | Endpoint accepts both v1 + v2 schemas |

**Feature Flags**: `config.dualread.enabled` controls persistence; default OFF. `config.unified.enabled` remains OFF.

**Rollback**: Toggle flags OFF to revert to legacy runtime instantly.

### Phase 2 — UI Simplification & Validation (Week 2-4)
**Scope**: Simplify UI state and validation while maintaining user feedback.

| Workstream | Tasks | Deliverables | Acceptance |
|------------|-------|--------------|------------|
| P2.1 Single Config Store | Replace `settingsStore` + `searchConfigService` with `useConfigStore` typed by `@kg/config-control` types; remove redundant services | `360t-kg-ui/src/stores/configStore.ts`; migration notes | All components compile; unit tests for selectors |
| P2.2 Panel Refactor | Bind panels directly to store slices; remove transformation utilities; ensure controlled components use schema defaults | PRs for `AtlasRAGConfigPanel`, `GraphitiConfigPanel`; storybook snapshots | Manual QA shows parity; storybook updated |
| P2.3 UI Validation | Implement lightweight validation wrapper using `validateConfig(...,{level:'ui'})` for immediate feedback; fallback to server errors displayed inline | `useConfigValidation.ts`; tests for error formatting | Form prevents invalid submissions without server roundtrip |
| P2.4 Local Dev DX | Document `.env.local` overrides for Azure dev credentials; add banner when running with client-supplied secrets | `docs/dev/config-local-dev.md`; banner component | Dev run displays banner when fallback used |

**Feature Flags**: `config.unified.enabled` can be ON in development to exercise new flow; production remains OFF until Phase 3.

**Rollback**: Restore previous store via git revert; toggle flags OFF to shut new path.

### Phase 3 — Service Alignment & Security (Week 4-6)
**Scope**: Move secrets server-side with fallback, align API/Python to new schema, and prepare for turning on unified pipeline.

| Workstream | Tasks | Deliverables | Acceptance |
|------------|-------|--------------|------------|
| P3.1 Azure Secret Lifecycle | Integrate with secret manager or `.env` loader; add health check verifying required secrets; expose diagnostics endpoint; UI shows provider info only | `360t-kg-api/config/azureSecrets.ts`; FastAPI health update; docs | Health endpoint fails fast when secrets absent; UI diagnostics page loads |
| P3.2 Validation Consolidation | Ensure Node + Python share validation spec; unify error shape (`{code, path, message, schemaVersion}`); update UI error handling | `validation/errorspec.json`; tests; UI handler | Contract tests show identical error payloads across services |
| P3.3 Turn On Unified Path | Enable `config.unified.enabled` in staging; run contract/e2e suites; monitor telemetry for coercion failures | Flag rollout scripts; dashboard screenshot | 0 critical errors for 48h in staging |
| P3.4 Legacy API Safe Harbor | Maintain `graphitiSettings` handling behind `config.legacy.acceptGraphiti` flag; default ON until Phase 4. Telemetry counts residual usage | Metrics board; flag docs | Legacy usage ≤5% before removal gate |

**Rollback**: Toggle `config.unified.enabled` OFF; secrets fallback using documented UI path for dev; revert error handler if necessary.

### Phase 4 — Cutover & Cleanup (Week 6-7)
**Scope**: Remove legacy paths after data indicates safety.

| Workstream | Tasks | Deliverables | Acceptance |
|------------|-------|--------------|------------|
| P4.1 Remove Legacy Storage | Once telemetry shows <1% legacy reads for 2 weeks, delete legacy keys and migrator; run cleanup script for stale keys | Script `scripts/cleanup-legacy-config.js`; doc record | Script dry-run matches expectations; production cleanup logged |
| P4.2 Retire graphitiSettings | Flip `config.legacy.acceptGraphiti` OFF in staging then production; remove code path after 1 release | PR removing fallback; changelog | No legacy traffic for release cycle |
| P4.3 Documentation & Training | Update docs, diagrams, ADR; run brown-bag for team | `docs/decisions/ADR-unified-config-platform.md`; updated diagrams | Sign-off from leads |
| P4.4 Decommission Flags | Remove flags once unused >1 release; preserve metrics for historical analysis | PR removing flag code; release notes | No runtime dependency on flags |

**Rollback**: During release window, toggle legacy flags back ON; re-run migrator script if needed.

## 4. Testing & Observability Strategy
- **Testing Pyramid**
  - Unit: Generated validation helpers (AJV & Pydantic), config store reducers, migration utilities.
  - Integration: UI panels ↔ store (React Testing Library); Express middleware ↔ FastAPI using supertest & httpx; secrets fallback scenarios.
  - Contract: JSON schema snapshot tests run in CI; cross-service contract tests verify version negotiation.
  - E2E: Playwright flows for AtlasRAG + Graphiti; include scenario switching schema versions mid-session.
  - Load/Chaos: Simulate burst of config saves; intentionally drop env vars to ensure health checks catch issues.
- **Telemetry & Dashboards**
  - Dashboard sections: Migration success rate, coercion fallback count, legacy key reads, secret health status, validation error top paths.
  - Alerts: Slack/Email on `config_migration_state.result = 'failed'` or health check failing.

## 5. Rollback Playbook (per environment)
1. Toggle `config.unified.enabled` OFF → Runtime reverts to v1 payloads automatically.
2. Toggle `config.dualread.enabled` ON to resume dual-write if needed.
3. Re-run `scripts/restore-legacy-config.js` (created alongside cleanup) to repopulate legacy keys from latest unified snapshot.
4. Re-enable `config.legacy.acceptGraphiti` if external clients detected.
5. Redeploy prior package version (kept in artifact registry) if schema incompatibility persists.

## 6. Success Metrics
- 90% reduction in config-related validation code duplication across repos (LOC tracked per repo).
- 0 client-side secret storage; security review sign-off.
- <200 ms median config save latency with new validation flow.
- Error rate for config saves remains ≤ existing baseline throughout rollout.
- After cutover, telemetry shows ≤1% coercion fallback utilization.

## 7. Open Risks & Mitigations
| Risk | Mitigation | Owner |
|------|------------|-------|
| Schema version drift between services | Automated publish pipeline + integration tests gating release | Platform engineer |
| Azure secrets missing in env | Health checks + deployment preflight script | DevOps |
| Migration script edge cases | Fuzz tests on legacy data samples; beta testers export configs prior to rollout | Frontend lead |
| Telemetry outage hides issues | Local file logging fallback + scheduled log verification | Observability |

## 8. Implementation Checklist for Claude Agent
1. Bootstrap `@kg/config-control` package (Phase 0).
2. Add feature flag wiring & telemetry schema.
3. Implement Phase 1 dual-read/write + gateway middleware with flags OFF by default.
4. Progressively refactor UI store/panels (Phase 2) while monitoring telemetry.
5. Introduce backend secret handling and aligned validation messages (Phase 3).
6. Execute cutover once metrics hit thresholds (Phase 4) and archive legacy code.

This staged plan balances ambitious simplification with controlled, observable steps, ready for automated execution by Claude code agents.
