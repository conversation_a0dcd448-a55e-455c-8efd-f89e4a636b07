# Graphiti/Neo4j Embedding Configuration Analysis Report

**Date**: 2025-09-27
**Purpose**: Deep investigation into embedding types and configuration used by the Graphiti/Neo4j system
**Scope**: Analysis of current embedding setup for new ingestion tool compatibility

## Executive Summary

This investigation reveals that the Graphiti/Neo4j system uses **OpenAI's text-embedding-3-small model with 1024 dimensions** for all embedding operations. The system has a consistent embedding configuration across all nodes and uses Graphiti's internal embedder client with standardized dimensions.

## Key Findings

### 1. Current Embedding Configuration

**Primary Embedding Model**: `text-embedding-3-small` (OpenAI)
**Embedding Dimensions**: `1024` (consistent across all data)
**Storage Format**: `name_embedding` property on nodes
**Vector Index**: Single vector index on Document nodes (currently unused for primary data)

### 2. Graphiti Embedding Integration

**Graphiti Core Configuration**:
- Default model: `text-embedding-3-small`
- Fixed dimensions: `1024` (hardcoded in Graphiti core)
- Embedder client: `OpenAIEmbedder` from Graphiti Core
- Configuration passed through: `OpenAIEmbedderConfig`

**Environment Variables**:
```bash
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
OPENAI_EMBEDDING_DIMENSIONS=1024
OPENAI_API_KEY=[required]
```

### 3. Neo4j Vector Storage

**Current Schema**:
- **Property Name**: `name_embedding` (stored as float array)
- **Data Types**: Entity, Configuration, Feature, Module, Parameter, Product, Role, Workflow nodes
- **Vector Index**: `vector` index on Document nodes (type: VECTOR, provider: vector-2.0)
- **Dimensions**: Consistently 1024 across all 5,617 embedded entities

**Node Distribution**:
- Total nodes with embeddings: 5,617 out of 5,703 Entity nodes (98.5% coverage)
- Primary data sources: user_guides_RMTB_v1.0.0 (587), user_guides_TMMB_v1.0.0 (523)
- All embeddings consistently 1024 dimensions regardless of data source

### 4. Embedding Model Details

**OpenAI text-embedding-3-small Specifications**:
- Model ID: `text-embedding-3-small`
- Vector dimensions: `1024`
- Token limit: `8191` tokens per text chunk
- API endpoint: `https://api.openai.com/v1/embeddings`
- Encoding: `cl100k_base`

**Configuration in Code**:
```python
# From graphiti_search_engine.py
embedding_model=os.getenv("OPENAI_EMBEDDING_MODEL", "text-embedding-3-small")
embedding_dimensions=int(os.getenv("OPENAI_EMBEDDING_DIMENSIONS", "1024"))

# From Graphiti Core embedder/client.py
EMBEDDING_DIM = 1024  # Hardcoded constant

# OpenAIEmbedderConfig setup
embedder_config = OpenAIEmbedderConfig(
    api_key=self.config.openai_api_key,
    embedding_model=self.config.embedding_model,
    embedding_dim=self.config.embedding_dimensions
)
```

## Critical Requirements for New Ingestion Tool

### 1. Embedding Model Compatibility
**MUST USE**: `text-embedding-3-small` from OpenAI
**Dimensions**: Exactly `1024` dimensions
**API**: OpenAI embeddings API v1

### 2. Neo4j Storage Format
**Property Name**: `name_embedding`
**Data Type**: Float array of length 1024
**Node Labels**: Apply to Entity, Configuration, Feature, Module, Parameter, Product, Role, Workflow

### 3. Configuration Requirements
**Environment Variables** (required for compatibility):
```bash
OPENAI_API_KEY=[your-openai-api-key]
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
OPENAI_EMBEDDING_DIMENSIONS=1024
```

### 4. Graphiti Integration Points
**For Graphiti compatibility**, new embeddings must:
- Use the same OpenAIEmbedder client interface
- Pass embedding_dim=1024 to the embedder config
- Store results in `name_embedding` property
- Handle text chunking within OpenAI's 8191 token limit

## Technical Implementation Details

### Embedding Generation Process
1. Text preprocessing and chunking (max 8191 tokens)
2. API call to OpenAI embeddings endpoint
3. Vector truncation to 1024 dimensions (handled by Graphiti)
4. Storage in Neo4j as `name_embedding` property
5. Vector index utilization for similarity search

### Error Handling Requirements
- OpenAI API rate limiting (3,500 RPM, 350K TPM)
- Token limit validation before API calls
- Fallback for API failures
- Dimension validation (must be exactly 1024)

### Performance Considerations
- **OpenAI Rate Limits**: 3,500 requests per minute, 350K tokens per minute
- **Batch Processing**: Recommended to batch multiple texts in single API calls
- **Memory Usage**: 1024 dimensions × 4 bytes = ~4KB per embedding
- **Index Performance**: Neo4j vector-2.0 index supports fast similarity search

## Data Migration Considerations

### For Existing Data
- All existing embeddings use 1024 dimensions
- No migration needed for new tool if using same specifications
- Mixed dimensions would break search functionality

### For New Data Sources
- Must use identical embedding model and dimensions
- Consistent property naming (`name_embedding`)
- Same text preprocessing pipeline

## Recommended Implementation Approach

### 1. Configuration Setup
```python
# Required configuration for compatibility
embedding_config = {
    "model": "text-embedding-3-small",
    "dimensions": 1024,
    "api_key": os.getenv("OPENAI_API_KEY"),
    "property_name": "name_embedding"
}
```

### 2. Embedding Generation
```python
from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig

config = OpenAIEmbedderConfig(
    api_key=api_key,
    embedding_model="text-embedding-3-small",
    embedding_dim=1024
)

embedder = OpenAIEmbedder(config)
embedding = await embedder.create(text)
```

### 3. Neo4j Storage
```cypher
// Create or update node with embedding
MERGE (n:Entity {uuid: $uuid})
SET n.name_embedding = $embedding_vector
```

## Testing and Validation

### Compatibility Tests
1. **Dimension Test**: Verify all embeddings are exactly 1024 dimensions
2. **Similarity Search**: Test vector similarity with existing data
3. **API Integration**: Validate OpenAI API compatibility
4. **Storage Test**: Confirm Neo4j vector index usage

### Quality Assurance
- Compare embedding quality with existing data
- Validate search result relevance
- Performance benchmarking against existing system

## Risk Assessment

### High Risk Factors
- **Model Mismatch**: Using different embedding model breaks semantic search
- **Dimension Mismatch**: Different dimensions prevent vector operations
- **Property Naming**: Wrong property names prevent index utilization

### Mitigation Strategies
- Use identical configuration from existing system
- Implement validation for embedding dimensions
- Test with existing data before production deployment

## Next Steps

### Immediate Actions
1. Configure new ingestion tool with `text-embedding-3-small` and 1024 dimensions
2. Implement OpenAI API integration with proper rate limiting
3. Test embedding generation with sample data
4. Validate Neo4j storage and vector index usage

### Future Considerations
- Monitor OpenAI model updates and changes
- Plan for potential model migration if needed
- Consider embedding caching for performance optimization
- Evaluate cost implications of embedding generation

## Conclusion

The Graphiti/Neo4j system has a well-defined and consistent embedding configuration using OpenAI's text-embedding-3-small model with 1024 dimensions. Any new ingestion tool **must** use identical specifications to maintain compatibility with the existing knowledge graph and search functionality.

**Critical Success Factors**:
- Exact embedding model match: `text-embedding-3-small`
- Exact dimension match: `1024`
- Consistent property naming: `name_embedding`
- OpenAI API integration with proper rate limiting

Following these specifications will ensure seamless integration with the existing Graphiti search system and maintain the quality of semantic search capabilities.

---

**Investigation Completed**: 2025-09-27
**Next Review**: Required if embedding model changes are planned
**Dependencies**: OpenAI API access, Neo4j database access