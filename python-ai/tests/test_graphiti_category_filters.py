"""Unit tests for category filter handling in Graphiti search engine."""

import types
import pytest

from src.search.graphiti_search_engine import create_search_engine, SearchParameters


class DummyGraphiti:
    """Minimal Graphiti stub returning predetermined edges and nodes."""

    def __init__(self, search_results):
        self._search_results = search_results

    async def _search(self, query, config):
        return self._search_results


class DummySearchResults:
    def __init__(self):
        self.edges = [
            types.SimpleNamespace(fact='Edge EMS', category='EMS'),
            types.SimpleNamespace(fact='Edge Trading', category='TRADING'),
        ]
        self.nodes = [
            types.SimpleNamespace(summary='Node EMS summary', category='EMS'),
            types.SimpleNamespace(summary='Node Trading summary', category='TRADING'),
        ]
        self.edge_reranker_scores = []
        self.node_reranker_scores = []


@pytest.mark.asyncio
async def test_search_context_respects_category_filters():
    engine = create_search_engine()

    dummy_results = DummySearchResults()
    dummy_graphiti = DummyGraphiti(dummy_results)
    engine._graphiti_instance = dummy_graphiti
    engine._build_graphiti_instance = lambda: dummy_graphiti

    params = SearchParameters(
        edge_count=2,
        node_count=2,
        search_type='COMBINED_HYBRID_SEARCH_CROSS_ENCODER',
        diversity_factor=0.3,
        temperature=0.3,
        timeout=120,
        enable_query_enhancement=False,
        conversation_history=[],
        category_filters={'EMS'}
    )

    context, _, citations, metadata = await engine._search_context('test', params)

    assert 'Edge EMS' in context
    assert 'Node EMS summary' in context
    assert 'Trading' not in context
    assert all(item.get('category') == 'EMS' for item in citations)
    assert metadata['filters']['category'] == ['EMS']
    assert metadata['returned']['edges'] == 1
    assert metadata['returned']['nodes'] == 1


@pytest.mark.asyncio
async def test_search_context_handles_null_filters():
    engine = create_search_engine()

    dummy_results = DummySearchResults()
    dummy_graphiti = DummyGraphiti(dummy_results)
    engine._graphiti_instance = dummy_graphiti
    engine._build_graphiti_instance = lambda: dummy_graphiti

    params = SearchParameters(
        edge_count=2,
        node_count=2,
        search_type='COMBINED_HYBRID_SEARCH_CROSS_ENCODER',
        diversity_factor=0.3,
        temperature=0.3,
        timeout=120,
        enable_query_enhancement=False,
        conversation_history=[],
        category_filters=None
    )

    context, _, citations, metadata = await engine._search_context('test', params)

    assert 'Trading' in context
    assert len(citations) >= 4
    assert metadata['filters']['category'] is None
